"use client"

import Link from "next/link"
import { useState, useEffect } from "react"
import { Search, Menu, Film, Home, Grid3X3 } from "lucide-react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { Badge } from "@/components/ui/badge"
import { useRouter, useSearchParams } from "next/navigation"

interface Category {
  name: string
  slug: string
  count?: number
}

const categories: Category[] = [
  { name: "全部", slug: "all", count: 0 },
  { name: "重生", slug: "rebirth", count: 12 },
  { name: "都市", slug: "urban", count: 8 },
  { name: "古装", slug: "historical", count: 15 },
  { name: "玄幻", slug: "fantasy", count: 6 },
  { name: "言情", slug: "romance", count: 20 },
]

export function SiteHeader({ categories: propCategories }: { categories?: Category[] }) {
  const [searchQuery, setSearchQuery] = useState("")
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const router = useRouter()
  const searchParams = useSearchParams()

  const displayCategories = propCategories || categories

  useEffect(() => {
    const q = searchParams.get('q')
    if (q) {
      setSearchQuery(q)
    }
  }, [searchParams])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      router.push(`/?q=${encodeURIComponent(searchQuery.trim())}`)
    } else {
      router.push('/')
    }
  }

  const handleCategoryClick = (slug: string) => {
    if (slug === 'all') {
      router.push('/')
    } else {
      router.push(`/category/${slug}`)
    }
    setIsMobileMenuOpen(false)
  }

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <Film className="h-6 w-6 text-primary" />
            <span className="text-xl font-bold bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
              短剧星球
            </span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-1">
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => handleCategoryClick('all')}
              className="flex items-center space-x-1"
            >
              <Home className="h-4 w-4" />
              <span>首页</span>
            </Button>
            {displayCategories.slice(1).map((category) => (
              <Button
                key={category.slug}
                variant="ghost"
                size="sm"
                onClick={() => handleCategoryClick(category.slug)}
                className="flex items-center space-x-1"
              >
                <span>{category.name}</span>
                {category.count && (
                  <Badge variant="secondary" className="text-xs">
                    {category.count}
                  </Badge>
                )}
              </Button>
            ))}
          </nav>

          {/* Search Bar */}
          <form onSubmit={handleSearch} className="flex-1 max-w-md mx-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="搜索短剧名称..."
                className="pl-9 pr-4 rounded-full"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </form>

          {/* Mobile Menu */}
          <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
            <SheetTrigger asChild className="md:hidden">
              <Button variant="ghost" size="sm">
                <Menu className="h-5 w-5" />
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[300px] sm:w-[400px]">
              <div className="flex flex-col space-y-4 mt-6">
                <div className="flex items-center space-x-2 mb-4">
                  <Film className="h-5 w-5 text-primary" />
                  <span className="text-lg font-semibold">分类导航</span>
                </div>
                {displayCategories.map((category) => (
                  <Button
                    key={category.slug}
                    variant="ghost"
                    className="justify-start"
                    onClick={() => handleCategoryClick(category.slug)}
                  >
                    <Grid3X3 className="h-4 w-4 mr-2" />
                    <span>{category.name}</span>
                    {category.count && (
                      <Badge variant="secondary" className="ml-auto text-xs">
                        {category.count}
                      </Badge>
                    )}
                  </Button>
                ))}
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  )
}
