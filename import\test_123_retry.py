#!/usr/bin/env python3
"""
测试123网盘重试机制
"""

import os
import sys
import time
from pathlib import Path

# 添加网盘模块路径
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.append(str(project_root / "123"))

try:
    from upload_share import YunPan123Client
    print("✅ 123网盘模块导入成功")
except ImportError as e:
    print(f"❌ 123网盘模块导入失败: {e}")
    sys.exit(1)

def test_upload_with_retry():
    """测试带重试的上传"""
    try:
        config_path = str(project_root / "123" / "config.json")
        client = YunPan123Client(config_path)
        
        # 创建一个小测试文件
        import tempfile
        temp_dir = tempfile.gettempdir()
        test_file = os.path.join(temp_dir, "test_retry.txt")
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("测试123网盘重试机制\n" * 100)
        
        print(f"📁 创建测试文件: {test_file}")
        
        max_retries = 3
        retry_delay = 2
        
        for attempt in range(max_retries):
            try:
                print(f"📤 开始上传 (第{attempt + 1}次尝试)")
                
                result = client.upload_and_share(
                    file_path=test_file,
                    directory_path="/网站/短剧/2025",
                    share_name="测试重试机制"
                )
                
                print(f"✅ 上传成功!")
                print(f"🔗 分享链接: {result['shareUrl']}")
                print(f"🔑 提取码: {result.get('extractCode', '无')}")
                
                # 清理测试文件
                os.remove(test_file)
                return result
                
            except Exception as e:
                error_msg = str(e)
                print(f"❌ 上传失败: {error_msg}")
                
                if "文件正在校验中" in error_msg and attempt < max_retries - 1:
                    print(f"⏳ 文件正在校验中，等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                    continue
                else:
                    print(f"❌ 重试失败，放弃上传")
                    # 清理测试文件
                    if os.path.exists(test_file):
                        os.remove(test_file)
                    raise e
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    print("=== 测试123网盘重试机制 ===")
    test_upload_with_retry()
