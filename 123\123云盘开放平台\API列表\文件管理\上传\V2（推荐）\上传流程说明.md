# 💡上传流程说明

## 分片上传
1. **创建文件**
    1. 调用创建文件接口，接口返回的`reuse`为true时，表示秒传成功，上传结束。
    2. 非秒传情况将会返回预上传ID`preuploadID`与分片大小`sliceSize`，请将文件根据分片大小切分。
    3. 非秒传情况下返回`servers`为后续上传文件的对应域名（<font style="color:#DF2A3F;">重要</font>），多个任选其一。
2. **上传分片**
    1. 该步骤准备工作，按照`sliceSize`将文件切分，并计算每个分片的MD5。
    2. 调用上传分片接口，传入对应参数，注意此步骤 `<font style="color:#DF2A3F;">Content-Type: multipart/form-data</font>`**。**
3. **上传完毕**
    1. 调用上传完毕接口，若接口返回的`completed`为 ture 且`fileID`不为0时，上传完成。
    2. 若接口返回的`completed`为 false 时，则需间隔1秒继续轮询此接口，获取上传最终结果。

### 上传文件时序图
![1749200624056-264f1c08-43a1-4199-9135-e068fcfe3fe5.png](./img/nk0kBDuKhbO9fcqz/1749200624056-264f1c08-43a1-4199-9135-e068fcfe3fe5-382677.png)

## 单步上传
1. **获取上传域名**
    1. 调用该接口，在接口返回中你将获得多个上传域名，后续上传任务需要使用。
2. **发起上传**
    1. 计算文件MD5；
    2. 使用获取到的上传域名发起上传；
    3. 注意此步骤 `<font style="color:#DF2A3F;">Content-Type: multipart/form-data</font>`**。**



> 更新: 2025-06-23 09:13:41  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/xogi45g7okqk7svr>