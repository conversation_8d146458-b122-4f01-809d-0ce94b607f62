# qBittorrent种子与短剧数据库匹配系统

这个系统可以自动读取qBittorrent中的种子信息，与Flask数据库中的短剧进行匹配，并为匹配的短剧添加网盘分享链接。

## 系统功能

✅ **自动连接qBittorrent** - 通过API读取种子信息  
✅ **智能名称匹配** - 从种子文件名提取短剧名称并与数据库匹配  
✅ **网盘集成** - 支持123云盘和夸克网盘分享链接  
✅ **自动链接添加** - 为匹配的短剧自动添加播放链接  
✅ **分类过滤** - 可指定处理特定分类的种子  

## 配置信息

### qBittorrent连接配置
- **地址**: http://192.168.1.7:49850/
- **用户名**: admin  
- **密码**: 507877550@lihao
- **目标分类**: agsvpt

### 网盘配置
- **123云盘**: 需要在 `../123/config.json` 中配置API密钥
- **夸克网盘**: 支持但当前未启用

## 使用方法

### 1. 查看qBittorrent分类
```bash
python qb_drama_matcher.py --list-categories
```

### 2. 模拟运行（不实际添加链接）
```bash
python qb_drama_matcher.py --category agsvpt --dry-run
```

### 3. 正式运行
```bash
python qb_drama_matcher.py --category agsvpt
```

### 4. 使用自定义分类
```bash
python qb_drama_matcher.py --category 其他分类名
```

## 工作流程

1. **连接qBittorrent** - 登录并获取指定分类的种子列表
2. **解析种子名称** - 从种子文件名提取短剧名称
   - 示例: `侯门主母温黎传.Hou.Men.Zhu.Mu.Wen.Li.Chuan.2025.S01.720p.WEB-DL.H265.AAC-GodDramas`
   - 提取: `侯门主母温黎传`
3. **数据库匹配** - 在Flask数据库中查找匹配的短剧
4. **创建分享链接** - 调用网盘API创建分享链接
5. **更新数据库** - 将分享链接添加到短剧的播放链接中

## 匹配逻辑

### 种子名称清理
- 去除年份信息 (2024, 2025等)
- 去除季数标识 (S01, S1等)  
- 去除分辨率信息 (720p, 1080p等)
- 去除编码格式 (H264, H265等)
- 去除制作组标识
- 提取中文名称部分

### 数据库匹配
1. **精确匹配** - 直接匹配短剧标题
2. **模糊匹配** - 去除空格和标点后进行包含匹配

## 输出示例

```
开始处理qBittorrent种子 (分类: agsvpt)
============================================================
获取到 2 个种子 (分类: agsvpt)

处理种子: 侯门主母温黎传.Hou.Men.Zhu.Mu.Wen.Li.Chuan.2025.S01.720p.WEB-DL.H265.AAC-GodDramas
状态: stalledUP
路径: /downloads/agsvpt/侯门主母温黎传.Hou.Men.Zhu.Mu.Wen.Li.Chuan.2025.S01.720p.WEB-DL.H265.AAC-GodDramas
提取的短剧名: 侯门主母温黎传
找到匹配短剧: 侯门主母温黎传 (ID: 17)
正在为 '侯门主母温黎传' 创建123云盘分享链接...
文件不存在，跳过为 '侯门主母温黎传' 创建分享链接: /downloads/agsvpt/侯门主母温黎传.Hou.Men.Zhu.Mu.Wen.Li.Chuan.2025.S01.720p.WEB-DL.H265.AAC-GodDramas
未能创建分享链接

处理种子: 开道图密.Kai.Dao.Tu.Mi.S01.2025.720p.WEB-DL.AVC.AAC.2.0-COMBINE@AGSVWEB
状态: stalledUP
路径: /downloads/agsvpt/开道图密.Kai.Dao.Tu.Mi.S01.2025.720p.WEB-DL.AVC.AAC.2.0-COMBINE@AGSVWEB
提取的短剧名: 开道图密
找到匹配短剧: 开道图密 (ID: 11)
正在为 '开道图密' 创建123云盘分享链接...
文件不存在，跳过为 '开道图密' 创建分享链接: /downloads/agsvpt/开道图密.Kai.Dao.Tu.Mi.S01.2025.720p.WEB-DL.AVC.AAC.2.0-COMBINE@AGSVWEB
未能创建分享链接

============================================================
处理完成! 共处理 2 个种子，匹配 2 个短剧
```

## 数据库集成

### 短剧表 (Play)
- 主要字段: `id`, `title`, `description`, `episodes`, `views`
- 关系: 与 `PlayLink` 一对多关系

### 播放链接表 (PlayLink)  
- 主要字段: `play_id`, `platform`, `link`, `code`
- 平台类型: `123pan`, `quark`
- 唯一约束: 同一短剧的同一平台只能有一个链接

## 注意事项

1. **种子状态** - 只处理已完成的种子 (uploading, stalledUP, pausedUP等状态)
2. **重复处理** - 如果短剧已有该平台的链接，会跳过不重复添加
3. **网盘配置** - 需要正确配置123云盘的API密钥才能创建真实分享链接
4. **文件存在性** - 只有当种子文件真实存在时才会上传并创建分享链接，不存在则跳过
5. **真实上传** - 系统不会创建模拟分享链接，只处理实际存在的文件

## 故障排除

### 常见问题

1. **qBittorrent连接失败**
   - 检查IP地址、端口、用户名密码是否正确
   - 确认qBittorrent的Web UI已启用

2. **没有找到匹配的短剧**
   - 检查数据库中是否存在对应的短剧记录
   - 可以手动添加测试短剧验证系统

3. **网盘分享链接创建失败**
   - 检查123云盘配置文件是否正确
   - 验证API密钥是否有效

### 测试用例

系统已创建测试短剧 "侯门主母温黎传" (ID: 17) 用于验证匹配功能。

## 扩展功能

未来可以添加：
- 定时自动运行
- 更多网盘平台支持
- 批量处理历史种子
- Web管理界面
- 邮件/微信通知