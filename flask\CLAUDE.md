# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Flask full-stack short-play streaming platform with PWA (Progressive Web App) capabilities designed for production deployment. The application uses MariaDB as the primary database with modern frontend technologies for a native app-like experience.

**Tech Stack:**
- Backend: Flask 3.0 + SQLAlchemy 2.0 (modern typed ORM) + Flask-Migrate
- Frontend: Alpine.js 3.x + Tailwind CSS + Jinja2 templates
- Database: MariaDB (production-ready)
- PWA: Service Worker + Web App Manifest + offline caching

## Essential Commands

### Production Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Initialize database with sample data
python run.py init

# Run production server
python run.py
# or explicitly: python run.py run
```

### Database Management
```bash
python run.py init    # Initialize DB + seed data
python run.py seed    # Add sample data only
python run.py reset   # Reset database (dangerous)
```

### Data Import
```bash
# Import data from AGSVPT crawler
cd ../import
python agsvpt_crawler.py 50    # Import 50 items with random views (100-10000)
```

## Architecture & Design Patterns

### Production-Only Configuration
The application is configured exclusively for production deployment:
- **Database**: MariaDB only (no SQLite support)
- **Config**: Single production configuration class
- **Debug**: Always disabled for production security
- **Environment**: Hardcoded to production mode

### Database Architecture (SQLAlchemy 2.0)
- **Production Database**: MariaDB with PyMySQL driver
- **Connection**: mysql+pymysql://sql23721_duanji:507877550%40lihao@************/sql23721_duanji?charset=utf8mb4
- **Modern typed ORM**: Uses `Mapped` types and `mapped_column()` for full type safety
- **Relationship pattern**: Bidirectional relationships with `back_populates`
- **Model serialization**: All models have `to_dict()` methods for JSON API responses
- **Multi-table associations**: `play_categories` table for many-to-many Play↔Category relationships

**Core Models:**
- `Category`: Hierarchical content organization with slugs and ordering
- `Play`: Main content model with ratings, views, episodes, status
- `PlayLink`: Multi-platform link management (123pan, Quark drive)

### Blueprint Architecture
- `routes/main.py`: Page routes (index, category, search, play detail, offline)
- `routes/api.py`: RESTful API endpoints with unified JSON response format
- All API responses use `create_response(success, data, message)` helper

### Frontend Architecture
- **Alpine.js Components**: Each page has dedicated Alpine.js data functions
- **Global State**: `appData()` function in base.html manages app-wide state
- **PWA Integration**: Service worker registration, install prompts, offline handling
- **Mobile-First**: Touch-optimized UI with safe area support and gesture handling

### PWA Implementation
- **Service Worker**: `/static/sw.js` with cache-first static assets, network-first API strategy
- **Offline Support**: Dedicated offline page, network status monitoring
- **Install Experience**: Smart install prompts, app shortcuts, standalone mode
- **Caching Strategy**: API responses cached for offline access, background sync

## Important Development Patterns

### Database Model Convention
All models must implement:
```python
def to_dict(self):
    """Convert model to dictionary for JSON serialization"""
    return {
        'id': self.id,
        # ... other fields
    }
```

### API Response Format
All API endpoints use unified response format:
```python
from routes.api import create_response
return create_response(True, data, "Success message")
```

### Template Data Serialization
When passing SQLAlchemy objects to templates for JavaScript consumption:
```python
# In route handler
plays_data = [play.to_dict() for play in plays.items]
return render_template('template.html', plays_data=plays_data)

# In template
plays: {{ plays_data | tojson }},
```

### PWA-Ready Components
- All interactive elements use `touch-target` class for 44px minimum touch areas
- Add `touch-manipulation` for optimal touch response
- Use `safe-area-inset` class for notch/dynamic island compatibility

## Configuration Management

The application uses production-only configuration:
- Production: MariaDB database, debug mode disabled
- Key settings: `SECRET_KEY`, `SQLALCHEMY_DATABASE_URI`, `PLAYS_PER_PAGE`
- No development or testing configurations available

## Data Import Strategy

### AGSVPT Crawler Integration
- Located in `../import/agsvpt_crawler.py`
- Automatically generates random views (100-10000) for SEO optimization
- Downloads and stores cover images locally with timestamp naming
- Creates categories automatically if they don't exist
- Does NOT create PlayLink records (reserved for manual network drive links)

### SEO Optimization
- Random view counts make content appear popular and established
- Local image storage improves loading performance
- Proper category organization for search engine indexing

## Mobile & PWA Considerations

- **Viewport**: `user-scalable=no` prevents zoom issues on mobile
- **Service Worker**: Handles offline scenarios gracefully with toast notifications
- **Touch Optimization**: All buttons are minimum 44px, menus have swipe gestures
- **Network Awareness**: App displays online/offline status and handles connectivity changes
- **Install Flow**: PWA install prompt appears after 3 seconds for eligible users

## Deployment Notes

The application is production-ready:
- Production: Use `python run.py` for direct deployment
- Database: MariaDB configured and optimized for production loads
- Security: Debug mode disabled, secure secret key management
- Performance: Optimized queries and caching strategies implemented

## Production Database Connection

**Database Details:**
- Host: ************
- Database: sql23721_duanji
- Username: sql23721_duanji
- Password: 507877550@lihao
- Charset: utf8mb4