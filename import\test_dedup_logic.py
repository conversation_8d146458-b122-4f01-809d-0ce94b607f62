#!/usr/bin/env python3
"""
测试去重逻辑
"""

import pymysql
from datetime import datetime

# 数据库配置
db_config = {
    'host': '************',
    'user': 'sql23721_duanji',
    'password': '507877550@lihao',
    'database': 'sql23721_duanji',
    'charset': 'utf8mb4'
}

def test_dedup_logic():
    """测试去重逻辑"""
    print("=== 测试去重逻辑 ===")
    
    try:
        connection = pymysql.connect(**db_config)
        
        # 查找"开到荼靡"的play_id
        with connection.cursor() as cursor:
            cursor.execute("SELECT id, title FROM play WHERE title = '开到荼靡'")
            play = cursor.fetchone()
            
            if not play:
                print("❌ 未找到'开到荼靡'短剧")
                return
            
            play_id = play[0]
            print(f"📺 找到短剧: {play[1]} (ID: {play_id})")
            
            # 查看现有链接
            cursor.execute("""
                SELECT platform, link, code FROM play_link 
                WHERE play_id = %s AND link IS NOT NULL AND link != ''
            """, (play_id,))
            existing_links = cursor.fetchall()
            
            print(f"📋 现有链接数量: {len(existing_links)}")
            for link in existing_links:
                platform, url, code = link
                print(f"  - {platform}: {url} (提取码: {code or '无'})")
            
            # 模拟去重逻辑
            platforms = [link[0] for link in existing_links]
            
            # 可用的网盘客户端
            available_clients = ["quark", "123pan"]  # 假设两个都可用
            
            # 检查缺失的平台
            missing_platforms = [client for client in available_clients if client not in platforms]
            
            print(f"🔍 可用网盘: {available_clients}")
            print(f"📋 已有平台: {platforms}")
            print(f"❓ 缺失平台: {missing_platforms}")
            
            if not missing_platforms:
                print("✅ 所有网盘都有链接，应该跳过")
            else:
                print(f"📤 需要补充{', '.join(missing_platforms)}网盘链接")
                
        connection.close()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def clear_test_links():
    """清理测试链接（可选）"""
    print("\n=== 清理测试链接 ===")
    
    try:
        connection = pymysql.connect(**db_config)
        
        with connection.cursor() as cursor:
            # 查找"开到荼靡"的play_id
            cursor.execute("SELECT id FROM play WHERE title = '开到荼靡'")
            play = cursor.fetchone()
            
            if play:
                play_id = play[0]
                
                # 删除所有链接（用于测试）
                cursor.execute("DELETE FROM play_link WHERE play_id = %s", (play_id,))
                deleted_count = cursor.rowcount
                
                connection.commit()
                print(f"🗑️  删除了 {deleted_count} 条链接记录")
            else:
                print("❌ 未找到'开到荼靡'短剧")
                
        connection.close()
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")

if __name__ == "__main__":
    test_dedup_logic()
    
    # 如果需要清理测试数据，取消下面的注释
    # clear_test_links()
