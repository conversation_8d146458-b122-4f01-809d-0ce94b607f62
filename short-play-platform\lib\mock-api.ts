// Mock API 服务器示例
// 这个文件展示了后端API应该如何实现的示例代码
// 实际后端可以参考这个结构用任何语言实现（Node.js、Python、Java等）

import { NextRequest, NextResponse } from 'next/server'
import { 
  ShortPlay, 
  Category, 
  ApiResponse,
  GetPlaysParams,
  SearchPlaysParams,
  PlayStatistics 
} from './api-types'

// ============= 模拟数据 =============

const mockCategories: Category[] = [
  { id: 1, name: "全部", slug: "all", count: 100, order: 0 },
  { id: 2, name: "重生", slug: "rebirth", count: 12, order: 1 },
  { id: 3, name: "都市", slug: "urban", count: 8, order: 2 },
  { id: 4, name: "古装", slug: "historical", count: 15, order: 3 },
  { id: 5, name: "玄幻", slug: "fantasy", count: 6, order: 4 },
  { id: 6, name: "言情", slug: "romance", count: 20, order: 5 },
]

const mockPlays: ShortPlay[] = [
  {
    id: 1,
    title: "重生之我在霸总文里当保姆",
    posterUrl: "/placeholder.svg?width=400&height=600",
    description: "一场意外，她重生到一本霸总小说里，成了男主角家的保姆，且看她如何逆袭，俘获霸总的心。",
    category: mockCategories[1],
    pan123: { link: "https://www.123pan.com/s/abc-123", code: "a1b2", isActive: true, updatedAt: "2024-07-28T10:00:00Z" },
    quark: { link: "https://pan.quark.cn/s/xyz-789", code: "x7y8", isActive: true, updatedAt: "2024-07-28T10:00:00Z" },
    rating: 4.5,
    views: 12580,
    updatedAt: "2024-07-28T10:00:00Z",
    createdAt: "2024-07-20T10:00:00Z",
    tags: ["重生", "霸总", "甜宠"],
    status: "active",
    episodes: 80,
    duration: 120,
  },
  {
    id: 2,
    title: "我的女友是机器人",
    posterUrl: "/placeholder.svg?width=400&height=600",
    description: "一个普通的程序员，意外收到了一个来自未来的机器人女友，生活从此发生了翻天覆地的变化。",
    category: mockCategories[2],
    pan123: { link: "https://www.123pan.com/s/def-456", code: "", isActive: true, updatedAt: "2024-07-30T10:00:00Z" },
    quark: { link: "https://pan.quark.cn/s/uvw-456", code: "u4v5", isActive: true, updatedAt: "2024-07-30T10:00:00Z" },
    rating: 4.2,
    views: 8920,
    updatedAt: "2024-07-30T10:00:00Z",
    createdAt: "2024-07-25T10:00:00Z",
    tags: ["科幻", "都市", "机器人"],
    status: "active",
    episodes: 60,
    duration: 90,
  },
  // 可以添加更多模拟数据...
]

// ============= 工具函数 =============

function createSuccessResponse<T>(data: T, pagination?: any): ApiResponse<T> {
  return {
    success: true,
    data,
    pagination,
  }
}

function createErrorResponse(code: string, message: string): ApiResponse<never> {
  return {
    success: false,
    error: { code, message },
  }
}

function parseQueryParams(request: NextRequest) {
  const url = new URL(request.url)
  const params = Object.fromEntries(url.searchParams.entries())
  
  // 转换数字类型的参数
  Object.keys(params).forEach(key => {
    if (['page', 'pageSize', 'limit', 'minRating'].includes(key)) {
      params[key] = parseInt(params[key]) || undefined
    }
    if (key === 'tags' || key === 'platforms') {
      params[key] = params[key].split(',').filter(Boolean)
    }
  })
  
  return params
}

function applyFilters(plays: ShortPlay[], params: any): ShortPlay[] {
  let filtered = [...plays]
  
  // 分类筛选
  if (params.category && params.category !== 'all') {
    filtered = filtered.filter(play => play.category.slug === params.category)
  }
  
  // 搜索筛选
  if (params.search || params.query) {
    const query = (params.search || params.query).toLowerCase()
    filtered = filtered.filter(play => 
      play.title.toLowerCase().includes(query) || 
      play.description.toLowerCase().includes(query) ||
      play.tags?.some(tag => tag.toLowerCase().includes(query))
    )
  }
  
  // 标签筛选
  if (params.tags && Array.isArray(params.tags)) {
    filtered = filtered.filter(play => 
      params.tags.some((tag: string) => play.tags?.includes(tag))
    )
  }
  
  // 评分筛选
  if (params.minRating) {
    filtered = filtered.filter(play => 
      play.rating && play.rating >= params.minRating
    )
  }
  
  // 平台筛选
  if (params.platforms && Array.isArray(params.platforms)) {
    filtered = filtered.filter(play => {
      return params.platforms.some((platform: string) => {
        if (platform === '123') return play.pan123.isActive
        if (platform === 'quark') return play.quark.isActive
        return false
      })
    })
  }
  
  // 提取码筛选
  if (params.hasCode !== undefined) {
    const hasCode = params.hasCode === 'true' || params.hasCode === true
    filtered = filtered.filter(play => {
      const hasAnyCode = !!(play.pan123.code || play.quark.code)
      return hasCode ? hasAnyCode : !hasAnyCode
    })
  }
  
  return filtered
}

function applySorting(plays: ShortPlay[], sortBy: string = 'latest', sortOrder: string = 'desc'): ShortPlay[] {
  const sorted = [...plays]
  
  sorted.sort((a, b) => {
    let comparison = 0
    
    switch (sortBy) {
      case 'popular':
        comparison = (b.views || 0) - (a.views || 0)
        break
      case 'rating':
        comparison = (b.rating || 0) - (a.rating || 0)
        break
      case 'latest':
      default:
        comparison = new Date(b.updatedAt || b.createdAt).getTime() - 
                    new Date(a.updatedAt || a.createdAt).getTime()
        break
    }
    
    return sortOrder === 'asc' ? -comparison : comparison
  })
  
  return sorted
}

function applyPagination<T>(items: T[], page: number = 1, pageSize: number = 20) {
  const offset = (page - 1) * pageSize
  const paginatedItems = items.slice(offset, offset + pageSize)
  
  const pagination = {
    page,
    pageSize,
    total: items.length,
    totalPages: Math.ceil(items.length / pageSize),
  }
  
  return { items: paginatedItems, pagination }
}

// ============= API处理函数 =============

export class MockApiHandlers {
  
  // 获取短剧列表
  static async getPlays(request: NextRequest): Promise<NextResponse> {
    try {
      const params = parseQueryParams(request)
      const { page = 1, pageSize = 20, sortBy = 'latest', sortOrder = 'desc' } = params
      
      let filtered = applyFilters(mockPlays, params)
      filtered = applySorting(filtered, sortBy, sortOrder)
      
      const { items, pagination } = applyPagination(filtered, page, pageSize)
      
      return NextResponse.json(createSuccessResponse(items, pagination))
    } catch (error) {
      return NextResponse.json(createErrorResponse('INTERNAL_SERVER_ERROR', 'Failed to get plays'), { status: 500 })
    }
  }
  
  // 获取短剧详情
  static async getPlayById(request: NextRequest, { params }: { params: { id: string } }): Promise<NextResponse> {
    try {
      const id = parseInt(params.id)
      const play = mockPlays.find(p => p.id === id)
      
      if (!play) {
        return NextResponse.json(createErrorResponse('PLAY_NOT_FOUND', 'Play not found'), { status: 404 })
      }
      
      return NextResponse.json(createSuccessResponse(play))
    } catch (error) {
      return NextResponse.json(createErrorResponse('INTERNAL_SERVER_ERROR', 'Failed to get play'), { status: 500 })
    }
  }
  
  // 搜索短剧
  static async searchPlays(request: NextRequest): Promise<NextResponse> {
    try {
      const params = parseQueryParams(request)
      const { query, page = 1, pageSize = 20 } = params
      
      if (!query) {
        return NextResponse.json(createErrorResponse('INVALID_PARAMETERS', 'Query parameter is required'), { status: 400 })
      }
      
      let filtered = applyFilters(mockPlays, params)
      
      const { items, pagination } = applyPagination(filtered, page, pageSize)
      
      return NextResponse.json(createSuccessResponse(items, pagination))
    } catch (error) {
      return NextResponse.json(createErrorResponse('INTERNAL_SERVER_ERROR', 'Search failed'), { status: 500 })
    }
  }
  
  // 获取热门短剧
  static async getPopularPlays(request: NextRequest): Promise<NextResponse> {
    try {
      const params = parseQueryParams(request)
      const { limit = 10, category } = params
      
      let filtered = applyFilters(mockPlays, { category })
      filtered = applySorting(filtered, 'popular')
      
      const popular = filtered.slice(0, limit)
      
      return NextResponse.json(createSuccessResponse(popular))
    } catch (error) {
      return NextResponse.json(createErrorResponse('INTERNAL_SERVER_ERROR', 'Failed to get popular plays'), { status: 500 })
    }
  }
  
  // 获取最新短剧
  static async getLatestPlays(request: NextRequest): Promise<NextResponse> {
    try {
      const params = parseQueryParams(request)
      const { limit = 10, category } = params
      
      let filtered = applyFilters(mockPlays, { category })
      filtered = applySorting(filtered, 'latest')
      
      const latest = filtered.slice(0, limit)
      
      return NextResponse.json(createSuccessResponse(latest))
    } catch (error) {
      return NextResponse.json(createErrorResponse('INTERNAL_SERVER_ERROR', 'Failed to get latest plays'), { status: 500 })
    }
  }
  
  // 获取分类列表
  static async getCategories(): Promise<NextResponse> {
    try {
      return NextResponse.json(createSuccessResponse(mockCategories))
    } catch (error) {
      return NextResponse.json(createErrorResponse('INTERNAL_SERVER_ERROR', 'Failed to get categories'), { status: 500 })
    }
  }
  
  // 获取分类下的短剧
  static async getPlaysByCategory(request: NextRequest, { params }: { params: { slug: string } }): Promise<NextResponse> {
    try {
      const category = mockCategories.find(c => c.slug === params.slug)
      if (!category && params.slug !== 'all') {
        return NextResponse.json(createErrorResponse('CATEGORY_NOT_FOUND', 'Category not found'), { status: 404 })
      }
      
      const queryParams = parseQueryParams(request)
      const { page = 1, pageSize = 20, sortBy = 'latest' } = queryParams
      
      let filtered = applyFilters(mockPlays, { category: params.slug })
      filtered = applySorting(filtered, sortBy)
      
      const { items, pagination } = applyPagination(filtered, page, pageSize)
      
      return NextResponse.json(createSuccessResponse(items, pagination))
    } catch (error) {
      return NextResponse.json(createErrorResponse('INTERNAL_SERVER_ERROR', 'Failed to get category plays'), { status: 500 })
    }
  }
  
  // 获取统计信息
  static async getStatistics(): Promise<NextResponse> {
    try {
      const stats: PlayStatistics = {
        totalPlays: mockPlays.length,
        totalViews: mockPlays.reduce((sum, play) => sum + (play.views || 0), 0),
        categoryCounts: mockCategories.reduce((acc, cat) => {
          acc[cat.slug] = cat.count || 0
          return acc
        }, {} as { [key: string]: number }),
        popularTags: ["重生", "都市", "古装", "言情", "霸总"],
        updateFrequency: {
          today: 5,
          thisWeek: 15,
          thisMonth: 45,
        },
      }
      
      return NextResponse.json(createSuccessResponse(stats))
    } catch (error) {
      return NextResponse.json(createErrorResponse('INTERNAL_SERVER_ERROR', 'Failed to get statistics'), { status: 500 })
    }
  }
  
  // 健康检查
  static async healthCheck(): Promise<NextResponse> {
    return NextResponse.json(createSuccessResponse({
      status: 'healthy',
      timestamp: new Date().toISOString(),
    }))
  }
}

// ============= 导出默认API处理映射 =============

export const apiHandlers = {
  'GET /api/plays': MockApiHandlers.getPlays,
  'GET /api/plays/:id': MockApiHandlers.getPlayById,
  'GET /api/plays/search': MockApiHandlers.searchPlays,
  'GET /api/plays/popular': MockApiHandlers.getPopularPlays,
  'GET /api/plays/latest': MockApiHandlers.getLatestPlays,
  'GET /api/categories': MockApiHandlers.getCategories,
  'GET /api/categories/:slug/plays': MockApiHandlers.getPlaysByCategory,
  'GET /api/statistics': MockApiHandlers.getStatistics,
  'GET /api/health': MockApiHandlers.healthCheck,
}

export default MockApiHandlers

/* 
============= 后端实现参考 =============

这个文件展示了完整的API结构，后端开发者可以参考以下要点：

1. **数据库设计**
   - plays 表：存储短剧信息
   - categories 表：存储分类信息  
   - play_links 表：存储网盘链接（支持多平台）
   - play_tags 表：存储标签关联

2. **API端点**
   - GET /api/plays - 短剧列表（支持分页、搜索、排序）
   - GET /api/plays/:id - 短剧详情
   - GET /api/plays/search - 搜索短剧
   - GET /api/plays/popular - 热门短剧
   - GET /api/plays/latest - 最新短剧
   - GET /api/categories - 分类列表
   - GET /api/categories/:slug/plays - 分类下的短剧
   - GET /api/statistics - 统计信息
   - GET /api/health - 健康检查

3. **响应格式**
   统一使用ApiResponse<T>格式，包含success、data、error、pagination字段

4. **错误处理**
   定义了标准错误码，方便前端统一处理

5. **性能优化建议**
   - 实现数据库索引（title、category、created_at、rating等）
   - 使用缓存（Redis）缓存热门数据
   - 实现分页查询避免大量数据传输
   - 考虑CDN缓存静态资源

6. **安全考虑**
   - API限流
   - 输入验证和清理
   - SQL注入防护
   - XSS防护

7. **可扩展性**
   - 支持多种数据库（MySQL、PostgreSQL等）
   - 支持微服务架构
   - 支持容器化部署
   - 支持负载均衡

*/