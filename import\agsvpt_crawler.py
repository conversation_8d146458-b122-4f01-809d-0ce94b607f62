"""
AGSVPT网站数据爬取模块
爬取短剧种子信息并解析为结构化数据，然后导入到Flask数据库
"""

import requests      
import xml.etree.ElementTree as ET          # 标准库
from bs4 import BeautifulSoup               # pip install beautifulsoup4
import re
import sys
import os
import random
import time
from datetime import datetime
from urllib.parse import urlparse
from pathlib import Path

# 添加Flask项目路径
flask_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'flask'))
sys.path.append(flask_path)

def clean_title(title):
    """清理标题，去除特殊字符和多余空格"""
    if not title:
        return ""
    # 去除常见的特殊标记
    title = re.sub(r'【.*?】|\[.*?\]|\(.*?\)|（.*?）', '', title)
    title = re.sub(r'\s+', ' ', title)  # 多个空格替换为单个
    return title.strip()

def parse_episodes(episodes_str):
    """解析集数"""
    if not episodes_str:
        return None
    # 提取数字
    match = re.search(r'(\d+)', str(episodes_str))
    return int(match.group(1)) if match else None

def parse_year(year_str):
    """解析年份"""
    if not year_str:
        return None
    # 提取4位数年份
    match = re.search(r'(20\d{2})', str(year_str))
    return int(match.group(1)) if match else None

def map_categories(category_str):
    """将爬虫分类映射到数据库分类，保持原始分类名称不变"""
    if not category_str:
        return []
    
    # 分割分类字符串，保持原始名称
    categories = re.split(r'[,，\s]+', category_str.strip())
    clean_categories = []
    
    for cat in categories:
        cat = cat.strip()
        # 去除分类名末尾的斜杠
        cat = cat.rstrip('/')
        if cat:
            clean_categories.append(cat)
    
    # 如果没有找到任何分类，默认为"全部"
    if not clean_categories:
        clean_categories = ['全部']
    
    return clean_categories

def ensure_categories_exist(category_names):
    """确保所有分类在数据库中存在，如果不存在则创建"""
    from models import db, Category
    from datetime import datetime
    
    existing_categories = []
    
    for cat_name in category_names:
        # 检查分类是否已存在
        category = Category.query.filter_by(name=cat_name).first()
        
        if not category:
            # 创建新分类
            # 生成slug（简单的拼音化处理）
            slug_mapping = {
                '剧情': 'drama',
                '喜剧': 'comedy',
                '逆袭': 'counterattack', 
                '都市': 'urban',
                '古装': 'historical',
                '玄幻': 'fantasy',
                '言情': 'romance',
                '甜宠': 'sweet',
                '复仇': 'revenge',
                '穿越': 'timetravel',
                '重生': 'rebirth',
                '现代': 'modern',
                '总裁': 'ceo',
                '霸总': 'domineering',
                '虐恋': 'tormented',
                '宫斗': 'palace',
                '权谋': 'intrigue',
                '仙侠': 'immortal',
                '修真': 'cultivation',
                '悬疑': 'suspense',
                '校园': 'campus',
                '军事': 'military',
                '医疗': 'medical',
                '商战': 'business',
                '科幻': 'sci-fi',
                '末日': 'apocalypse',
                '生存': 'survival'
            }
            
            slug = slug_mapping.get(cat_name, cat_name.lower())
            
            # 获取最大的order_index
            max_order = db.session.query(db.func.max(Category.order_index)).scalar() or 0
            
            category = Category(
                name=cat_name,
                slug=slug,
                order_index=max_order + 1,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            db.session.add(category)
            db.session.flush()  # 立即获取ID，但不提交事务
            print(f"创建新分类: {cat_name} ({slug})")
        
        existing_categories.append(category)
    
    return existing_categories

def download_image_to_local(image_url, base_dir=None):
    """
    下载图片到本地，使用时间戳命名
    
    Args:
        image_url: 图片URL
        base_dir: 基础目录，默认为 ../flask/static/images
        
    Returns:
        str: 成功时返回相对路径，失败时返回None
    """
    if not image_url:
        return None
        
    if base_dir is None:
        # 默认保存到Flask的static/images目录
        current_dir = os.path.dirname(__file__)
        base_dir = os.path.abspath(os.path.join(current_dir, '..', 'flask', 'static', 'images'))
    
    try:
        print(f"  [DOWN] 开始下载封面: {image_url}")
        
        # 生成时间戳文件名
        now = datetime.now()
        timestamp = int(now.timestamp() * 1000)
        date_path = now.strftime("%Y/%m/%d")
        filename = f"{timestamp}.jpg"
        
        # 创建完整的保存路径 - 注意这里直接用date_path
        full_dir = os.path.join(base_dir, *date_path.split('/'))  # 转换为正确的路径分隔符
        os.makedirs(full_dir, exist_ok=True)
        
        # 下载图片
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        response = requests.get(image_url, headers=headers, timeout=30)
        response.raise_for_status()
        
        # 检查是否为图片内容
        content_type = response.headers.get('Content-Type', '').lower()
        if 'image' not in content_type and len(response.content) < 1024:
            print(f"  [WARN] 可能不是有效图片: Content-Type={content_type}")
        
        # 保存文件
        file_path = os.path.join(full_dir, filename)
        with open(file_path, 'wb') as f:
            f.write(response.content)
        
        # 返回相对于static的路径（用于数据库存储）
        # 使用正斜杠，因为这是web路径
        relative_path = f"/static/images/{date_path}/{filename}"
        
        print(f"  [OK] 封面下载成功: {relative_path}")
        print(f"       本地文件: {file_path}")
        return relative_path
        
    except Exception as e:
        print(f"  [ERROR] 封面下载失败: {e}")
        return None

def import_to_database(items):
    """将爬取的数据导入到生产环境数据库 (MariaDB)"""
    try:
        from app import create_app
        from models import db, Category, Play, PlayLink
        
        # 强制使用生产环境
        os.environ['FLASK_CONFIG'] = 'production'
        app = create_app('production')
        print("[INFO] 使用生产环境数据库 (MariaDB)")
        
        with app.app_context():
            imported_count = 0
            skipped_count = 0
            
            print(f"\n开始导入 {len(items)} 条爬取数据到生产数据库...")
            
            for item in items:
                try:
                    # 清理和解析数据
                    title = clean_title(item.get('title', ''))
                    if not title:
                        print(f"跳过无标题项目")
                        skipped_count += 1
                        continue
                    
                    # 检查是否已存在
                    existing_play = Play.query.filter_by(title=title).first()
                    if existing_play:
                        print(f"跳过已存在的短剧: {title}")
                        skipped_count += 1
                        continue
                    
                    # 解析其他字段
                    episodes = parse_episodes(item.get('episodes'))
                    year = parse_year(item.get('year'))
                    description = item.get('synopsis', '')[:500] if item.get('synopsis') else ''  # 限制长度
                    
                    # 生成随机观看数 (100-10000) 用于SEO优化
                    random_views = random.randint(100, 10000)
                    
                    # 处理封面图片：下载到本地
                    original_poster_url = item.get('cover', '')
                    if original_poster_url:
                        # 尝试下载图片到本地
                        local_poster_path = download_image_to_local(original_poster_url)
                        if local_poster_path:
                            poster_url = local_poster_path  # 使用本地路径
                            print(f"  [OK] 使用本地封面: {local_poster_path}")
                        else:
                            poster_url = original_poster_url  # 下载失败，使用原始URL
                            print(f"  [WARN] 使用原始封面: {original_poster_url}")
                    else:
                        poster_url = ''
                    
                    # 创建短剧记录
                    play = Play(
                        title=title,
                        description=description,
                        poster_url=poster_url,
                        year=year,
                        episodes=episodes,
                        views=random_views,  # 使用随机观看数
                        status='active'
                    )
                    
                    db.session.add(play)
                    db.session.flush()  # 获取ID
                    
                    # 处理分类 - 确保分类存在并获取分类对象
                    category_names = map_categories(item.get('category', ''))
                    categories = ensure_categories_exist(category_names)
                    
                    # 关联分类
                    for category in categories:
                        play.categories.append(category)
                    
                    # 注意：不向play_link表插入数据，那是留给网盘链接的
                    # 爬虫的链接信息不需要保存到数据库
                    
                    db.session.commit()
                    imported_count += 1
                    print(f"成功导入: {title} (观看数: {random_views})")
                    
                except Exception as e:
                    db.session.rollback()
                    print(f"导入失败: {item.get('title', 'Unknown')} - {str(e)}")
                    skipped_count += 1
            
            print(f"\n导入完成！成功: {imported_count}, 跳过: {skipped_count}")
            return imported_count, skipped_count
            
    except ImportError as e:
        print(f"无法导入Flask模块: {e}")
        print("请确保在Flask项目环境中运行此脚本")
        return 0, len(items)

# 1. 抓取 RSS
url = "https://www.agsvpt.com/torrentrss.php?passkey=0d4feadb8f31654b72763a3dad167361&rows=10&cat419=1&icat=1&ismalldescr=1&isize=1&iuplder=1&paid=2"

def parse_description(desc_html: str) -> dict:
    """提取封面 + 6 个"◎字段" """
    soup = BeautifulSoup(desc_html or "", "lxml")

    # ① 封面：第一张 <img>
    first_img = soup.find("img")
    cover = first_img.get("src") if first_img else None

    # ② 去掉 HTML → 纯文本
    plain = soup.get_text("\n").replace("\u3000", "")  # 去全角空格

    # ③ 用正则一次性抓
    def pick(field):
        m = re.search(fr"◎{field}\s*[:：]?\s*([^\n]+)", plain)
        return m.group(1).strip() if m else None

    return {
        "cover":     cover,
        "title":     pick("片名"),
        "year":      pick("年代"),
        "region":    pick("产地"),
        "category":  pick("类别"),
        "language":  pick("语言"),
        "synopsis":  pick("简介"),
        "raw_text":  plain
    }

def crawl_and_import(limit=10):
    """爬取数据并导入生产数据库 (MariaDB)
    
    Args:
        limit: 爬取数量限制
    """
    try:
        print("=== AGSVPT数据爬取和导入 ===")
        print(f"目标环境: 生产环境 (MariaDB)")
        print(f"开始爬取最新{limit}条短剧数据...")
        
        # 动态设置抓取数量
        crawl_url = f"https://www.agsvpt.com/torrentrss.php?passkey=0d4feadb8f31654b72763a3dad167361&rows={limit}&cat419=1&icat=1&ismalldescr=1&isize=1&iuplder=1&paid=2"
        
        # 增加重试机制和更长的超时时间
        max_retries = 3
        for attempt in range(max_retries):
            try:
                print(f"[FETCH] 尝试连接 (第{attempt + 1}次)...")
                
                # 设置请求头和更长的超时时间
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept': 'text/xml,application/xml,application/xhtml+xml,text/html;q=0.9,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate',
                    'Connection': 'keep-alive',
                    'Cache-Control': 'no-cache'
                }
                
                resp = requests.get(crawl_url, headers=headers, timeout=60)
                resp.raise_for_status()
                xml_text = resp.text
                print(f"[OK] 成功获取RSS数据 ({len(xml_text)} 字符)")
                break
                
            except requests.exceptions.Timeout:
                print(f"[WARN] 请求超时 (第{attempt + 1}次)")
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 10
                    print(f"[WAIT] 等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    raise
                    
            except requests.exceptions.RequestException as e:
                print(f"[ERROR] 网络请求失败 (第{attempt + 1}次): {e}")
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 5
                    print(f"[WAIT] 等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    raise

        # 2. 解析 XML
        try:
            root = ET.fromstring(xml_text)
        except ET.ParseError as e:
            print(f"[ERROR] XML解析失败: {e}")
            print(f"[DEBUG] 响应内容前500字符: {xml_text[:500]}")
            return

        # 3. 遍历 <item>
        items = []
        for node in root.findall("./channel/item"):
            title_raw = node.findtext("title")
            title_clean = re.sub(r"<!\[CDATA\[|\]\]>", "", title_raw or "")
            m = re.search(r"全\s*(\d+)\s*集", title_clean)   # 匹配 "全 84 集" 这种
            episodes = int(m.group(1)) if m else None
            desc_block = node.findtext("description")
            meta = parse_description(desc_block)

            items.append({
                "link":      node.findtext("link"),
                "episodes":  episodes,
                "pub_date":  node.findtext("pubDate"),
                **meta
            })

        print(f"成功爬取到 {len(items)} 条数据")
        
        # 4. 显示预览
        print(f"\n=== 数据预览 (前3条) ===")
        for i, it in enumerate(items[:3], 1):
            print(f"\n--- 第{i}条 ---")
            print("标题    :", it["title"])
            print("集数    :", it["episodes"])
            print("年代    :", it["year"])
            print("分类    :", it["category"])
            print("封面    :", it["cover"])
            synopsis = it["synopsis"] or ""
            print("简介摘录:", synopsis[:50] + "..." if len(synopsis) > 50 else synopsis)
        
        # 5. 导入数据库
        if items:
            imported, skipped = import_to_database(items)
            print(f"\n=== 最终结果 ===")
            print(f"目标环境: 生产环境 (MariaDB)")
            print(f"爬取数据: {len(items)} 条")
            print(f"成功导入: {imported} 条")
            print(f"跳过重复: {skipped} 条")
        else:
            print("没有爬取到有效数据")
            
    except Exception as e:
        print(f"爬取失败: {str(e)}")
        print(f"[DEBUG] 错误类型: {type(e).__name__}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    import sys
    
    # 解析命令行参数 - 简化版本
    limit = 10  # 默认抓取10条
    
    # 处理命令行参数
    for arg in sys.argv[1:]:
        if arg.isdigit():
            limit = int(arg)
        elif arg.lower() in ['--help', '-h']:
            print("用法: python agsvpt_crawler.py [数量]")
            print("参数:")
            print("  数量           爬取的数据条数 (默认: 10)")
            print("  --help, -h     显示帮助信息")
            print()
            print("说明:")
            print("  此脚本专门用于向生产环境数据库(MariaDB)导入数据")
            print("  每个短剧会自动生成100-10000的随机观看数用于SEO优化")
            print()
            print("示例:")
            print("  python agsvpt_crawler.py 20      # 爬取20条数据")
            print("  python agsvpt_crawler.py 50      # 爬取50条数据")
            sys.exit(0)
    
    # 显示配置信息
    print(f"[CONFIG] 爬取数量: {limit}")
    print(f"[CONFIG] 目标环境: 生产环境 (MariaDB)")
    print(f"[CONFIG] 随机观看数: 100-10000 (SEO优化)")
    print(f"[CONFIG] 超时设置: 60秒，最多重试3次")
    
    # 开始爬取和导入
    crawl_and_import(limit)