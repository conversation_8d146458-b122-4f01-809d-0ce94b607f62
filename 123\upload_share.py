import os
import json
import hashlib
import random
import string
import requests
import time
from typing import Optional, List, Dict, Any

class YunPan123Client:
    def __init__(self, config_path: str = "config.json"):
        """
        初始化客户端
        
        Args:
            config_path: 配置文件路径，默认为 config.json
        """
        self.config = self._load_config(config_path)
        self.client_id = self.config["client_id"]
        self.client_secret = self.config["client_secret"]
        self.access_token = None
        self.token_expires_at = None
        self.base_url = self.config.get("base_url", "https://open-api.123pan.com")
        
        # 默认设置
        self.default_expire_days = self.config["default_settings"]["expire_days"]
        self.auto_generate_code = self.config["default_settings"]["auto_generate_code"]
        self.extract_code_length = self.config["default_settings"]["extract_code_length"]
        self.chunk_size = self.config["default_settings"]["chunk_size"]
        self.token_refresh_margin = self.config["default_settings"]["token_refresh_margin"]
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        if not os.path.exists(config_path):
            raise Exception(f"配置文件不存在: {config_path}")
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 验证必要配置
            required_keys = ["client_id", "client_secret"]
            for key in required_keys:
                if not config.get(key) or config[key] == f"your_{key}_here":
                    raise Exception(f"请在配置文件中设置正确的 {key}")
            
            return config
        except json.JSONDecodeError as e:
            raise Exception(f"配置文件格式错误: {e}")
        except Exception as e:
            raise Exception(f"加载配置文件失败: {e}")
        
    def _get_access_token(self) -> str:
        """获取access_token"""
        if self.access_token and self.token_expires_at:
            if time.time() < self.token_expires_at:
                return self.access_token
        
        url = f"{self.base_url}/api/v1/access_token"
        headers = {
            "Platform": "open_platform",
            "Content-Type": "application/json"
        }
        data = {
            "clientID": self.client_id,
            "clientSecret": self.client_secret
        }
        
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        
        result = response.json()
        if result["code"] != 0:
            raise Exception(f"获取access_token失败: {result['message']}")
        
        self.access_token = result["data"]["accessToken"]
        # 解析过期时间
        expires_at_str = result["data"]["expiredAt"]
        import datetime
        expires_at = datetime.datetime.fromisoformat(expires_at_str.replace('Z', '+00:00'))
        self.token_expires_at = expires_at.timestamp() - self.token_refresh_margin  # 提前刷新
        
        return self.access_token
    
    def _calculate_md5(self, file_path: str) -> str:
        """计算文件MD5"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(self.chunk_size), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def _calculate_chunk_md5(self, chunk_data: bytes) -> str:
        """计算分片MD5"""
        return hashlib.md5(chunk_data).hexdigest()
    
    def create_directory(self, parent_id: int, name: str) -> int:
        """创建目录"""
        access_token = self._get_access_token()
        url = f"{self.base_url}/upload/v1/file/mkdir"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Platform": "open_platform",
            "Content-Type": "application/json"
        }
        data = {
            "parentID": parent_id,
            "name": name
        }
        
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        
        result = response.json()
        if result["code"] != 0:
            raise Exception(f"创建目录失败: {result['message']}")
        
        return result["data"]["dirID"]
    
    def get_file_list(self, parent_id: int = 0) -> List[Dict]:
        """获取文件列表"""
        access_token = self._get_access_token()
        url = f"{self.base_url}/api/v2/file/list"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Platform": "open_platform",
            "Content-Type": "application/json"
        }
        params = {
            "parentFileId": parent_id,
            "limit": 100
        }
        
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()
        
        result = response.json()
        if result["code"] != 0:
            raise Exception(f"获取文件列表失败: {result['message']}")
        
        # 过滤掉回收站文件
        file_list = [item for item in result["data"]["fileList"] if item["trashed"] == 0]
        return file_list
    
    def find_or_create_directory_path(self, directory_path: str, parent_id: int = 0) -> int:
        """查找或创建目录路径"""
        if not directory_path or directory_path == "/":
            return parent_id
        
        # 移除开头的斜杠并分割路径
        path_parts = directory_path.strip("/").split("/")
        current_parent_id = parent_id
        
        for part in path_parts:
            if not part:
                continue
                
            # 获取当前目录下的文件列表
            file_list = self.get_file_list(current_parent_id)
            
            # 查找是否存在同名目录
            found_dir = None
            for item in file_list:
                if item["filename"] == part and item["type"] == 1:  # type=1表示目录
                    found_dir = item
                    break
            
            if found_dir:
                current_parent_id = found_dir["fileId"]
            else:
                # 创建新目录
                current_parent_id = self.create_directory(current_parent_id, part)
        
        return current_parent_id
    
    def upload_file(self, file_path: str, directory_path: str = "/") -> int:
        """上传文件到指定目录"""
        if not os.path.exists(file_path):
            raise Exception(f"文件不存在: {file_path}")
        
        # 查找或创建目录
        parent_id = self.find_or_create_directory_path(directory_path)
        
        file_name = os.path.basename(file_path)
        file_size = os.path.getsize(file_path)
        file_md5 = self._calculate_md5(file_path)
        
        access_token = self._get_access_token()
        
        # 1. 创建文件
        create_url = f"{self.base_url}/upload/v2/file/create"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Platform": "open_platform",
            "Content-Type": "application/json"
        }
        data = {
            "parentFileID": parent_id,
            "filename": file_name,
            "etag": file_md5,
            "size": file_size,
            "duplicate": 1  # 保留两者，新文件名将自动添加后缀
        }
        
        response = requests.post(create_url, headers=headers, json=data)
        response.raise_for_status()
        
        result = response.json()
        if result["code"] != 0:
            raise Exception(f"创建文件失败: {result['message']}")
        
        # 检查是否秒传
        if result["data"]["reuse"]:
            print(f"文件秒传成功: {file_name}")
            return result["data"]["fileID"]
        
        preupload_id = result["data"]["preuploadID"]
        slice_size = result["data"]["sliceSize"]
        servers = result["data"]["servers"]
        
        if not servers:
            raise Exception("未获取到上传服务器")
        
        upload_server = servers[0]
        
        # 2. 分片上传
        with open(file_path, 'rb') as f:
            slice_index = 1
            while True:
                chunk = f.read(slice_size)
                if not chunk:
                    break
                
                chunk_md5 = self._calculate_chunk_md5(chunk)
                
                upload_url = f"{upload_server}/upload/v2/file/slice"
                files = {'slice': ('chunk', chunk, 'application/octet-stream')}
                data = {
                    'preuploadID': preupload_id,
                    'sliceNo': str(slice_index),
                    'sliceMD5': chunk_md5
                }
                headers = {
                    'Authorization': f'Bearer {access_token}',
                    'Platform': 'open_platform'
                }
                
                upload_response = requests.post(upload_url, files=files, data=data, headers=headers)
                upload_response.raise_for_status()
                
                upload_result = upload_response.json()
                if upload_result["code"] != 0:
                    raise Exception(f"上传分片失败: {upload_result['message']}")
                
                print(f"上传分片 {slice_index} 完成")
                slice_index += 1
        
        # 3. 上传完毕
        complete_url = f"{self.base_url}/upload/v2/file/upload_complete"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Platform": "open_platform",
            "Content-Type": "application/json"
        }
        data = {
            "preuploadID": preupload_id
        }
        
        while True:
            response = requests.post(complete_url, headers=headers, json=data)
            response.raise_for_status()
            
            result = response.json()
            if result["code"] != 0:
                raise Exception(f"完成上传失败: {result['message']}")
            
            if result["data"]["completed"] and result["data"]["fileID"] != 0:
                file_id = result["data"]["fileID"]
                print(f"文件上传成功: {file_name}, fileID: {file_id}")
                return file_id
            
            print("等待上传处理完成...")
            time.sleep(1)
    
    def generate_random_extract_code(self, length: Optional[int] = None) -> str:
        """生成随机提取码"""
        if length is None:
            length = self.extract_code_length
        # 使用字母和数字组合，排除容易混淆的字符
        chars = "abcdefghijkmnpqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ23456789"
        return ''.join(random.choice(chars) for _ in range(length))
    
    def create_share_link(self, 
                         file_ids: List[int], 
                         share_name: str,
                         expire_days: Optional[int] = None,
                         extract_code: Optional[str] = None,
                         auto_generate_code: Optional[bool] = None) -> Dict[str, Any]:
        """创建分享链接
        
        Args:
            file_ids: 文件ID列表
            share_name: 分享链接名称
            expire_days: 有效期天数 (1, 7, 30, 0永久)，默认使用配置文件设置
            extract_code: 提取码，如果为None且auto_generate_code为True则自动生成
            auto_generate_code: 是否自动生成提取码，默认使用配置文件设置
        """
        access_token = self._get_access_token()
        
        # 使用默认配置
        if expire_days is None:
            expire_days = self.default_expire_days
        if auto_generate_code is None:
            auto_generate_code = self.auto_generate_code
        
        if expire_days not in [0, 1, 7, 30]:
            raise Exception("有效期只能是 0(永久), 1, 7, 30 天")
        
        # 自动生成提取码
        if extract_code is None and auto_generate_code:
            extract_code = self.generate_random_extract_code()
        
        url = f"{self.base_url}/api/v1/share/create"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Platform": "open_platform",
            "Content-Type": "application/json"
        }
        
        data = {
            "shareName": share_name,
            "shareExpire": expire_days,
            "fileIDList": ",".join(map(str, file_ids))
        }
        
        if extract_code:
            data["sharePwd"] = extract_code
        
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        
        result = response.json()
        if result["code"] != 0:
            raise Exception(f"创建分享链接失败: {result['message']}")
        
        share_key = result["data"]["shareKey"]
        share_url = f"https://www.123pan.com/s/{share_key}"
        
        return {
            "shareID": result["data"]["shareID"],
            "shareKey": share_key,
            "shareUrl": share_url,
            "extractCode": extract_code,
            "expireDays": expire_days
        }
    
    def upload_and_share(self,
                        file_path: str,
                        directory_path: str = "/",
                        share_name: Optional[str] = None,
                        expire_days: Optional[int] = None,
                        extract_code: Optional[str] = None,
                        auto_generate_code: Optional[bool] = None) -> Dict[str, Any]:
        """上传文件并创建分享链接"""
        
        # 使用默认配置
        if expire_days is None:
            expire_days = self.default_expire_days
        if auto_generate_code is None:
            auto_generate_code = self.auto_generate_code
        
        # 上传文件
        print(f"开始上传文件: {file_path}")
        file_id = self.upload_file(file_path, directory_path)
        
        # 创建分享链接
        if share_name is None:
            share_name = f"分享文件: {os.path.basename(file_path)}"
        
        print("创建分享链接...")
        share_info = self.create_share_link(
            file_ids=[file_id],
            share_name=share_name,
            expire_days=expire_days,
            extract_code=extract_code,
            auto_generate_code=auto_generate_code
        )
        
        result = {
            "fileID": file_id,
            "fileName": os.path.basename(file_path),
            "directory": directory_path,
            **share_info
        }
        
        print(f"\n=== 上传并分享完成 ===")
        print(f"文件名: {result['fileName']}")
        print(f"上传目录: {result['directory']}")
        print(f"分享链接: {result['shareUrl']}")
        if result['extractCode']:
            print(f"提取码: {result['extractCode']}")
        print(f"有效期: {expire_days}天" if expire_days > 0 else "永久有效")
        
        return result

def main():
    """示例使用"""
    try:
        # 使用配置文件初始化客户端
        client = YunPan123Client("config.json")
        
        # 示例1: 上传文件到根目录并分享（使用默认配置）
        result = client.upload_and_share(
            file_path="test.txt",  # 替换为实际文件路径
            directory_path="/",
            share_name="测试分享"
        )
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        # 示例2: 上传文件到指定目录并分享（自定义配置）
        result = client.upload_and_share(
            file_path="document.pdf",  # 替换为实际文件路径
            directory_path="/我的文档/重要文件",
            share_name="重要文档分享",
            expire_days=30,
            extract_code="abc123"  # 自定义提取码
        )
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
    except Exception as e:
        print(f"错误: {e}")
        print("请检查config.json文件是否正确配置")

if __name__ == "__main__":
    main()