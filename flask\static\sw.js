const CACHE_NAME = 'shortplay-v1.0.0';
const OFFLINE_URL = '/offline';

// 预缓存的核心资源
const CORE_ASSETS = [
  '/',
  '/static/css/main.css',
  '/static/js/main.js',
  '/static/manifest.json',
  '/offline',
  // Alpine.js和Tailwind CSS CDN
  'https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js',
  'https://cdn.tailwindcss.com'
];

// 需要缓存的API端点
const API_ENDPOINTS = [
  '/api/categories',
  '/api/plays',
  '/api/search/suggestions'
];

// 静态资源匹配模式
const STATIC_ASSETS_REGEX = /\.(js|css|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot|ico)$/;

/**
 * Service Worker安装事件
 */
self.addEventListener('install', event => {
  console.log('[SW] Installing Service Worker');
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('[SW] Pre-caching core assets');
        return cache.addAll(CORE_ASSETS);
      })
      .then(() => {
        console.log('[SW] Core assets cached successfully');
        // 强制激活新版本
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('[SW] Failed to cache core assets:', error);
      })
  );
});

/**
 * Service Worker激活事件
 */
self.addEventListener('activate', event => {
  console.log('[SW] Activating Service Worker');
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName !== CACHE_NAME) {
              console.log('[SW] Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('[SW] Service Worker activated');
        // 立即接管所有页面
        return self.clients.claim();
      })
  );
});

/**
 * Fetch事件处理 - 实现不同的缓存策略
 */
self.addEventListener('fetch', event => {
  const { request } = event;
  const url = new URL(request.url);
  
  // 只处理同源请求和需要的CDN资源
  if (!url.origin.includes(self.location.origin) && 
      !url.href.includes('unpkg.com') && 
      !url.href.includes('tailwindcss.com')) {
    return;
  }
  
  // API请求 - 网络优先策略
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(handleApiRequest(request));
    return;
  }
  
  // 静态资源 - 缓存优先策略
  if (STATIC_ASSETS_REGEX.test(url.pathname) || url.href.includes('cdn')) {
    event.respondWith(handleStaticAssets(request));
    return;
  }
  
  // HTML页面 - 网络优先，离线降级策略
  if (request.destination === 'document') {
    event.respondWith(handleDocumentRequest(request));
    return;
  }
  
  // 其他请求 - 网络优先策略
  event.respondWith(handleOtherRequests(request));
});

/**
 * 处理API请求 - 网络优先，缓存降级
 */
async function handleApiRequest(request) {
  const cache = await caches.open(CACHE_NAME);
  
  try {
    // 尝试网络请求
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // 缓存成功的API响应
      cache.put(request, networkResponse.clone());
      
      // 添加网络状态标识
      const response = networkResponse.clone();
      response.headers.set('X-Served-By', 'network');
      
      return response;
    }
    
    throw new Error('Network response not ok');
  } catch (error) {
    console.log('[SW] Network failed for API, serving from cache:', request.url);
    
    // 网络失败，从缓存获取
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      // 添加缓存状态标识
      cachedResponse.headers.set('X-Served-By', 'cache');
      return cachedResponse;
    }
    
    // 返回离线API响应
    return new Response(JSON.stringify({
      success: false,
      message: '网络连接异常，请检查网络后重试',
      offline: true
    }), {
      status: 503,
      headers: {
        'Content-Type': 'application/json',
        'X-Served-By': 'offline'
      }
    });
  }
}

/**
 * 处理静态资源 - 缓存优先策略
 */
async function handleStaticAssets(request) {
  const cache = await caches.open(CACHE_NAME);
  
  // 首先检查缓存
  const cachedResponse = await cache.match(request);
  
  if (cachedResponse) {
    console.log('[SW] Serving static asset from cache:', request.url);
    
    // 后台更新缓存
    fetch(request)
      .then(response => {
        if (response.ok) {
          cache.put(request, response.clone());
        }
      })
      .catch(() => {
        // 静默失败，继续使用缓存版本
      });
    
    return cachedResponse;
  }
  
  // 缓存中没有，尝试网络请求
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // 缓存新的静态资源
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.error('[SW] Failed to fetch static asset:', request.url);
    
    // 返回占位图片或默认资源
    if (request.url.includes('.png') || request.url.includes('.jpg')) {
      return new Response('', { 
        status: 200, 
        headers: { 'Content-Type': 'image/svg+xml' }
      });
    }
    
    throw error;
  }
}

/**
 * 处理HTML文档请求 - 网络优先，离线降级
 */
async function handleDocumentRequest(request) {
  const cache = await caches.open(CACHE_NAME);
  
  try {
    // 尝试网络请求
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // 缓存页面（但不包括带查询参数的动态页面）
      const url = new URL(request.url);
      if (!url.search) {
        cache.put(request, networkResponse.clone());
      }
      
      return networkResponse;
    }
    
    throw new Error('Network response not ok');
  } catch (error) {
    console.log('[SW] Network failed for document, trying cache:', request.url);
    
    // 尝试从缓存获取
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // 返回离线页面
    console.log('[SW] Serving offline page');
    return cache.match(OFFLINE_URL) || 
           cache.match('/') || 
           new Response('离线页面不可用', { 
             status: 503,
             headers: { 'Content-Type': 'text/html; charset=utf-8' }
           });
  }
}

/**
 * 处理其他请求 - 网络优先策略
 */
async function handleOtherRequests(request) {
  try {
    return await fetch(request);
  } catch (error) {
    console.log('[SW] Network failed for other request:', request.url);
    
    // 尝试从缓存获取
    const cache = await caches.open(CACHE_NAME);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    throw error;
  }
}

/**
 * 后台同步事件（如果支持）
 */
self.addEventListener('sync', event => {
  console.log('[SW] Background sync triggered:', event.tag);
  
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync());
  }
});

/**
 * 执行后台同步任务
 */
async function doBackgroundSync() {
  console.log('[SW] Performing background sync');
  
  try {
    // 更新缓存的API数据
    const cache = await caches.open(CACHE_NAME);
    
    for (const endpoint of API_ENDPOINTS) {
      try {
        const response = await fetch(endpoint);
        if (response.ok) {
          await cache.put(endpoint, response.clone());
          console.log('[SW] Updated cache for:', endpoint);
        }
      } catch (error) {
        console.log('[SW] Failed to update cache for:', endpoint);
      }
    }
  } catch (error) {
    console.error('[SW] Background sync failed:', error);
  }
}

/**
 * 推送通知事件（预留）
 */
self.addEventListener('push', event => {
  console.log('[SW] Push notification received');
  
  const options = {
    body: event.data ? event.data.text() : '您有新的短剧更新！',
    icon: '/static/icons/icon-192x192.png',
    badge: '/static/icons/badge-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: '1'
    },
    actions: [
      {
        action: 'explore',
        title: '立即查看',
        icon: '/static/icons/checkmark.png'
      },
      {
        action: 'close',
        title: '稍后查看',
        icon: '/static/icons/xmark.png'
      }
    ]
  };
  
  event.waitUntil(
    self.registration.showNotification('短剧平台', options)
  );
});

/**
 * 通知点击事件
 */
self.addEventListener('notificationclick', event => {
  console.log('[SW] Notification click received');
  
  event.notification.close();
  
  if (event.action === 'explore') {
    // 打开应用
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});