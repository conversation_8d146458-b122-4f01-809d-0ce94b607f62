# 获取付费分享链接列表

API： GET 域名 + /api/v1/share/payment/list

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#000000;">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## QueryString 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| limit | number | 必填 | 每页文件数量，最大不超过100 |
|  lastShareId | number | 选填 | 翻页查询时需要填写，默认为 0 |


## 返回数据
| **名称** | | **类型** | **是否必填** | **说明** |
| --- | --- | :---: | :---: | --- |
|  lastShareId   | | number | 必填 | -1代表最后一页（无需再翻页查询）<br/>其他代表下一页开始的分享文件id，携带到请求参数中 |
| shareList  | | array | 必填 | 文件列表 |
|  | shareId  | number | 必填 | 分享ID  |
|  | shareKey   | string | 必填 | 分享码,请将分享码拼接至 <br/>https://www.123pan.com/<font style="color:#DF2A3F;">ps</font>/ 后面访问,即是分享页面   |
|  | shareName   | string | 必填 | 分享链接名称 |
|  | payAmount | number | 必填 | 付费金额 |
|  | amount | number | 必填 | 分享收益 |
|  | expiration   | string | 必填 | 过期时间 |
|  | expired   | integer | 必填 | 是否失效<br/>0  未失效<br/>1  失效 |
|  | <br/>trafficSwitch   | <br/>integer | <br/>必填 | 分享提取流量包   1 全部关闭<br/>2 打开游客免登录提取<br/>3 打开超流量用户提取<br/>4 全部开启 |
|  | <br/>trafficLimitSwitch | <br/>integer | <br/>必填 | 分享提取流量包流量限制开关<br/>1 关闭限制<br/>2 打开限制 |
|  | <br/>trafficLimit   | <br/>number | <br/>必填 | 分享提取流量包限制流量<br/>单位：字节 |
|  | bytesCharge | number | 必填 | 分享使用流量<br/>单位：字节 |
|  | previewCount | number | 必填 | 预览次数 |
|  | downloadCount | number | 必填 | 下载次数 |
|  | saveCount | number | 必填 | 转存次数 |


## 示例
**请求示例**

```shell
curl --location 'https://open-api.123pan.com/api/v1/share/payment/list?limit=10&lastShareId=0' \
--header 'Content-Type: application/json' \
--header 'Platform: open_platform' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)' \
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
.build();
MediaType mediaType = MediaType.parse("application/json");
RequestBody body = RequestBody.create(mediaType, "{\n    \"shareIdList\": [87187530],\n    \"trafficSwitch\": 2,\n    \"trafficLimitSwitch\": 2,\n    \"trafficLimit\": 1073741824\n}");
Request request = new Request.Builder()
.url("https://open-api.123pan.com/api/v1/share/list/payment/info")
.method("PUT", body)
.addHeader("Content-Type", "application/json")
.addHeader("Platform", "open_platform")
.addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)")
.build();
Response response = client.newCall(request).execute();
```

```javascript
var settings = {
  "url": "https://open-api.123pan.com/api/v1/share/list/payment/info",
  "method": "PUT",
  "timeout": 0,
  "headers": {
    "Content-Type": "application/json",
    "Platform": "open_platform",
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)"
  },
  "data": JSON.stringify({
    "shareIdList": [
      87187530
    ],
    "trafficSwitch": 2,
    "trafficLimitSwitch": 2,
    "trafficLimit": 1073741824
  }),
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
```

```javascript
const axios = require('axios');
let data = JSON.stringify({
  "shareIdList": [
    87187530
  ],
  "trafficSwitch": 2,
  "trafficLimitSwitch": 2,
  "trafficLimit": 1073741824
});

let config = {
  method: 'put',
  maxBodyLength: Infinity,
  url: 'https://open-api.123pan.com/api/v1/share/list/payment/info',
  headers: { 
    'Content-Type': 'application/json', 
    'Platform': 'open_platform', 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
  },
  data : data
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});

```

```python
import http.client
import json

conn = http.client.HTTPSConnection("open-api.123pan.com")
payload = json.dumps({
    "shareIdList": [
        87187530
    ],
    "trafficSwitch": 2,
    "trafficLimitSwitch": 2,
    "trafficLimit": 1073741824
})
headers = {
    'Content-Type': 'application/json',
    'Platform': 'open_platform',
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
}
conn.request("PUT", "/api/v1/share/list/payment/info", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

**响应示例**

```json
{
    "code": 0,
    "message": "ok",
    "data": {
        "shareList": [
            {
                "shareId": 81252309,
                "shareKey": "JY1ZVv-9GqnH",
                "shareName": "张三",
                "expiration": "2099-12-31 08:00:00",
                "expired": 0,
                "trafficSwitch": 4,
                "trafficLimitSwitch": 2,
                "trafficLimit": 1231783478,
                "previewCount": 0,
                "downloadCount": 0,
                "saveCount": 0,
                "payAmount": 11.1,
                "auditStatus": 0,
                "amount": 0,
                "bytesCharge": 0,
                "createAt": "2025-07-07 18:36:24",
                "updateAt": "2025-07-07 18:56:12"
            }
        ],
        "lastShareId": -1
    },
    "x-traceID": ""
}
```



> 更新: 2025-07-22 20:48:38  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/mxc7eq2x3la72mwg>