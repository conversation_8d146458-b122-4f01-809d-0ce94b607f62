@echo off
chcp 65001 >nul
echo ================================
echo    短剧导入系统启动脚本
echo ================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未找到Python，请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

:: 检查是否安装了依赖
echo [信息] 检查Python依赖包...
pip show pyyaml >nul 2>&1
if errorlevel 1 (
    echo [警告] 检测到缺少依赖包，正在自动安装...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo [错误] 依赖包安装失败，请手动执行: pip install -r requirements.txt
        pause
        exit /b 1
    )
)

:: 检查配置文件
if not exist "config.yml" (
    echo [错误] 未找到配置文件 config.yml，请先配置系统参数
    pause
    exit /b 1
)

:: 显示菜单
:menu
echo.
echo 请选择运行模式:
echo [1] 完整模式 - 启动所有服务（推荐）
echo [2] 仅运行爬虫 - 执行一次爬取任务
echo [3] 仅qBittorrent监控 - 只监控下载完成
echo [4] 测试模式 - 测试各模块是否正常
echo [5] 查看日志文件
echo [6] 退出
echo.
set /p choice=请输入选项编号（1-6）:

if "%choice%"=="1" (
    echo [信息] 启动完整模式...
    python main.py --mode full
    goto end
)

if "%choice%"=="2" (
    echo [信息] 运行爬虫任务...
    python main.py --mode crawler
    echo [完成] 爬虫任务执行完毕
    pause
    goto menu
)

if "%choice%"=="3" (
    echo [信息] 启动qBittorrent监控...
    python main.py --mode qb
    goto end
)

if "%choice%"=="4" (
    echo [信息] 运行测试模式...
    python main.py --mode test
    echo [完成] 测试完成
    pause
    goto menu
)

if "%choice%"=="5" (
    if exist "import.log" (
        echo [信息] 显示最新日志...
        echo ================================
        type import.log | more
        echo ================================
    ) else (
        echo [信息] 日志文件不存在
    )
    pause
    goto menu
)

if "%choice%"=="6" (
    goto end
)

echo [错误] 无效选项，请重新选择
goto menu

:end
echo.
echo 感谢使用短剧导入系统！
pause