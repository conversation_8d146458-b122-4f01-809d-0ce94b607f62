# 获取文件列表（旧）

API： GET 域名 + /api/v1/file/list

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#000000;">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## QueryString 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | --- |
| parentFileId | number | 必填 | 文件夹ID，根目录传 0 |
| page | number | 必填 | 页码数 |
| limit | number | 必填 | 每页文件数量，最大不超过100 |
| orderBy | string | 必填 | 排序字段,例如:file_id、size、file_name |
| orderDirection | string | 必填 | 排序方向:asc、desc |
| trashed | bool | 选填 | 是否查看回收站的文件 |
| searchData | string | 选填 | 搜索关键字 |


## 返回数据
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | --- |
| fileList | array | 必填 |  |
| fileList[*].fileID | number | 必填 | 文件ID |
| fileList[*].filename | string | 必填 | 文件名 |
| fileList[*].type | number | 必填 | 0-文件  1-文件夹 |
| fileList[*].size | number | 必填 | 文件大小 |
| fileList[*].etag | boolean | 必填 | md5 |
| fileList[*].status | number | 必填 | 文件审核状态。 大于 100 为审核驳回文件 |
| fileList[*].parentFileId | number | 必填 | 目录ID |
| fileList[*].parentName | string | 必填 | 目录名 |
| fileList[*].category | number | 必填 | 文件分类：0-未知 1-音频 2-视频 3-图片 |
| fileList[*].contentType | number | 必填 | 文件类型 |


## 示例
请求示例

```shell
curl --location 'https://open-api.123pan.com/api/v1/file/list?parentFileId=0&page=1&limit=5&orderBy=file_id&orderDirection=asc' \
--header 'Content-Type: application/json' \
--header 'Platform: open_platform' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)' \
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
.build();
MediaType mediaType = MediaType.parse("application/json");
RequestBody body = RequestBody.create(mediaType, "");
Request request = new Request.Builder()
.url("https://open-api.123pan.com/api/v1/file/list?parentFileId=0&page=1&limit=5&orderBy=file_id&orderDirection=asc")
.method("GET", body)
.addHeader("Content-Type", "application/json")
.addHeader("Platform", "open_platform")
.addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)")
.build();
Response response = client.newCall(request).execute();
```

```javascript
var settings = {
  "url": "https://open-api.123pan.com/api/v1/file/list?parentFileId=0&page=1&limit=5&orderBy=file_id&orderDirection=asc",
  "method": "GET",
  "timeout": 0,
  "headers": {
    "Content-Type": "application/json",
    "Platform": "open_platform",
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)"
  },
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
```

```javascript
const axios = require('axios');

let config = {
  method: 'get',
  maxBodyLength: Infinity,
  url: 'https://open-api.123pan.com/api/v1/file/list?parentFileId=0&page=1&limit=5&orderBy=file_id&orderDirection=asc',
  headers: { 
    'Content-Type': 'application/json', 
    'Platform': 'open_platform', 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
  }
};

axios.request(config)
  .then((response) => {
    console.log(JSON.stringify(response.data));
  })
  .catch((error) => {
    console.log(error);
  });

```

```python
import http.client
import json

conn = http.client.HTTPSConnection("open-api.123pan.com")
payload = ''
headers = {
    'Content-Type': 'application/json',
    'Platform': 'open_platform',
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
}
conn.request("GET", "/api/v1/file/list?parentFileId=0&page=1&limit=5&orderBy=file_id&orderDirection=asc", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

响应示例

```json
{
  "code": 0,
  "message": "ok",
  "data": {
    "total": 4,
    "fileList": [
      {
        "fileID": 14710240,
        "filename": "测试二级目录",
        "parentFileId": 14663228,
        "parentName": "",
        "type": 1,
        "etag": "",
        "size": 0,
        "contentType": "0",
        "category": 0,
        "hidden": false,
        "status": 0,
        "punishFlag": 0,
        "s3KeyFlag": "x-0",
        "storageNode": "m0",
        "createAt": "2025-02-24 17:57:01 +0800 CST",
        "updateAt": "2025-02-24 17:57:01 +0800 CST",
        "thumbnail": "",
        "downloadUrl": ""
      },
      {
        "fileID": 10171597,
        "filename": "6-m.mp4",
        "parentFileId": 14663228,
        "parentName": "",
        "type": 0,
        "etag": "7a...(过长省略)",
        "size": 367628427,
        "contentType": "0",
        "category": 2,
        "hidden": false,
        "status": 2,
        "punishFlag": 0,
        "s3KeyFlag": "x-0",
        "storageNode": "m51",
        "createAt": "2024-09-27 09:39:46 +0800 CST",
        "updateAt": "2025-02-24 17:56:24 +0800 CST",
        "thumbnail": "",
        "downloadUrl": ""
      },
      {
        "fileID": 8903127,
        "filename": "2.json.gz",
        "parentFileId": 14663228,
        "parentName": "",
        "type": 0,
        "etag": "46...(过长省略)",
        "size": 221476024,
        "contentType": "0",
        "category": 10,
        "hidden": false,
        "status": 0,
        "punishFlag": 0,
        "s3KeyFlag": "x-0",
        "storageNode": "m50",
        "createAt": "2024-08-16 13:18:09 +0800 CST",
        "updateAt": "2025-02-24 17:56:29 +0800 CST",
        "thumbnail": "",
        "downloadUrl": ""
      },
      {
        "fileID": 5373646,
        "filename": "download.mp4",
        "parentFileId": 14663228,
        "parentName": "",
        "type": 0,
        "etag": "af...(过长省略)",
        "size": 518564433,
        "contentType": "0",
        "category": 2,
        "hidden": false,
        "status": 2,
        "punishFlag": 0,
        "s3KeyFlag": "x-0",
        "storageNode": "m16",
        "createAt": "2024-04-30 11:58:36 +0800 CST",
        "updateAt": "2025-02-24 17:56:45 +0800 CST",
        "thumbnail": "",
        "downloadUrl": ""
      }
        ]
    },
    "x-traceID": "db28f069-2a43-4e0a-8f11-511a3962ab07_kong-db-5898fdd8c6-wnv6h"
}
```



> 更新: 2025-05-19 14:00:32  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/hosdqqax0knovnm2>