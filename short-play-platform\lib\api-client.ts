// API客户端工具
// 提供统一的API调用接口，包含错误处理、重试机制等

import { 
  ApiResponse, 
  ApiConfig, 
  DEFAULT_API_CONFIG, 
  API_ROUTES,
  ApiErrorCode,
  API_ERROR_CODES,
  // 请求参数类型
  GetPlaysParams,
  GetPlayByIdParams,
  GetPlaysByCategoryParams,
  SearchPlaysParams,
  GetPopularPlaysParams,
  GetLatestPlaysParams,
  CreatePlayRequest,
  UpdatePlayRequest,
  DeletePlayParams,
  // 响应类型
  GetPlaysResponse,
  GetPlayByIdResponse,
  GetCategoriesResponse,
  GetPlaysByCategoryResponse,
  SearchPlaysResponse,
  GetPopularPlaysResponse,
  GetLatestPlaysResponse,
  GetStatisticsResponse,
  CreatePlayResponse,
  UpdatePlayResponse,
  DeletePlayResponse,
} from './api-types'

// ============= API客户端类 =============

export class ApiClient {
  private config: ApiConfig

  constructor(config: Partial<ApiConfig> = {}) {
    this.config = { ...DEFAULT_API_CONFIG, ...config }
  }

  // 基础请求方法
  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.config.baseURL}${endpoint}`
    const requestOptions: RequestInit = {
      ...options,
      headers: {
        ...this.config.headers,
        ...options.headers,
      },
    }

    let lastError: Error | null = null
    
    // 重试机制
    for (let attempt = 0; attempt <= this.config.retries; attempt++) {
      try {
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), this.config.timeout)
        
        const response = await fetch(url, {
          ...requestOptions,
          signal: controller.signal,
        })
        
        clearTimeout(timeoutId)
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }
        
        const data = await response.json()
        return data as ApiResponse<T>
        
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error')
        
        if (attempt === this.config.retries) {
          break
        }
        
        // 指数退避
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000))
      }
    }
    
    // 返回错误响应
    return {
      success: false,
      error: {
        code: this.getErrorCode(lastError),
        message: lastError?.message || 'Request failed',
        details: lastError,
      },
    }
  }

  private getErrorCode(error: Error | null): ApiErrorCode {
    if (!error) return API_ERROR_CODES.UNKNOWN_ERROR
    
    if (error.name === 'AbortError') {
      return API_ERROR_CODES.TIMEOUT_ERROR
    }
    
    if (error.message.includes('fetch')) {
      return API_ERROR_CODES.NETWORK_ERROR
    }
    
    return API_ERROR_CODES.UNKNOWN_ERROR
  }

  private buildQueryString(params: Record<string, any>): string {
    const filtered = Object.entries(params)
      .filter(([_, value]) => value !== undefined && value !== null)
      .map(([key, value]) => [key, Array.isArray(value) ? value.join(',') : String(value)])
    
    const searchParams = new URLSearchParams(filtered)
    return searchParams.toString()
  }

  // ============= 短剧相关API =============

  async getPlays(params: GetPlaysParams = {}): Promise<GetPlaysResponse> {
    const queryString = this.buildQueryString(params)
    const endpoint = `${API_ROUTES.PLAYS.LIST}${queryString ? `?${queryString}` : ''}`
    return this.request<GetPlaysResponse['data']>(endpoint)
  }

  async getPlayById(params: GetPlayByIdParams): Promise<GetPlayByIdResponse> {
    const endpoint = API_ROUTES.PLAYS.DETAIL.replace(':id', String(params.id))
    return this.request<GetPlayByIdResponse['data']>(endpoint)
  }

  async searchPlays(params: SearchPlaysParams): Promise<SearchPlaysResponse> {
    const queryString = this.buildQueryString(params)
    const endpoint = `${API_ROUTES.PLAYS.SEARCH}?${queryString}`
    return this.request<SearchPlaysResponse['data']>(endpoint)
  }

  async getPopularPlays(params: GetPopularPlaysParams = {}): Promise<GetPopularPlaysResponse> {
    const queryString = this.buildQueryString(params)
    const endpoint = `${API_ROUTES.PLAYS.POPULAR}${queryString ? `?${queryString}` : ''}`
    return this.request<GetPopularPlaysResponse['data']>(endpoint)
  }

  async getLatestPlays(params: GetLatestPlaysParams = {}): Promise<GetLatestPlaysResponse> {
    const queryString = this.buildQueryString(params)
    const endpoint = `${API_ROUTES.PLAYS.LATEST}${queryString ? `?${queryString}` : ''}`
    return this.request<GetLatestPlaysResponse['data']>(endpoint)
  }

  // ============= 分类相关API =============

  async getCategories(): Promise<GetCategoriesResponse> {
    return this.request<GetCategoriesResponse['data']>(API_ROUTES.CATEGORIES.LIST)
  }

  async getPlaysByCategory(params: GetPlaysByCategoryParams): Promise<GetPlaysByCategoryResponse> {
    const { slug, ...queryParams } = params
    const queryString = this.buildQueryString(queryParams)
    const endpoint = API_ROUTES.CATEGORIES.PLAYS.replace(':slug', slug)
    return this.request<GetPlaysByCategoryResponse['data']>(`${endpoint}${queryString ? `?${queryString}` : ''}`)
  }

  // ============= 统计相关API =============

  async getStatistics(): Promise<GetStatisticsResponse> {
    return this.request<GetStatisticsResponse['data']>(API_ROUTES.STATISTICS)
  }

  // ============= 管理相关API（可选） =============

  async createPlay(data: CreatePlayRequest): Promise<CreatePlayResponse> {
    return this.request<CreatePlayResponse['data']>(API_ROUTES.PLAYS.CREATE, {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async updatePlay(data: UpdatePlayRequest): Promise<UpdatePlayResponse> {
    const endpoint = API_ROUTES.PLAYS.UPDATE.replace(':id', String(data.id))
    const { id, ...updateData } = data
    return this.request<UpdatePlayResponse['data']>(endpoint, {
      method: 'PUT',
      body: JSON.stringify(updateData),
    })
  }

  async deletePlay(params: DeletePlayParams): Promise<DeletePlayResponse> {
    const endpoint = API_ROUTES.PLAYS.DELETE.replace(':id', String(params.id))
    return this.request<DeletePlayResponse['data']>(endpoint, {
      method: 'DELETE',
    })
  }

  // ============= 健康检查 =============

  async healthCheck(): Promise<ApiResponse<{ status: string; timestamp: string }>> {
    return this.request<{ status: string; timestamp: string }>(API_ROUTES.HEALTH)
  }
}

// ============= 默认客户端实例 =============

export const apiClient = new ApiClient()

// ============= 便捷方法 =============

export const api = {
  // 短剧相关
  plays: {
    list: (params?: GetPlaysParams) => apiClient.getPlays(params),
    get: (id: number) => apiClient.getPlayById({ id }),
    search: (params: SearchPlaysParams) => apiClient.searchPlays(params),
    popular: (params?: GetPopularPlaysParams) => apiClient.getPopularPlays(params),
    latest: (params?: GetLatestPlaysParams) => apiClient.getLatestPlays(params),
    create: (data: CreatePlayRequest) => apiClient.createPlay(data),
    update: (data: UpdatePlayRequest) => apiClient.updatePlay(data),
    delete: (id: number) => apiClient.deletePlay({ id }),
  },
  
  // 分类相关
  categories: {
    list: () => apiClient.getCategories(),
    plays: (params: GetPlaysByCategoryParams) => apiClient.getPlaysByCategory(params),
  },
  
  // 统计相关
  statistics: () => apiClient.getStatistics(),
  
  // 健康检查
  health: () => apiClient.healthCheck(),
}

// ============= React Hook 支持（可选） =============

// 简单的数据获取hook示例，可以与 SWR 或 React Query 结合
export function useApiData<T>(
  fetcher: () => Promise<ApiResponse<T>>,
  options: {
    enabled?: boolean
    onSuccess?: (data: T) => void
    onError?: (error: any) => void
  } = {}
) {
  // 这里可以实现基于 SWR 或 React Query 的 hook
  // 暂时返回基础结构
  return {
    data: null as T | null,
    error: null as any,
    isLoading: false,
    mutate: () => {},
  }
}

export default apiClient