import { getCategories, getPlaysByCategory } from "@/lib/data"
import { PlayCard } from "@/components/play-card"
import { notFound } from "next/navigation"
import type { Metadata } from "next"
import { SiteHeader } from "@/components/header"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { ArrowLeft, Grid3X3 } from "lucide-react"
import Link from "next/link"

type Props = {
  params: { slug: string }
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const categories = await getCategories()
  const category = categories.find((cat) => cat.slug === params.slug)

  if (!category) {
    return {
      title: "分类未找到",
    }
  }

  return {
    title: `${category.name}短剧 - 短剧星球`,
    description: `浏览所有${category.name}类型的精彩短剧`,
  }
}

export async function generateStaticParams() {
  const categories = await getCategories()
  return categories.map((category) => ({
    slug: category.slug,
  }))
}

export default async function CategoryPage({ params }: Props) {
  const { slug } = params
  const plays = await getPlaysByCategory(slug)
  const categories = await getCategories()
  const category = categories.find((cat) => cat.slug === slug)

  if (!category) {
    notFound()
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-700">
      <SiteHeader />
      
      <main className="container mx-auto px-4 py-8">
        {/* Back Button */}
        <div className="mb-6">
          <Link href="/">
            <Button variant="ghost" className="mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回首页
            </Button>
          </Link>
        </div>

        {/* Category Header */}
        <div className="text-center py-8 mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Grid3X3 className="h-8 w-8 text-primary" />
            <h1 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
              {category.name}短剧
            </h1>
          </div>
          <p className="text-lg text-muted-foreground mb-4">
            精选{category.name}类型短剧，为您推荐优质内容
          </p>
          <Badge variant="secondary" className="px-4 py-2 text-base">
            共 {plays.length} 部短剧
          </Badge>
        </div>

        {/* Plays Grid */}
        {plays.length > 0 ? (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7 gap-4 md:gap-6">
            {plays.map((play) => (
              <PlayCard key={play.id} play={play} />
            ))}
          </div>
        ) : (
          <div className="text-center py-20">
            <Grid3X3 className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">暂无{category.name}短剧</h3>
            <p className="text-muted-foreground mb-6">
              该分类下暂时没有短剧内容，请关注其他分类或稍后再来查看
            </p>
            <Link href="/">
              <Button>
                浏览全部短剧
              </Button>
            </Link>
          </div>
        )}
      </main>
    </div>
  )
}