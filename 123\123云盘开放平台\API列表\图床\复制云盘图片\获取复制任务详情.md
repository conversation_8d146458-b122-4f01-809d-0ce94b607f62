# 获取复制任务详情

API： GET 域名 + /api/v1/oss/source/copy/process

说明：该接口将会获取图床复制任务执行情况

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#000000;">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## QueryString 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| taskID | string | 必填 | 复制任务ID |


## 返回数据
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| status | int | 必填 | 任务状态:  1进行中,2结束,3失败,4等待 |
| failMsg | string | 必填 | 失败原因 |


## 示例
**请求示例**

```shell
curl --location 'https://open-api.123pan.com/api/v1/oss/source/copy/process?taskID=1ketmmu1000d86isuyq33su100w1bnp6' \
--header 'Content-Type: application/json' \
--header 'Platform: open_platform' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)' \
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
.build();
MediaType mediaType = MediaType.parse("application/json");
RequestBody body = RequestBody.create(mediaType, "");
Request request = new Request.Builder()
.url("https://open-api.123pan.com/api/v1/oss/source/copy/process?taskID=1ketmmu1000d86isuyq33su100w1bnp6")
.method("GET", body)
.addHeader("Content-Type", "application/json")
.addHeader("Platform", "open_platform")
.addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)")
.build();
Response response = client.newCall(request).execute();
```

```javascript
var settings = {
  "url": "https://open-api.123pan.com/api/v1/oss/source/copy/process?taskID=1ketmmu1000d86isuyq33su100w1bnp6",
  "method": "GET",
  "timeout": 0,
  "headers": {
    "Content-Type": "application/json",
    "Platform": "open_platform",
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)"
  },
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
```

```javascript
const axios = require('axios');

let config = {
  method: 'get',
  maxBodyLength: Infinity,
  url: 'https://open-api.123pan.com/api/v1/oss/source/copy/process?taskID=1ketmmu1000d86isuyq33su100w1bnp6',
  headers: { 
    'Content-Type': 'application/json', 
    'Platform': 'open_platform', 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
  }
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});

```

```python
import http.client
import json

conn = http.client.HTTPSConnection("open-api.123pan.com")
payload = ''
headers = {
  'Content-Type': 'application/json',
  'Platform': 'open_platform',
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
}
conn.request("GET", "/api/v1/oss/source/copy/process?taskID=1ketmmu1000d86isuyq33su100w1bnp6", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

**响应示例**

```json
{
    "code": 0,
    "message": "ok",
    "data": {
        "status": 2,
        "failMsg": ""
    },
    "x-traceID": "9c4eea65-23ca-4f37-b305-89a6d7bb1f5a_kong-db-5898fdd8c6-t5pvc"
}
```



> 更新: 2025-03-17 19:16:57  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/rissl4ewklaui4th>