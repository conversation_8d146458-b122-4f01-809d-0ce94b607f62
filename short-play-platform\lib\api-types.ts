// API接口类型定义
// 这个文件定义了前端与后端交互的所有接口结构

// ============= 基础类型定义 =============

export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
  pagination?: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
}

// ============= 短剧相关接口 =============

export interface ShortPlay {
  id: number
  title: string
  posterUrl: string
  description: string
  category: Category
  pan123: CloudLink
  quark: CloudLink
  rating?: number
  views?: number
  updatedAt?: string
  createdAt: string
  tags?: string[]
  status: 'active' | 'inactive' | 'pending'
  episodes?: number
  duration?: number // 总时长，单位：分钟
}

export interface Category {
  id: number
  name: string
  slug: string
  description?: string
  count?: number
  order: number
}

export interface CloudLink {
  link: string
  code?: string
  isActive: boolean
  updatedAt: string
}

// ============= API接口定义 =============

// 短剧列表接口
export interface GetPlaysParams {
  page?: number
  pageSize?: number
  category?: string
  search?: string
  sortBy?: 'latest' | 'popular' | 'rating'
  sortOrder?: 'asc' | 'desc'
  tags?: string[]
}

export interface GetPlaysResponse extends ApiResponse<ShortPlay[]> {}

// 短剧详情接口
export interface GetPlayByIdParams {
  id: number
}

export interface GetPlayByIdResponse extends ApiResponse<ShortPlay> {}

// 分类列表接口
export interface GetCategoriesResponse extends ApiResponse<Category[]> {}

// 分类短剧接口
export interface GetPlaysByCategoryParams {
  slug: string
  page?: number
  pageSize?: number
  sortBy?: 'latest' | 'popular' | 'rating'
}

export interface GetPlaysByCategoryResponse extends ApiResponse<ShortPlay[]> {}

// 搜索接口
export interface SearchPlaysParams {
  query: string
  page?: number
  pageSize?: number
  category?: string
  filters?: {
    minRating?: number
    hasCode?: boolean
    platforms?: ('123' | 'quark')[]
  }
}

export interface SearchPlaysResponse extends ApiResponse<ShortPlay[]> {}

// 热门推荐接口
export interface GetPopularPlaysParams {
  limit?: number
  category?: string
}

export interface GetPopularPlaysResponse extends ApiResponse<ShortPlay[]> {}

// 最新更新接口
export interface GetLatestPlaysParams {
  limit?: number
  category?: string
}

export interface GetLatestPlaysResponse extends ApiResponse<ShortPlay[]> {}

// ============= 统计相关接口 =============

export interface PlayStatistics {
  totalPlays: number
  totalViews: number
  categoryCounts: { [key: string]: number }
  popularTags: string[]
  updateFrequency: {
    today: number
    thisWeek: number
    thisMonth: number
  }
}

export interface GetStatisticsResponse extends ApiResponse<PlayStatistics> {}

// ============= 管理相关接口（可选） =============

export interface CreatePlayRequest {
  title: string
  description: string
  posterUrl: string
  categoryId: number
  pan123?: Omit<CloudLink, 'isActive' | 'updatedAt'>
  quark?: Omit<CloudLink, 'isActive' | 'updatedAt'>
  tags?: string[]
  episodes?: number
  duration?: number
}

export interface CreatePlayResponse extends ApiResponse<ShortPlay> {}

export interface UpdatePlayRequest extends Partial<CreatePlayRequest> {
  id: number
}

export interface UpdatePlayResponse extends ApiResponse<ShortPlay> {}

export interface DeletePlayParams {
  id: number
}

export interface DeletePlayResponse extends ApiResponse<null> {}

// ============= API路由定义 =============

export const API_ROUTES = {
  // 短剧相关
  PLAYS: {
    LIST: '/api/plays',
    DETAIL: '/api/plays/:id',
    SEARCH: '/api/plays/search',
    POPULAR: '/api/plays/popular',
    LATEST: '/api/plays/latest',
    CREATE: '/api/plays',
    UPDATE: '/api/plays/:id',
    DELETE: '/api/plays/:id',
  },
  
  // 分类相关
  CATEGORIES: {
    LIST: '/api/categories',
    PLAYS: '/api/categories/:slug/plays',
  },
  
  // 统计相关
  STATISTICS: '/api/statistics',
  
  // 其他
  HEALTH: '/api/health',
} as const

// ============= 请求配置 =============

export interface ApiConfig {
  baseURL: string
  timeout: number
  retries: number
  headers: Record<string, string>
}

export const DEFAULT_API_CONFIG: ApiConfig = {
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001',
  timeout: 10000,
  retries: 3,
  headers: {
    'Content-Type': 'application/json',
  },
}

// ============= 错误码定义 =============

export const API_ERROR_CODES = {
  // 通用错误
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  
  // 业务错误
  PLAY_NOT_FOUND: 'PLAY_NOT_FOUND',
  CATEGORY_NOT_FOUND: 'CATEGORY_NOT_FOUND',
  INVALID_PARAMETERS: 'INVALID_PARAMETERS',
  
  // 服务器错误
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
} as const

export type ApiErrorCode = typeof API_ERROR_CODES[keyof typeof API_ERROR_CODES]