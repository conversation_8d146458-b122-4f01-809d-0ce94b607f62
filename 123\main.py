#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import argparse
import json
from pathlib import Path
from upload_share import YunPan123Client

def print_success(message):
    """打印成功信息"""
    try:
        print(f"✅ {message}")
    except UnicodeEncodeError:
        print(f"[SUCCESS] {message}")

def print_error(message):
    """打印错误信息"""
    try:
        print(f"❌ {message}")
    except UnicodeEncodeError:
        print(f"[ERROR] {message}")

def print_info(message):
    """打印普通信息"""
    try:
        print(f"ℹ️  {message}")
    except UnicodeEncodeError:
        print(f"[INFO] {message}")

def format_file_size(size_bytes):
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024.0 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    return f"{size_bytes:.1f}{size_names[i]}"

def validate_file_path(file_path):
    """验证文件路径"""
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")
    
    if not os.path.isfile(file_path):
        raise ValueError(f"路径不是文件: {file_path}")
    
    file_size = os.path.getsize(file_path)
    if file_size == 0:
        raise ValueError(f"文件为空: {file_path}")
    
    return file_size

def upload_and_get_share_link(file_path, directory_path="/", share_name=None, 
                             expire_days=None, extract_code=None, 
                             auto_generate_code=None, config_path="config.json"):
    """上传文件并获取分享链接"""
    
    try:
        # 验证文件
        file_size = validate_file_path(file_path)
        file_name = os.path.basename(file_path)
        
        print_info(f"准备上传文件: {file_name}")
        print_info(f"文件大小: {format_file_size(file_size)}")
        print_info(f"目标目录: {directory_path}")
        
        # 初始化客户端
        client = YunPan123Client(config_path)
        
        # 设置分享名称
        if share_name is None:
            share_name = f"分享文件: {file_name}"
        
        print_info(f"分享名称: {share_name}")
        
        # 上传并分享
        result = client.upload_and_share(
            file_path=file_path,
            directory_path=directory_path,
            share_name=share_name,
            expire_days=expire_days,
            extract_code=extract_code,
            auto_generate_code=auto_generate_code
        )
        
        # 显示结果
        print("\n" + "="*50)
        print_success("上传并分享成功！")
        print("="*50)
        try:
            print(f"📁 文件名称: {result['fileName']}")
            print(f"📂 上传目录: {result['directory']}")
            print(f"🔗 分享链接: {result['shareUrl']}")
            
            if result['extractCode']:
                print(f"🔑 提取码: {result['extractCode']}")
            else:
                print("🔑 提取码: 无")
            
            expire_text = f"{result['expireDays']}天" if result['expireDays'] > 0 else "永久"
            print(f"⏰ 有效期: {expire_text}")
            print(f"🆔 文件ID: {result['fileID']}")
            print(f"🆔 分享ID: {result['shareID']}")
        except UnicodeEncodeError:
            print(f"文件名称: {result['fileName']}")
            print(f"上传目录: {result['directory']}")
            print(f"分享链接: {result['shareUrl']}")
            
            if result['extractCode']:
                print(f"提取码: {result['extractCode']}")
            else:
                print("提取码: 无")
            
            expire_text = f"{result['expireDays']}天" if result['expireDays'] > 0 else "永久"
            print(f"有效期: {expire_text}")
            print(f"文件ID: {result['fileID']}")
            print(f"分享ID: {result['shareID']}")
        print("="*50)
        
        # 保存到剪贴板（如果可用）
        try:
            import pyperclip
            share_text = result['shareUrl']
            if result['extractCode']:
                share_text += f" 提取码: {result['extractCode']}"
            pyperclip.copy(share_text)
            print_info("分享信息已复制到剪贴板")
        except ImportError:
            print_info("提示: 安装 pyperclip 可自动复制分享信息到剪贴板")
        except Exception:
            pass
        
        return result
        
    except Exception as e:
        print_error(f"操作失败: {e}")
        return None

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="123网盘上传分享工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s file.txt                                    # 上传到根目录，使用默认设置
  %(prog)s file.txt -d "/我的文档"                      # 上传到指定目录
  %(prog)s file.txt -n "重要文件分享"                   # 自定义分享名称
  %(prog)s file.txt -e 30                              # 设置30天有效期
  %(prog)s file.txt -p "abc123"                        # 自定义提取码
  %(prog)s file.txt -d "/文档" -n "测试" -e 7 -p "1234" # 完整参数示例
        """
    )
    
    parser.add_argument(
        "file_path",
        help="要上传的文件路径"
    )
    
    parser.add_argument(
        "-d", "--directory",
        default="/",
        help="云盘目录路径 (默认: /)"
    )
    
    parser.add_argument(
        "-n", "--name",
        help="分享链接名称 (默认: 分享文件: 文件名)"
    )
    
    parser.add_argument(
        "-e", "--expire",
        type=int,
        choices=[0, 1, 7, 30],
        help="有效期天数 (0=永久, 1=1天, 7=7天, 30=30天, 默认使用配置文件设置)"
    )
    
    parser.add_argument(
        "-p", "--password",
        help="自定义提取码"
    )
    
    parser.add_argument(
        "--no-auto-code",
        action="store_true",
        help="不自动生成提取码"
    )
    
    parser.add_argument(
        "-c", "--config",
        default="config.json",
        help="配置文件路径 (默认: config.json)"
    )
    
    parser.add_argument(
        "--version",
        action="version",
        version="123网盘上传分享工具 v1.0"
    )
    
    args = parser.parse_args()
    
    # 检查配置文件
    if not os.path.exists(args.config):
        print_error(f"配置文件不存在: {args.config}")
        print_info("请确保config.json文件存在并配置了正确的API凭证")
        sys.exit(1)
    
    # 转换参数
    auto_generate_code = None if not args.no_auto_code else False
    
    # 执行上传
    result = upload_and_get_share_link(
        file_path=args.file_path,
        directory_path=args.directory,
        share_name=args.name,
        expire_days=args.expire,
        extract_code=args.password,
        auto_generate_code=auto_generate_code,
        config_path=args.config
    )
    
    if result:
        sys.exit(0)
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()