#!/usr/bin/env python3
"""
测试动态网盘客户端的去重逻辑
"""

import pymysql
from datetime import datetime

# 数据库配置
db_config = {
    'host': '************',
    'user': 'sql23721_duanji',
    'password': '507877550@lihao',
    'database': 'sql23721_duanji',
    'charset': 'utf8mb4'
}

def setup_test_data(scenario):
    """设置测试数据"""
    print(f"=== 设置测试场景: {scenario} ===")
    
    try:
        connection = pymysql.connect(**db_config)
        
        with connection.cursor() as cursor:
            # 查找"开到荼靡"的play_id
            cursor.execute("SELECT id FROM play WHERE title = '开到荼靡'")
            play = cursor.fetchone()
            
            if not play:
                print("❌ 未找到'开到荼靡'短剧")
                return
            
            play_id = play[0]
            current_time = datetime.now()
            
            # 清理现有链接
            cursor.execute("DELETE FROM play_link WHERE play_id = %s", (play_id,))
            
            if scenario == "no_links":
                # 无链接
                pass
            elif scenario == "quark_only":
                # 只有quark链接
                cursor.execute("""
                    INSERT INTO play_link (play_id, link, code, platform, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """, (play_id, "https://pan.quark.cn/s/test123", "", "quark", current_time, current_time))
            elif scenario == "123pan_only":
                # 只有123pan链接
                cursor.execute("""
                    INSERT INTO play_link (play_id, link, code, platform, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """, (play_id, "https://www.123pan.com/s/test456", "test", "123pan", current_time, current_time))
            elif scenario == "both_links":
                # 两个链接都有
                cursor.execute("""
                    INSERT INTO play_link (play_id, link, code, platform, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """, (play_id, "https://pan.quark.cn/s/test123", "", "quark", current_time, current_time))
                cursor.execute("""
                    INSERT INTO play_link (play_id, link, code, platform, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """, (play_id, "https://www.123pan.com/s/test456", "test", "123pan", current_time, current_time))
            
            connection.commit()
            print(f"✅ 设置完成: {scenario}")
            
        connection.close()
        
    except Exception as e:
        print(f"❌ 设置失败: {e}")

def test_dedup_logic(available_clients, scenario_name):
    """测试去重逻辑"""
    print(f"\n=== 测试场景: {scenario_name} ===")
    print(f"🔍 模拟可用网盘: {available_clients}")
    
    try:
        connection = pymysql.connect(**db_config)
        
        with connection.cursor() as cursor:
            # 查找"开到荼靡"的play_id
            cursor.execute("SELECT id FROM play WHERE title = '开到荼靡'")
            play = cursor.fetchone()
            
            if not play:
                print("❌ 未找到'开到荼靡'短剧")
                return
            
            play_id = play[0]
            
            # 查看现有链接
            cursor.execute("""
                SELECT platform, link FROM play_link 
                WHERE play_id = %s AND link IS NOT NULL AND link != ''
            """, (play_id,))
            existing_links = cursor.fetchall()
            
            platforms = [link[0] for link in existing_links]
            
            print(f"📋 已有链接平台: {platforms}")
            
            # 模拟去重逻辑
            missing_platforms = [client for client in available_clients if client not in platforms]
            
            print(f"❓ 缺失平台: {missing_platforms}")
            
            if not missing_platforms:
                print("✅ 所有可用网盘都有链接，应该跳过")
                return "skip"
            else:
                print(f"📤 需要补充{', '.join(missing_platforms)}网盘链接")
                return "continue"
                
        connection.close()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return "error"

def run_all_tests():
    """运行所有测试场景"""
    print("=== 动态网盘客户端去重逻辑测试 ===\n")
    
    scenarios = [
        ("no_links", "无链接"),
        ("quark_only", "只有quark链接"),
        ("123pan_only", "只有123pan链接"),
        ("both_links", "两个链接都有")
    ]
    
    client_scenarios = [
        (["quark", "123pan"], "两个网盘都可用"),
        (["quark"], "只有quark可用"),
        (["123pan"], "只有123pan可用"),
        ([], "没有网盘可用")
    ]
    
    results = {}
    
    for data_scenario, data_name in scenarios:
        setup_test_data(data_scenario)
        results[data_scenario] = {}
        
        for clients, client_name in client_scenarios:
            result = test_dedup_logic(clients, f"{data_name} + {client_name}")
            results[data_scenario][tuple(clients)] = result
    
    # 输出测试结果总结
    print("\n" + "="*60)
    print("测试结果总结")
    print("="*60)
    
    for data_scenario, data_name in scenarios:
        print(f"\n📊 {data_name}:")
        for clients, client_name in client_scenarios:
            result = results[data_scenario][tuple(clients)]
            status = "✅" if result in ["skip", "continue"] else "❌"
            print(f"  {status} {client_name}: {result}")

if __name__ == "__main__":
    run_all_tests()
