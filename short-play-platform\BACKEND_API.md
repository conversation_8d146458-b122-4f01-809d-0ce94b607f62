# 短剧分享平台 - 后端API对接文档

这个文档为后端开发者提供了完整的API实现指南，包括数据结构、接口定义、实现示例等。

## 📋 目录

- [技术栈建议](#技术栈建议)
- [数据库设计](#数据库设计)
- [API接口规范](#api接口规范)
- [实现示例](#实现示例)
- [部署指南](#部署指南)
- [性能优化](#性能优化)

## 🔧 技术栈建议

### 推荐技术栈
- **Node.js + Express/Fastify**: 快速开发，与前端技术栈一致
- **Python + FastAPI/Django**: 强大的数据处理能力
- **Java + Spring Boot**: 企业级应用，性能稳定
- **Go + Gin/Echo**: 高性能，部署简单

### 数据库
- **主数据库**: PostgreSQL / MySQL
- **缓存**: Redis
- **搜索**: Elasticsearch (可选)

## 📊 数据库设计

### 核心表结构

#### 1. 分类表 (categories)
```sql
CREATE TABLE categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    slug VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    order_index INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. 短剧表 (plays)
```sql
CREATE TABLE plays (
    id SERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    poster_url VARCHAR(500),
    category_id INTEGER REFERENCES categories(id),
    rating DECIMAL(3,2) CHECK (rating >= 0 AND rating <= 5),
    views INTEGER DEFAULT 0,
    episodes INTEGER,
    duration INTEGER, -- 总时长(分钟)
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 3. 网盘链接表 (play_links)
```sql
CREATE TABLE play_links (
    id SERIAL PRIMARY KEY,
    play_id INTEGER REFERENCES plays(id) ON DELETE CASCADE,
    platform VARCHAR(20) NOT NULL, -- '123pan', 'quark'
    link VARCHAR(500) NOT NULL,
    code VARCHAR(20),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(play_id, platform)
);
```

#### 4. 标签表 (tags)
```sql
CREATE TABLE tags (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 5. 短剧标签关联表 (play_tags)
```sql
CREATE TABLE play_tags (
    play_id INTEGER REFERENCES plays(id) ON DELETE CASCADE,
    tag_id INTEGER REFERENCES tags(id) ON DELETE CASCADE,
    PRIMARY KEY (play_id, tag_id)
);
```

### 索引优化
```sql
-- 查询优化索引
CREATE INDEX idx_plays_category_id ON plays(category_id);
CREATE INDEX idx_plays_status ON plays(status);
CREATE INDEX idx_plays_rating ON plays(rating DESC);
CREATE INDEX idx_plays_views ON plays(views DESC);
CREATE INDEX idx_plays_updated_at ON plays(updated_at DESC);
CREATE INDEX idx_plays_title ON plays USING gin(to_tsvector('chinese', title));
CREATE INDEX idx_play_links_play_id ON play_links(play_id);
CREATE INDEX idx_play_links_platform ON play_links(platform);
```

## 🚀 API接口规范

### 统一响应格式
```typescript
interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
  pagination?: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
}
```

### 主要接口列表

#### 1. 短剧列表 `GET /api/plays`
**查询参数**:
```typescript
{
  page?: number        // 页码，默认1
  pageSize?: number    // 每页数量，默认20
  category?: string    // 分类slug
  search?: string      // 搜索关键词
  sortBy?: 'latest' | 'popular' | 'rating'  // 排序方式
  sortOrder?: 'asc' | 'desc'  // 排序顺序
  tags?: string[]      // 标签筛选
}
```

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "重生之我在霸总文里当保姆",
      "posterUrl": "https://example.com/poster1.jpg",
      "description": "...",
      "category": {
        "id": 2,
        "name": "重生",
        "slug": "rebirth"
      },
      "pan123": {
        "link": "https://www.123pan.com/s/abc-123",
        "code": "a1b2",
        "isActive": true,
        "updatedAt": "2024-07-28T10:00:00Z"
      },
      "quark": {
        "link": "https://pan.quark.cn/s/xyz-789",
        "code": "x7y8",
        "isActive": true,
        "updatedAt": "2024-07-28T10:00:00Z"
      },
      "rating": 4.5,
      "views": 12580,
      "tags": ["重生", "霸总", "甜宠"],
      "episodes": 80,
      "duration": 120,
      "createdAt": "2024-07-20T10:00:00Z",
      "updatedAt": "2024-07-28T10:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "pageSize": 20,
    "total": 100,
    "totalPages": 5
  }
}
```

#### 2. 短剧详情 `GET /api/plays/:id`
返回单个短剧的完整信息。

#### 3. 搜索短剧 `GET /api/plays/search`
**查询参数**:
```typescript
{
  query: string        // 必须，搜索关键词
  page?: number
  pageSize?: number
  category?: string
  filters?: {
    minRating?: number
    hasCode?: boolean
    platforms?: ('123' | 'quark')[]
  }
}
```

#### 4. 热门短剧 `GET /api/plays/popular`
**查询参数**:
```typescript
{
  limit?: number       // 返回数量，默认10
  category?: string    // 分类筛选
}
```

#### 5. 最新短剧 `GET /api/plays/latest`
参数同热门短剧接口。

#### 6. 分类列表 `GET /api/categories`
返回所有分类信息。

#### 7. 分类短剧 `GET /api/categories/:slug/plays`
返回指定分类下的短剧列表。

#### 8. 统计信息 `GET /api/statistics`
```json
{
  "success": true,
  "data": {
    "totalPlays": 100,
    "totalViews": 150000,
    "categoryCounts": {
      "rebirth": 12,
      "urban": 8,
      "historical": 15
    },
    "popularTags": ["重生", "都市", "古装"],
    "updateFrequency": {
      "today": 5,
      "thisWeek": 15,
      "thisMonth": 45
    }
  }
}
```

## 💻 实现示例

### Node.js + Express 示例

```javascript
// app.js
const express = require('express');
const cors = require('cors');
const rateLimit = require('express-rate-limit');

const app = express();

// 中间件
app.use(cors());
app.use(express.json());
app.use(rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100 // 限制每个IP 100个请求
}));

// 路由
app.use('/api/plays', require('./routes/plays'));
app.use('/api/categories', require('./routes/categories'));
app.use('/api/statistics', require('./routes/statistics'));

// 错误处理
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    error: {
      code: 'INTERNAL_SERVER_ERROR',
      message: 'Something went wrong!'
    }
  });
});

const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
```

```javascript
// routes/plays.js
const express = require('express');
const router = express.Router();
const playsController = require('../controllers/playsController');

router.get('/', playsController.getPlays);
router.get('/search', playsController.searchPlays);
router.get('/popular', playsController.getPopularPlays);
router.get('/latest', playsController.getLatestPlays);
router.get('/:id', playsController.getPlayById);

module.exports = router;
```

```javascript
// controllers/playsController.js
const db = require('../db');
const { validationResult } = require('express-validator');

exports.getPlays = async (req, res) => {
  try {
    const { page = 1, pageSize = 20, category, search, sortBy = 'latest', sortOrder = 'desc' } = req.query;
    
    let query = `
      SELECT p.*, c.name as category_name, c.slug as category_slug,
             array_agg(DISTINCT t.name) as tags,
             json_build_object(
               'link', pl1.link, 
               'code', pl1.code, 
               'isActive', pl1.is_active,
               'updatedAt', pl1.updated_at
             ) as pan123,
             json_build_object(
               'link', pl2.link, 
               'code', pl2.code, 
               'isActive', pl2.is_active,
               'updatedAt', pl2.updated_at
             ) as quark
      FROM plays p
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN play_tags pt ON p.id = pt.play_id
      LEFT JOIN tags t ON pt.tag_id = t.id
      LEFT JOIN play_links pl1 ON p.id = pl1.play_id AND pl1.platform = '123pan'
      LEFT JOIN play_links pl2 ON p.id = pl2.play_id AND pl2.platform = 'quark'
      WHERE p.status = 'active'
    `;
    
    const params = [];
    let paramIndex = 1;
    
    // 分类筛选
    if (category && category !== 'all') {
      query += ` AND c.slug = $${paramIndex}`;
      params.push(category);
      paramIndex++;
    }
    
    // 搜索筛选
    if (search) {
      query += ` AND (p.title ILIKE $${paramIndex} OR p.description ILIKE $${paramIndex})`;
      params.push(`%${search}%`);
      paramIndex++;
    }
    
    query += ` GROUP BY p.id, c.name, c.slug, pl1.link, pl1.code, pl1.is_active, pl1.updated_at, pl2.link, pl2.code, pl2.is_active, pl2.updated_at`;
    
    // 排序
    const sortMap = {
      'latest': 'p.updated_at',
      'popular': 'p.views',
      'rating': 'p.rating'
    };
    query += ` ORDER BY ${sortMap[sortBy] || sortMap.latest} ${sortOrder.toUpperCase()}`;
    
    // 分页
    const offset = (page - 1) * pageSize;
    query += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    params.push(pageSize, offset);
    
    const result = await db.query(query, params);
    
    // 获取总数
    let countQuery = `SELECT COUNT(*) FROM plays p LEFT JOIN categories c ON p.category_id = c.id WHERE p.status = 'active'`;
    const countParams = [];
    let countParamIndex = 1;
    
    if (category && category !== 'all') {
      countQuery += ` AND c.slug = $${countParamIndex}`;
      countParams.push(category);
      countParamIndex++;
    }
    
    if (search) {
      countQuery += ` AND (p.title ILIKE $${countParamIndex} OR p.description ILIKE $${countParamIndex})`;
      countParams.push(`%${search}%`);
    }
    
    const countResult = await db.query(countQuery, countParams);
    const total = parseInt(countResult.rows[0].count);
    
    const plays = result.rows.map(row => ({
      id: row.id,
      title: row.title,
      description: row.description,
      posterUrl: row.poster_url,
      category: {
        name: row.category_name,
        slug: row.category_slug
      },
      pan123: row.pan123,
      quark: row.quark,
      rating: row.rating ? parseFloat(row.rating) : null,
      views: row.views,
      tags: row.tags ? row.tags.filter(tag => tag !== null) : [],
      episodes: row.episodes,
      duration: row.duration,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    }));
    
    res.json({
      success: true,
      data: plays,
      pagination: {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    });
    
  } catch (error) {
    console.error('Error getting plays:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to get plays'
      }
    });
  }
};
```

### Python + FastAPI 示例

```python
# main.py
from fastapi import FastAPI, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, List
import asyncpg
import os

app = FastAPI(title="短剧分享平台 API", version="1.0.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型
class Category(BaseModel):
    id: int
    name: str
    slug: str

class CloudLink(BaseModel):
    link: str
    code: Optional[str] = None
    isActive: bool
    updatedAt: str

class ShortPlay(BaseModel):
    id: int
    title: str
    description: str
    posterUrl: str
    category: Category
    pan123: CloudLink
    quark: CloudLink
    rating: Optional[float] = None
    views: Optional[int] = None
    tags: Optional[List[str]] = []
    episodes: Optional[int] = None
    duration: Optional[int] = None
    createdAt: str
    updatedAt: str

class ApiResponse(BaseModel):
    success: bool
    data: Optional[any] = None
    error: Optional[dict] = None
    pagination: Optional[dict] = None

@app.get("/api/plays", response_model=ApiResponse)
async def get_plays(
    page: int = Query(1, ge=1),
    pageSize: int = Query(20, ge=1, le=100),
    category: Optional[str] = None,
    search: Optional[str] = None,
    sortBy: str = Query('latest', regex='^(latest|popular|rating)$'),
    sortOrder: str = Query('desc', regex='^(asc|desc)$')
):
    try:
        # 数据库连接
        conn = await asyncpg.connect(os.getenv('DATABASE_URL'))
        
        # 构建查询
        base_query = """
            SELECT p.*, c.name as category_name, c.slug as category_slug,
                   array_agg(DISTINCT t.name) as tags,
                   pl1.link as pan123_link, pl1.code as pan123_code, 
                   pl1.is_active as pan123_active, pl1.updated_at as pan123_updated,
                   pl2.link as quark_link, pl2.code as quark_code,
                   pl2.is_active as quark_active, pl2.updated_at as quark_updated
            FROM plays p
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN play_tags pt ON p.id = pt.play_id
            LEFT JOIN tags t ON pt.tag_id = t.id
            LEFT JOIN play_links pl1 ON p.id = pl1.play_id AND pl1.platform = '123pan'
            LEFT JOIN play_links pl2 ON p.id = pl2.play_id AND pl2.platform = 'quark'
            WHERE p.status = 'active'
        """
        
        params = []
        param_count = 0
        
        if category and category != 'all':
            param_count += 1
            base_query += f" AND c.slug = ${param_count}"
            params.append(category)
            
        if search:
            param_count += 1
            base_query += f" AND (p.title ILIKE ${param_count} OR p.description ILIKE ${param_count})"
            params.append(f"%{search}%")
        
        base_query += " GROUP BY p.id, c.name, c.slug, pl1.link, pl1.code, pl1.is_active, pl1.updated_at, pl2.link, pl2.code, pl2.is_active, pl2.updated_at"
        
        # 排序
        sort_map = {
            'latest': 'p.updated_at',
            'popular': 'p.views',
            'rating': 'p.rating'
        }
        base_query += f" ORDER BY {sort_map[sortBy]} {sortOrder.upper()}"
        
        # 分页
        offset = (page - 1) * pageSize
        param_count += 2
        base_query += f" LIMIT ${param_count - 1} OFFSET ${param_count}"
        params.extend([pageSize, offset])
        
        # 执行查询
        rows = await conn.fetch(base_query, *params)
        
        # 格式化结果
        plays = []
        for row in rows:
            play = {
                'id': row['id'],
                'title': row['title'],
                'description': row['description'],
                'posterUrl': row['poster_url'],
                'category': {
                    'name': row['category_name'],
                    'slug': row['category_slug']
                },
                'pan123': {
                    'link': row['pan123_link'] or '',
                    'code': row['pan123_code'] or '',
                    'isActive': row['pan123_active'] or False,
                    'updatedAt': str(row['pan123_updated']) if row['pan123_updated'] else ''
                },
                'quark': {
                    'link': row['quark_link'] or '',
                    'code': row['quark_code'] or '',
                    'isActive': row['quark_active'] or False,
                    'updatedAt': str(row['quark_updated']) if row['quark_updated'] else ''
                },
                'rating': float(row['rating']) if row['rating'] else None,
                'views': row['views'],
                'tags': [tag for tag in row['tags'] if tag] if row['tags'] else [],
                'episodes': row['episodes'],
                'duration': row['duration'],
                'createdAt': str(row['created_at']),
                'updatedAt': str(row['updated_at'])
            }
            plays.append(play)
        
        # 获取总数
        count_query = "SELECT COUNT(*) FROM plays p LEFT JOIN categories c ON p.category_id = c.id WHERE p.status = 'active'"
        count_params = []
        count_param_count = 0
        
        if category and category != 'all':
            count_param_count += 1
            count_query += f" AND c.slug = ${count_param_count}"
            count_params.append(category)
            
        if search:
            count_param_count += 1
            count_query += f" AND (p.title ILIKE ${count_param_count} OR p.description ILIKE ${count_param_count})"
            count_params.append(f"%{search}%")
        
        total = await conn.fetchval(count_query, *count_params)
        
        await conn.close()
        
        return {
            'success': True,
            'data': plays,
            'pagination': {
                'page': page,
                'pageSize': pageSize,
                'total': total,
                'totalPages': (total + pageSize - 1) // pageSize
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail={
            'success': False,
            'error': {
                'code': 'INTERNAL_SERVER_ERROR',
                'message': str(e)
            }
        })

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=3001)
```

## 🚀 部署指南

### Docker 部署示例

```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 3001

CMD ["node", "app.js"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  api:
    build: .
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=**********************************/shortplay
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=shortplay
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

### 环境变量配置

```bash
# .env
NODE_ENV=production
PORT=3001

# 数据库
DATABASE_URL=postgresql://user:password@localhost:5432/shortplay

# Redis缓存
REDIS_URL=redis://localhost:6379

# 安全配置
JWT_SECRET=your-jwt-secret-key
API_RATE_LIMIT=100

# 文件上传
UPLOAD_PATH=/uploads
MAX_FILE_SIZE=10485760

# 日志
LOG_LEVEL=info
LOG_FILE=/var/log/shortplay-api.log
```

## ⚡ 性能优化

### 1. 数据库优化
- 创建适当的索引
- 使用连接池
- 实现读写分离
- 定期维护和优化查询

### 2. 缓存策略
```javascript
// Redis缓存示例
const redis = require('redis');
const client = redis.createClient();

// 缓存热门数据
exports.getPopularPlaysWithCache = async (req, res) => {
  const cacheKey = 'popular_plays';
  
  try {
    // 先尝试从缓存获取
    const cached = await client.get(cacheKey);
    if (cached) {
      return res.json(JSON.parse(cached));
    }
    
    // 缓存未命中，从数据库查询
    const plays = await getPopularPlaysFromDB();
    
    // 存入缓存，过期时间10分钟
    await client.setex(cacheKey, 600, JSON.stringify(plays));
    
    res.json(plays);
  } catch (error) {
    console.error('Cache error:', error);
    // 缓存失败时直接查询数据库
    const plays = await getPopularPlaysFromDB();
    res.json(plays);
  }
};
```

### 3. API优化
- 实现响应压缩
- 使用CDN加速静态资源
- 实现API版本控制
- 添加请求验证和清理

### 4. 监控和日志
```javascript
// 日志中间件
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});

app.use((req, res, next) => {
  logger.info(`${req.method} ${req.url}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  });
  next();
});
```

## 📝 注意事项

1. **安全性**
   - 实现输入验证和清理
   - 使用参数化查询防止SQL注入
   - 添加API限流
   - 实现错误处理，避免敏感信息泄露

2. **可扩展性**
   - 使用微服务架构
   - 实现水平扩展
   - 考虑负载均衡
   - 实现健康检查

3. **维护性**
   - 添加完整的API文档
   - 实现自动化测试
   - 使用版本控制
   - 实现监控和告警

4. **合规性**
   - 遵守数据保护法规
   - 实现用户隐私保护
   - 添加内容审核机制
   - 考虑版权保护

---

## 📞 技术支持

如有任何问题，请联系前端开发团队：
- 邮箱: <EMAIL>
- 技术文档: https://docs.shortplay.com
- API测试: https://api.shortplay.com/docs