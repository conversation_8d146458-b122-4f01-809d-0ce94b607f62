# 123网盘上传分享工具

基于123网盘开放平台API实现的文件上传和分享链接创建工具。

## 功能特性

- ✅ 文件上传到指定目录（支持自动创建目录）
- ✅ 智能分片上传（大文件支持）
- ✅ 文件秒传检测
- ✅ 创建分享链接
- ✅ 随机生成提取码
- ✅ 灵活的有效期设置
- ✅ 一键上传并分享
- ✅ 配置文件管理

## 安装依赖

```bash
pip install requests
```

## 配置

### 1. 获取API凭证

访问 [123网盘开放平台](https://open.123pan.com/) 注册并创建应用，获取 `client_id` 和 `client_secret`。

### 2. 编辑配置文件

编辑 `config.json` 文件：

```json
{
    "client_id": "your_client_id_here",
    "client_secret": "your_client_secret_here",
    "base_url": "https://open-api.123pan.com",
    "default_settings": {
        "expire_days": 7,
        "auto_generate_code": true,
        "extract_code_length": 4,
        "chunk_size": 4096,
        "token_refresh_margin": 300
    }
}
```

**配置项说明：**
- `client_id`: 开放平台应用ID
- `client_secret`: 开放平台应用密钥
- `base_url`: API基础URL
- `default_settings`: 默认设置
  - `expire_days`: 默认有效期天数
  - `auto_generate_code`: 是否自动生成提取码
  - `extract_code_length`: 提取码长度
  - `chunk_size`: 文件读取块大小
  - `token_refresh_margin`: token提前刷新时间（秒）

## 使用示例

### 基本使用

```python
from upload_share import YunPan123Client

# 使用配置文件初始化客户端
client = YunPan123Client("config.json")

# 上传文件并创建分享链接（使用默认配置）
result = client.upload_and_share(
    file_path="test.txt",
    directory_path="/我的文档",
    share_name="测试分享"
)

print(f"分享链接: {result['shareUrl']}")
print(f"提取码: {result['extractCode']}")
```

### 自定义配置

```python
# 覆盖默认配置
result = client.upload_and_share(
    file_path="document.pdf",
    directory_path="/重要文件",
    share_name="重要文档",
    expire_days=30,  # 覆盖默认有效期
    extract_code="custom123",  # 自定义提取码
    auto_generate_code=False  # 不自动生成提取码
)
```

### 高级用法

```python
# 上传到指定目录
file_id = client.upload_file("document.pdf", "/重要文件/项目资料")

# 创建分享链接（多文件）
share_info = client.create_share_link(
    file_ids=[file_id1, file_id2],
    share_name="多文件分享",
    expire_days=30
)

# 自动创建目录结构
parent_id = client.find_or_create_directory_path("/项目/2024/文档")

# 生成随机提取码
extract_code = client.generate_random_extract_code(6)  # 6位提取码
```

## API参数说明

### YunPan123Client

**初始化参数:**
- `config_path` (str): 配置文件路径，默认为 "config.json"

### upload_and_share

**参数:**
- `file_path` (str): 本地文件路径
- `directory_path` (str): 云盘目录路径，默认为根目录"/"
- `share_name` (str, optional): 分享链接名称
- `expire_days` (int, optional): 有效期天数，可选值: 1, 7, 30, 0(永久)，默认使用配置文件设置
- `extract_code` (str, optional): 自定义提取码
- `auto_generate_code` (bool, optional): 是否自动生成提取码，默认使用配置文件设置

**返回值:**
```python
{
    "fileID": 12345,
    "fileName": "test.txt",
    "directory": "/我的文档",
    "shareID": 87187530,
    "shareKey": "PvitVv-nPeLH",
    "shareUrl": "https://www.123pan.com/s/PvitVv-nPeLH",
    "extractCode": "ab3c",
    "expireDays": 7
}
```

## 错误处理

所有方法都会抛出异常，建议使用try-catch处理：

```python
try:
    result = client.upload_and_share("test.txt")
    print("上传成功")
except Exception as e:
    print(f"上传失败: {e}")
```

## 文件结构

```
123/
├── config.json          # 配置文件
├── upload_share.py      # 主程序
└── README.md           # 说明文档
```

## 注意事项

1. **配置文件安全**: 请妥善保管 `config.json` 文件，不要将其提交到版本控制系统
2. **access_token管理**: access_token会自动管理和刷新
3. **大文件上传**: 大文件会自动分片上传，支持断点续传（API层面）
4. **提取码生成**: 排除了容易混淆的字符（如0、O、1、l等）
5. **目录自动创建**: 目录路径会自动创建
6. **频率限制**: 分享链接创建有访问频率限制，请合理使用

## 许可证

MIT License