# 从云盘空间上传

<font style="color:rgb(38, 38, 38);">API： POST 域名 + /api/v1/transcode/upload/from_cloud_disk</font>

## <font style="color:rgb(38, 38, 38);">Header 参数</font>
| **<font style="color:rgb(38, 38, 38);">名称</font>** | **<font style="color:rgb(38, 38, 38);">类型</font>** | **<font style="color:rgb(38, 38, 38);">是否必填</font>** | **<font style="color:rgb(38, 38, 38);">说明</font>** |
| :---: | :---: | :---: | :---: |
| <font style="color:rgb(38, 38, 38);">Authorization</font> | <font style="color:rgb(38, 38, 38);">string</font> | <font style="color:rgb(0, 0, 0);">必填</font><font style="color:rgb(38, 38, 38);"></font> | <font style="color:rgb(38, 38, 38);">鉴权access_token</font> |
| <font style="color:rgb(38, 38, 38);">Platform</font> | <font style="color:rgb(38, 38, 38);">string</font> | <font style="color:rgb(38, 38, 38);">必填</font> | <font style="color:rgb(38, 38, 38);">固定为:open_platform</font> |


## **<font style="color:rgb(38, 38, 38);">Body 参数</font>**
| **<font style="color:rgb(38, 38, 38);">名称</font>**<font style="color:rgb(38, 38, 38);"></font> | **<font style="color:rgb(38, 38, 38);">类型</font>**<font style="color:rgb(38, 38, 38);"></font> | **<font style="color:rgb(38, 38, 38);">是否必填</font>**<font style="color:rgb(38, 38, 38);"></font> | **<font style="color:rgb(38, 38, 38);">说明</font>**<font style="color:rgb(38, 38, 38);"></font> |
| :---: | :---: | :---: | :---: |
| <font style="color:rgb(38, 38, 38);">fileId</font> | []objet | <font style="color:rgb(38, 38, 38);">必填</font> | <font style="color:#000000;">云盘空间文件ID</font><br/><font style="color:#000000;">注意：一次性最多支持100个文件上传</font> |


### <font style="color:rgb(38, 38, 38);">body参数示例</font>
```json
[
    {
        "fileId": 2875051
    },
    {
        "fileId": 2875052
    }
]
```

## **<font style="color:rgb(38, 38, 38);">返回数据</font>**
<font style="color:rgb(38, 38, 38);">文字提示：上传成功</font>



> 更新: 2025-03-17 19:16:36  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/tqy2xatoo4qmdbz7>