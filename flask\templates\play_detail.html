{% extends "base.html" %}

{% block title %}{{ play.title }} - 短剧星球{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- 主要内容 -->
        <div class="lg:col-span-2">
            <!-- 短剧详情卡片 -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden mb-8">
                <div class="md:flex">
                    <!-- 海报 -->
                    <div class="md:w-1/3">
                        <div class="aspect-[3/4] bg-gray-200">
                            {% if play.poster_url %}
                            <img src="{{ play.poster_url }}" 
                                 alt="{{ play.title }}" 
                                 class="w-full h-full object-cover">
                            {% else %}
                            <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-400 to-purple-500">
                                <svg class="h-20 w-20 text-white/80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                </svg>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- 详情信息 -->
                    <div class="md:w-2/3 p-6">
                        <div class="mb-4">
                            <div class="flex items-center gap-2 mb-2">
                                <span class="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded-md text-xs font-medium">
                                    {{ play.primary_category.name if play.primary_category else '未分类' }}
                                </span>
                            </div>
                            
                            <h1 class="text-2xl md:text-3xl font-bold text-gray-900 mb-3">{{ play.title }}</h1>
                            
                            <div class="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-4">
                                {% if play.year %}
                                <div class="flex items-center gap-1">
                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                    </svg>
                                    <span>{{ play.year }}</span>
                                </div>
                                {% endif %}
                                
                                {% if play.episodes %}
                                <div class="flex items-center gap-1">
                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                    </svg>
                                    <span>{{ play.episodes }}</span>
                                </div>
                                {% endif %}
                                
                                {% if play.views %}
                                <div class="flex items-center gap-1">
                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                    </svg>
                                    <span>{{ play.views }} 次观看</span>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- 多个分类显示 -->
                        {% if play.categories %}
                        <div class="flex flex-wrap gap-2 mb-4">
                            {% for category in play.categories %}
                            <span class="inline-block bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                                {{ category.name }}
                            </span>
                            {% endfor %}
                        </div>
                        {% endif %}
                        
                        <!-- 描述 -->
                        {% if play.description %}
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">剧情简介</h3>
                            <p class="text-gray-700 leading-relaxed">{{ play.description }}</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- 观看链接 -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                    <svg class="h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.102m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
                    </svg>
                    观看链接
                </h2>
                
                <div class="space-y-4">
                    <!-- 123盘链接 -->
                    {% if play.pan123_link %}
                    <div class="border border-blue-200 rounded-lg p-4 bg-blue-50">
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center gap-2">
                                <span class="text-sm font-medium text-blue-800">123盘</span>
                            </div>
                            <button onclick="copyToClipboard('{{ play.pan123_link.link }}', this)" 
                                    class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 transition-colors">
                                复制链接
                            </button>
                        </div>
                        {% if play.pan123_link.code %}
                        <div class="flex items-center gap-2 text-sm text-blue-700">
                            <span>提取码:</span>
                            <code class="bg-blue-100 px-2 py-1 rounded font-mono">{{ play.pan123_link.code }}</code>
                            <button onclick="copyToClipboard('{{ play.pan123_link.code }}', this)" 
                                    class="text-blue-600 hover:text-blue-800 underline">
                                复制
                            </button>
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}
                    
                    <!-- 夸克网盘链接 -->
                    {% if play.quark_link %}
                    <div class="border border-purple-200 rounded-lg p-4 bg-purple-50">
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center gap-2">
                                <span class="text-sm font-medium text-purple-800">夸克网盘</span>
                            </div>
                            <button onclick="copyToClipboard('{{ play.quark_link.link }}', this)" 
                                    class="px-3 py-1 bg-purple-600 text-white rounded-md text-sm hover:bg-purple-700 transition-colors">
                                复制链接
                            </button>
                        </div>
                        {% if play.quark_link.code %}
                        <div class="flex items-center gap-2 text-sm text-purple-700">
                            <span>提取码:</span>
                            <code class="bg-purple-100 px-2 py-1 rounded font-mono">{{ play.quark_link.code }}</code>
                            <button onclick="copyToClipboard('{{ play.quark_link.code }}', this)" 
                                    class="text-purple-600 hover:text-purple-800 underline">
                                复制
                            </button>
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}
                    
                    <!-- 无可用链接 -->
                    {% if not play.pan123_link and not play.quark_link %}
                    <div class="text-center py-8 text-gray-500">
                        <svg class="mx-auto h-12 w-12 text-gray-400 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.102m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
                        </svg>
                        <p>暂无可用观看链接</p>
                        <p class="text-sm mt-1">链接正在更新中，请稍后再试</p>
                    </div>
                    {% endif %}
                </div>
                
                <!-- 使用说明 -->
                <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <h4 class="text-sm font-medium text-yellow-800 mb-2">使用说明:</h4>
                    <ul class="text-sm text-yellow-700 space-y-1">
                        <li>• 点击"复制链接"按钮复制网盘地址</li>
                        <li>• 在浏览器中打开网盘链接，输入提取码</li>
                        <li>• 本站仅提供链接分享，不存储任何视频文件</li>
                        <li>• 如链接失效，请联系管理员更新</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 侧边栏 -->
        <div class="lg:col-span-1">
            <!-- 相关推荐 -->
            {% if related_plays %}
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h3 class="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
                    <svg class="h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                    </svg>
                    相关推荐
                </h3>
                
                <div class="space-y-4">
                    {% for related_play in related_plays %}
                    <div class="flex gap-3 group cursor-pointer" @click="window.location.href='{{ url_for('main.play_detail', id=related_play.id) }}'">
                        <div class="w-16 h-20 flex-shrink-0 bg-gray-200 rounded-md overflow-hidden">
                            {% if related_play.poster_url %}
                            <img src="{{ related_play.poster_url }}" 
                                 alt="{{ related_play.title }}" 
                                 class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200">
                            {% else %}
                            <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-400 to-purple-500">
                                <svg class="h-6 w-6 text-white/80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                </svg>
                            </div>
                            {% endif %}
                        </div>
                        <div class="flex-1 min-w-0">
                            <h4 class="font-medium text-gray-900 line-clamp-2 group-hover:text-blue-600 transition-colors text-sm mb-1">
                                {{ related_play.title }}
                            </h4>
                            <div class="flex items-center gap-2 text-xs text-gray-500">
                                {% if related_play.year %}
                                <span>{{ related_play.year }}</span>
                                {% endif %}
                                {% if related_play.views %}
                                <span>{{ related_play.views }} 观看</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <div class="mt-4 pt-4 border-t border-gray-200">
                    <a href="{{ url_for('main.category', slug=play.primary_category.slug) if play.primary_category else url_for('main.index') }}" 
                       class="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1">
                        查看更多{{ play.primary_category.name if play.primary_category else '全部' }}短剧
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </a>
                </div>
            </div>
            {% endif %}
            
            <!-- 分类导航 -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
                    <svg class="h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                    </svg>
                    热门分类
                </h3>
                
                <div class="space-y-2">
                    {% for category in categories %}
                        {% if category.slug != 'all' %}
                        <a href="{{ url_for('main.category', slug=category.slug) }}" 
                           class="flex items-center justify-between p-2 rounded-md hover:bg-gray-50 transition-colors {{ 'bg-blue-50 text-blue-700' if category in play.categories else 'text-gray-700' }}">
                            <span class="text-sm font-medium">{{ category.name }}</span>
                            {% if category.play_count %}
                            <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                                {{ category.play_count }}
                            </span>
                            {% endif %}
                        </a>
                        {% endif %}
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 返回顶部按钮 -->
<button x-data="{ show: false }" 
        x-show="show" 
        @scroll.window="show = (window.pageYOffset > 400)"
        @click="window.scrollTo({top: 0, behavior: 'smooth'})"
        class="fixed bottom-6 right-6 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-all z-40">
    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"/>
    </svg>
</button>
{% endblock %}