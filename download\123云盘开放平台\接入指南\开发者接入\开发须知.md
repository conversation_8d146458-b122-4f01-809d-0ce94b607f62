# 开发须知

## 注意事项
1. <font style="color:rgb(51, 51, 51);">调用服务端接口时，需使用 HTTPS 协议、JSON 数据格式、UTF-8 编码，POST 请求请在 HTTP Header 中设置 Content-Type:application/json。</font>
2. <font style="color:rgb(51, 51, 51);">接口域名为：</font> https://open-api.123pan.com 
3. 分配的 client_secret 请注意安全保存，不要放在前端代码里，泄露后请联系客服更换。
4. 所有请求接口均需要带上请求头 platform 值为:open_platform
5. 开放平台发放的 Access Token 也将作为设备计算。同一时刻只允许 client_id 下最多 3 个token 正在使用。多次颁发会存在将 Token 踢下线的情况。颁发后的 Token 有效期为 30 天。

## 公共请求头
| **请求头名称** | **是否必填** | **类型** | **示例值** | **描述** |
| :---: | :---: | :---: | :---: | --- |
| Authorization | 是 | string | Bearer access_token | <font style="color:#000000;">注意格式：                  Bearer+空格+access_token</font> |
| Platform | 是 | string | open_platform | 固定值：open_platform |
| Content-Type | 是 | string | application/json | 固定值：application/json |


## 接口身份校验示例
开放 API 的 HTTP 调用，需在 Header 中传递 Authorization 参数，值为 token_type + 空格 + access_token。

示例：

```http
curl -v -x POST '域名/upload/v1/file/create' \
-H 'Authorization: Bearer access_token' \
-H 'Content-Type: application/json' \
-H 'Platform: open_platform' \
-d '{
    "parentFileID": 0,
    "filename": "测试文件.txt",
    "etag":"0a05e3dcd8ba1d14753597bc8611d0a1",
    "size":44321
}'
```

## 接口响应参数
| **参数名称** | **是否必填** | **类型** | **示例值** | **描述** |
| :---: | :---: | :---: | :---: | --- |
| code | 是 | int | 0 | code字段等于0 标识成功响应，其他code为失败响应 |
| message | 是 | string | ok | 请求成功为ok；异常时为具体异常信息 |
| data | 是 | any | - | 返回的响应内容；异常时为null |
| x-traceID | 是 | string | eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9 | 接口响应异常需要技术支持请提供接口返回的x-traceID |


## 接口响应示例
所有 API 的响应体中都包含code、message、data字段，data字段的数据结构请查看具体接口的文档。API都使用返回的 code字段等于0 标识成功响应，其他code为失败响应，请根据响应的code和message排查错误具体原因。如果接口响应异常需要技术支持请提供接口返回的x-traceID。

示例：

```json
{
  "code": 0,
  "message": "ok",
  "data": null,
  "x-traceID":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"
}
```

## 常用code码
| **body 中的 code** | **描述** |
| :---: | :---: |
| 401 | access_token无效 |
| 429 | 请求太频繁 |


## 限流处理
为确保所有用户的使用体验，123云盘开放平台会对下面 API 进行限流：

| **API** | **限制QPS（同一个client_id，每秒最大请求次数）** |
| --- | --- |
| api/v1/user/info | 1 |
| api/v1/file/move | 1 |
| api/v1/file/delete | 1 |
| api/v1/file/list | 4 |
| api/v2/file/list | 3 |
| upload/v1/file/mkdir | 2 |
| upload/v1/file/create | 2 |
| api/v1/access_token | 1 |
| api/v1/share/list   | 10 |
| api/v1/share/list/info   | 10 |
| api/v1/transcode/folder/info   | 20 |
| api/v1/transcode/upload/from_cloud_disk   | 1 |
| api/v1/transcode/delete   | 10 |
| api/v1/transcode/video/resolutions    | 1 |
| api/v1/transcode/video | 3 |
| api/v1/transcode/video/record   | 20 |
| api/v1/transcode/video/result  | 20 |
| api/v1/transcode/file/download   | 10 |
| api/v1/transcode/m3u8_ts/download   | 20 |
| api/v1/transcode/file/download/all   | 1 |




## 


> 更新: 2025-05-20 17:15:10  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/txgcvbfgh0gtuad5>