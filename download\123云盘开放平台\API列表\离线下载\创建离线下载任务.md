# 创建离线下载任务

API： POST 域名 + /api/v1/offline/download

> 说明：离线下载任务仅支持 http/https 任务创建
>

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#000000;">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## Body 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | --- |
| url | string | 必填 | 下载资源地址(http/https) |
| fileName | string | 非必填 | 自定义文件名称 （需携带图片格式，支持格式：png, gif, jpeg, tiff, webp,jpg,tif,svg,bmp）   |
| dirID | number | 非必填 | 选择下载到指定目录ID。 示例:10023<br/>注:不支持下载到根目录,默认会下载到名为"来自:离线下载"的目录中 |
| callBackUrl | string | 非必填 | 回调地址,当文件下载成功或者失败,均会通过回调地址通知。回调内容如下      url: 下载资源地址<br/>status: 0 成功，1 失败<br/>fileReason：失败原因<br/>fileID:成功后,该文件在云盘上的ID      请求类型：POST   {<br/>	"url": "[http://dc.com/resource.jpg",](http://dc.com/resource.jpg",)<br/>	"status": 0, <br/>	"failReason": "",<br/>        "fileID":100<br/>} |


## 返回数据
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| taskID | number | 必填 | 离线下载任务ID,可通过该ID,调用查询任务进度接口获取下载进度 |


## 示例
**请求示例**

```shell
curl --location 'https://open-api.123pan.com/api/v1/offline/download' \
--header 'Content-Type: application/json' \
--header 'Platform: open_platform' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)' \
--data '{
    "url": "https://vip.123pan.cn/1815309870/...(过长省略)",
    "fileName": "测试https离线下载.mp4"
}'
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
.build();
MediaType mediaType = MediaType.parse("application/json");
RequestBody body = RequestBody.create(mediaType, "{\n    \"url\": \"https://vip.123pan.cn/1815309870/...(过长省略)\",\n    \"fileName\": \"测试https离线下载.mp4\"\n}");
Request request = new Request.Builder()
.url("https://open-api.123pan.com/api/v1/offline/download")
.method("POST", body)
.addHeader("Content-Type", "application/json")
.addHeader("Platform", "open_platform")
.addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)")
.build();
Response response = client.newCall(request).execute();
```

```javascript
var settings = {
  "url": "https://open-api.123pan.com/api/v1/offline/download",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "Content-Type": "application/json",
    "Platform": "open_platform",
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)"
  },
  "data": JSON.stringify({
    "url": "https://vip.123pan.cn/1815309870/...(过长省略)",
    "fileName": "测试https离线下载.mp4"
  }),
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
```

```javascript
const axios = require('axios');
let data = JSON.stringify({
  "url": "https://vip.123pan.cn/1815309870/...(过长省略)",
  "fileName": "测试https离线下载.mp4"
});

let config = {
  method: 'post',
  maxBodyLength: Infinity,
  url: 'https://open-api.123pan.com/api/v1/offline/download',
  headers: { 
    'Content-Type': 'application/json', 
    'Platform': 'open_platform', 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
  },
  data : data
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});

```

```python
import http.client
import json

conn = http.client.HTTPSConnection("open-api.123pan.com")
payload = json.dumps({
  "url": "https://vip.123pan.cn/1815309870/...(过长省略)",
  "fileName": "测试https离线下载.mp4"
})
headers = {
  'Content-Type': 'application/json',
  'Platform': 'open_platform',
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
}
conn.request("POST", "/api/v1/offline/download", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

**响应示例**

```json
{
  "code": 0,
  "message": "ok",
  "data": {
    "taskID": 394756
  },
  "x-traceID": "2eabaf50-f64d-4603-a62d-b49cb4661e8e_kong-db-5898fdd8c6-t5pvc"
}
```



> 更新: 2025-03-17 19:17:10  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/he47hsq2o1xvgado>