#!/usr/bin/env python3
"""
短剧导入系统主入口
整合爬虫、数据库导入、QB监控、网盘上传的完整流程
"""

import os
import sys
import argparse
import time
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from drama_import_manager import DramaImportManager
from cloud_storage_manager import CloudStorageManager
from agsvpt_crawler import crawl_and_import


def show_banner():
    """显示程序横幅"""
    print("=" * 60)
    print("           短剧导入系统 v2.0")
    print("    集成爬虫 + 数据库 + QB监控 + 网盘上传")
    print("=" * 60)


def show_status(manager: DramaImportManager):
    """显示系统状态"""
    print("\n=== 系统状态 ===")
    
    # 显示可用网盘
    available_storages = manager.cloud_manager.get_available_storages()
    print(f"可用网盘: {available_storages if available_storages else '无'}")
    
    # 显示任务状态
    task_status = manager.get_task_status()
    print(f"总任务数: {task_status['total_tasks']}")
    
    if task_status['total_tasks'] > 0:
        print("\n最近任务:")
        for task in task_status['tasks'][-5:]:  # 显示最近5个任务
            print(f"  - {task['title'][:30]}... [{task['status']}]")


def run_crawler_only(limit: int, enable_cloud: bool = False):
    """仅运行爬虫模式"""
    print(f"\n=== 爬虫模式 ===")
    print(f"爬取数量: {limit}")
    print(f"网盘上传: {'启用' if enable_cloud else '禁用'}")
    
    result = crawl_and_import(limit, enable_cloud)
    
    print(f"\n爬虫执行结果:")
    print(f"  状态: {result.get('status', 'unknown')}")
    print(f"  爬取: {result.get('crawled', 0)} 条")
    print(f"  导入: {result.get('imported', 0)} 条")
    print(f"  跳过: {result.get('skipped', 0)} 条")
    if enable_cloud:
        print(f"  上传任务: {result.get('upload_tasks', 0)} 个")
    
    return result


def run_full_system(manager: DramaImportManager, crawl_limit: int):
    """运行完整系统"""
    print(f"\n=== 完整系统模式 ===")
    print(f"爬取数量: {crawl_limit}")
    
    # 运行完整导入流程
    result = manager.run_complete_import_process(crawl_limit)
    
    print(f"\n系统启动结果:")
    print(f"  状态: {result.get('status', 'unknown')}")
    if result.get('status') == 'success':
        crawl_result = result.get('crawl_result', {})
        print(f"  爬取导入: {crawl_result.get('imported', 0)} 条")
        print(f"  可用网盘: {result.get('available_storages', [])}")
        print(f"  监控状态: 已启动")
    
    return result


def run_monitor_mode(manager: DramaImportManager):
    """运行监控模式"""
    print(f"\n=== 监控模式 ===")
    print("启动QB下载监控...")
    
    manager.start_monitoring()
    
    try:
        print("监控已启动，按 Ctrl+C 停止")
        while True:
            time.sleep(60)
            
            # 每分钟显示一次状态
            task_status = manager.get_task_status()
            if task_status['total_tasks'] > 0:
                print(f"\n[{time.strftime('%H:%M:%S')}] 任务状态: {task_status['total_tasks']} 个任务")
                
                # 显示进行中的任务
                active_tasks = [t for t in task_status['tasks'] if t['status'] in ['downloading', 'uploading']]
                if active_tasks:
                    for task in active_tasks:
                        print(f"  - {task['title'][:30]}... [{task['status']}]")
            else:
                print(f"[{time.strftime('%H:%M:%S')}] 暂无任务")
                
    except KeyboardInterrupt:
        print("\n停止监控...")
        manager.stop_monitoring()


def test_cloud_storage():
    """测试网盘存储"""
    print(f"\n=== 网盘测试 ===")
    
    try:
        cloud_manager = CloudStorageManager()
        available = cloud_manager.get_available_storages()
        
        print(f"可用网盘: {available}")
        
        if available:
            print("网盘连接正常")
            return True
        else:
            print("没有可用的网盘")
            return False
            
    except Exception as e:
        print(f"网盘测试失败: {e}")
        return False


def main():
    """主函数"""
    show_banner()
    
    parser = argparse.ArgumentParser(description='短剧导入系统')
    parser.add_argument('--mode', choices=['crawler', 'full', 'monitor', 'test'], 
                       default='full', help='运行模式')
    parser.add_argument('--limit', type=int, default=10, help='爬取数量限制')
    parser.add_argument('--cloud', action='store_true', help='启用网盘上传')
    parser.add_argument('--status', action='store_true', help='显示系统状态')
    
    args = parser.parse_args()
    
    # 显示系统状态
    if args.status:
        try:
            manager = DramaImportManager()
            show_status(manager)
        except Exception as e:
            print(f"获取状态失败: {e}")
        return
    
    # 根据模式执行不同操作
    if args.mode == 'test':
        # 测试模式
        test_cloud_storage()
        
    elif args.mode == 'crawler':
        # 仅爬虫模式
        run_crawler_only(args.limit, args.cloud)
        
    elif args.mode == 'monitor':
        # 仅监控模式
        try:
            manager = DramaImportManager()
            run_monitor_mode(manager)
        except Exception as e:
            print(f"监控模式启动失败: {e}")
            
    elif args.mode == 'full':
        # 完整系统模式
        try:
            manager = DramaImportManager()
            result = run_full_system(manager, args.limit)
            
            if result.get('status') == 'success':
                print(f"\n系统已启动，可以使用以下命令查看状态:")
                print(f"  python main.py --status")
                print(f"\n或者启动监控模式:")
                print(f"  python main.py --mode monitor")
            
        except Exception as e:
            print(f"完整系统启动失败: {e}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    main()
