#!/usr/bin/env python3
"""
完整测试qBittorrent上传管理器
"""

import os
import tempfile
import shutil
from qb_upload_manager import QBUploadManager

def create_mock_download_folder():
    """创建模拟的下载文件夹"""
    # 创建临时目录模拟下载文件夹
    temp_base = tempfile.mkdtemp()
    mock_download_path = os.path.join(temp_base, "downloads", "agsvpt")
    os.makedirs(mock_download_path, exist_ok=True)
    
    # 创建模拟的短剧文件夹
    drama_folder = os.path.join(mock_download_path, "开到荼靡.Kai.Dao.Tu.Mi.S01.2025.720p.WEB-DL.AVC.AAC.2.0-COMBINE@AGSVWEB")
    os.makedirs(drama_folder, exist_ok=True)
    
    # 创建一些模拟文件
    test_files = [
        "第01集.mp4",
        "第02集.mp4", 
        "第03集.mp4",
        "字幕.srt"
    ]
    
    for filename in test_files:
        file_path = os.path.join(drama_folder, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(f"模拟文件内容: {filename}\n" * 1000)  # 创建一些内容
    
    print(f"✅ 创建模拟下载文件夹: {drama_folder}")
    return temp_base, mock_download_path, drama_folder

def test_full_process():
    """测试完整流程"""
    print("=== 测试完整qBittorrent上传流程 ===")
    
    # 创建模拟环境
    temp_base, mock_download_path, drama_folder = create_mock_download_folder()
    
    try:
        # 初始化管理器
        manager = QBUploadManager()
        
        # 临时修改配置中的路径
        original_path = manager.host_download_path
        manager.host_download_path = mock_download_path
        
        print(f"📁 模拟下载路径: {mock_download_path}")
        
        # 测试文件夹压缩
        title = "开到荼靡"
        print(f"📦 测试压缩文件夹: {title}")
        
        zip_path = manager.compress_folder(drama_folder, title)
        print(f"✅ 压缩完成: {zip_path}")
        
        # 测试网盘上传
        print(f"📤 测试网盘上传...")
        result = manager.upload_to_cloud(drama_folder, title)
        
        if result:
            print(f"✅ 上传成功!")
            print(f"🌐 平台: {result['platform']}")
            print(f"🔗 分享链接: {result['share_url']}")
            if result['extract_code']:
                print(f"🔑 提取码: {result['extract_code']}")
        else:
            print(f"❌ 上传失败")
        
        # 恢复原始配置
        manager.host_download_path = original_path
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理临时文件
        shutil.rmtree(temp_base)
        print(f"🗑️  清理临时文件夹")

if __name__ == "__main__":
    test_full_process()
