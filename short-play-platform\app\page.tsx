"use client"

import { Suspense } from "react"
import { Search, TrendingUp, Clock, Star } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { getPlays } from "@/lib/data"
import { PlayCard } from "@/components/play-card"
import { SiteHeader } from "@/components/header"

export default async function HomePage({
  searchParams,
}: {
  searchParams?: { q?: string; sort?: string }
}) {
  const allPlays = await getPlays()
  const searchQuery = searchParams?.q || ""
  const sortBy = searchParams?.sort || "latest"

  let filteredPlays = allPlays.filter((play) => 
    play.title.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // 排序逻辑
  switch (sortBy) {
    case "popular":
      filteredPlays = filteredPlays.sort((a, b) => (b.views || 0) - (a.views || 0))
      break
    case "rating":
      filteredPlays = filteredPlays.sort((a, b) => (b.rating || 0) - (a.rating || 0))
      break
    case "latest":
    default:
      filteredPlays = filteredPlays.sort((a, b) => (b.id) - (a.id))
      break
  }

  const sortOptions = [
    { value: "latest", label: "最新上传", icon: Clock },
    { value: "popular", label: "热门推荐", icon: TrendingUp },
    { value: "rating", label: "高分好剧", icon: Star },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-700">
      <SiteHeader />
      
      <main className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        {!searchQuery && (
          <section className="text-center py-12 mb-12">
            <div className="max-w-4xl mx-auto">
              <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent mb-6">
                发现精彩短剧
              </h1>
              <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
                汇聚全网热门短剧资源，123网盘、夸克网盘在线观看，更新及时，资源丰富
              </p>
              <div className="flex flex-wrap justify-center gap-4 mb-8">
                <Badge variant="secondary" className="px-4 py-2">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  每日更新
                </Badge>
                <Badge variant="secondary" className="px-4 py-2">
                  <Star className="h-4 w-4 mr-1" />
                  精选内容
                </Badge>
                <Badge variant="secondary" className="px-4 py-2">
                  <Clock className="h-4 w-4 mr-1" />
                  高清画质
                </Badge>
              </div>
            </div>
          </section>
        )}

        {/* Search Results Header */}
        {searchQuery && (
          <div className="mb-8">
            <h2 className="text-2xl font-bold mb-2">
              搜索结果: "{searchQuery}"
            </h2>
            <p className="text-muted-foreground">
              找到 {filteredPlays.length} 部相关短剧
            </p>
          </div>
        )}

        {/* Sort Options */}
        <div className="flex flex-wrap items-center justify-between mb-8">
          <div className="flex items-center space-x-2 mb-4 sm:mb-0">
            <span className="text-sm font-medium text-muted-foreground">排序:</span>
            <div className="flex flex-wrap gap-2">
              {sortOptions.map((option) => {
                const Icon = option.icon
                return (
                  <Button
                    key={option.value}
                    variant={sortBy === option.value ? "default" : "outline"}
                    size="sm"
                    onClick={() => {
                      const params = new URLSearchParams()
                      if (searchQuery) params.set('q', searchQuery)
                      params.set('sort', option.value)
                      window.location.search = params.toString()
                    }}
                    className="flex items-center space-x-1"
                  >
                    <Icon className="h-4 w-4" />
                    <span>{option.label}</span>
                  </Button>
                )
              })}
            </div>
          </div>
          
          <div className="text-sm text-muted-foreground">
            共 {filteredPlays.length} 部短剧
          </div>
        </div>

        {/* Play Grid */}
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7 gap-4 md:gap-6">
          {filteredPlays.map((play) => (
            <PlayCard key={play.id} play={play} />
          ))}
        </div>

        {/* Empty State */}
        {filteredPlays.length === 0 && (
          <div className="text-center py-20">
            <div className="max-w-md mx-auto">
              <Search className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">未找到相关内容</h3>
              <p className="text-muted-foreground mb-6">
                {searchQuery 
                  ? `未找到与 "${searchQuery}" 相关的短剧，请尝试其他关键词`
                  : "暂无短剧内容，请稍后再来查看"
                }
              </p>
              {searchQuery && (
                <Button onClick={() => window.location.href = '/'}>
                  浏览全部短剧
                </Button>
              )}
            </div>
          </div>
        )}

        {/* Load More - 为未来分页功能预留 */}
        {filteredPlays.length > 0 && filteredPlays.length >= 20 && (
          <div className="text-center mt-12">
            <Button variant="outline" size="lg">
              加载更多
            </Button>
          </div>
        )}
      </main>
    </div>
  )
}
