#!/usr/bin/env python3
"""
Flask短剧平台启动脚本 - 生产环境
"""

import sys
from app import create_app
from models import db, init_db, seed_data

# 创建生产环境应用实例
app = create_app()

def main():
    """主函数"""
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == 'init':
            """初始化数据库和示例数据"""
            print("初始化生产数据库...")
            with app.app_context():
                # 创建所有表
                db.create_all()
                print("数据库表创建成功")
                
                # 添加示例数据
                print("添加示例数据...")
                seed_data()
                print("示例数据添加成功")
                
                print("生产数据库初始化完成!")
                
        elif command == 'seed':
            """仅添加示例数据"""
            print("添加示例数据...")
            with app.app_context():
                seed_data()
                print("示例数据添加成功")
                
        elif command == 'reset':
            """重置数据库"""
            print("重置生产数据库...")
            with app.app_context():
                db.drop_all()
                db.create_all()
                seed_data()
                print("生产数据库重置完成")
                
        elif command == 'run' or command == 'serve':
            """运行应用"""
            print("启动短剧平台 - 生产环境...")
            print("数据库: MariaDB")
            print("访问地址: http://localhost:5000")
            
            # 显示数据库状态
            with app.app_context():
                from models import Play, Category
                play_count = Play.query.count()
                category_count = Category.query.count()
                print(f"当前数据: {play_count}个短剧, {category_count}个分类")
            
            app.run(debug=False, host='0.0.0.0', port=5000)
            
        else:
            print_usage()
    else:
        # 默认: 运行应用
        print("启动短剧平台 - 生产环境...")
        print("数据库: MariaDB") 
        print("访问地址: http://localhost:5000")
        
        # 显示数据库状态
        with app.app_context():
            from models import Play, Category
            play_count = Play.query.count()
            category_count = Category.query.count()
            print(f"当前数据: {play_count}个短剧, {category_count}个分类")
        
        app.run(debug=False, host='0.0.0.0', port=5000)

def print_usage():
    """打印使用说明"""
    print("""
短剧平台 Flask 应用 - 生产环境

用法:
    python run.py [command]

可用命令:
    init    - 初始化数据库和示例数据
    seed    - 添加示例数据
    reset   - 重置数据库 (危险操作)
    run     - 运行生产服务器 (默认)
    serve   - 运行生产服务器

示例:
    python run.py init      # 初始化项目
    python run.py           # 直接运行
    python run.py run       # 运行服务器

注意: 此版本仅支持生产环境(MariaDB)
""")

if __name__ == '__main__':
    main()