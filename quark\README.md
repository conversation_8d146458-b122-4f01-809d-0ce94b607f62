# Quark Cloud Disk Assistant

A simple and easy-to-use Quark cloud disk command-line tool that supports file upload, download, transfer, and sharing.

## Features

- File Upload
- File Download
- File Transfer
- File Sharing

## How to use

Run the main program and follow the on-screen instructions:

```bash
python main.py
```

## Core Files

- `main.py`: Main program entrance, integrating all functions.
- `quark.py`: Encapsulates core Quark cloud disk operations.
- `config_manager.py`: Manages reading and writing of configuration files.
- `exceptions.py`: Custom exception classes.

## Configuration

- `config.yml`: Main configuration file.
- `quark.cookie.txt`: Stores user cookies for login.

## Requirements

Install the necessary dependencies:

```bash
pip install -r requirements.txt
```