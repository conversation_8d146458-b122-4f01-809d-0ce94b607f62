# 获取视频文件可转码的分辨率

API： POST 域名 + /api/v1/transcode/video/resolutions

<font style="color:#DF2A3F;">注意：该接口需要轮询去查询结果，建议10s一次</font>

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#000000;">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## Body 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| fileId | number | 必填 | 文件Id |


### body参数示例
```json
{
    "fileId": 2875008
}
```

## 返回数据
| **名称** | | **类型** | **是否必填** | **说明** |
| :---: | --- | :---: | :---: | --- |
|  IsGetResolution | | boolean | 必填 | true 代表正在获取<br/>false 代表已经获取结束 |
| Resolutions | | string | 必填 | 可转码的分辨率 |
| <font style="color:#000000;">NowOrFinishedResolutions</font> | | <font style="color:#000000;">string</font> | <font style="color:#000000;">可填</font> | <font style="color:#000000;">已经转码的分辨率，如果为空则代表该视频从未转码过，后续在转码时候，如果已经有正在或者已经转码的分辨率之后，就无需在视频转码中传递传递已有的分辨率，避免重复转码</font> |
| CodecNames | | string | 必填 | 编码方式 |
| VideoTime | | number | 必填 | 视频时长，单位：秒 |


### **返回示例**
#### 正在获取中
```json
{
    "code": 0,
    "message": "ok",
    "data": {
        "IsGetResolution": true,
        "Resolutions": "",
        "NowOrFinishedResolutions": "",
        "CodecNames": "",
        "VideoTime": 0
    },
    "x-traceID": ""
}
```

#### 已经获取到了（从未转码过）
```json
{
    "code": 0,
    "message": "ok",
    "data": {
        "IsGetResolution": false,
        "Resolutions": "480p,720p,1080p",
        "NowOrFinishedResolutions": "",
        "CodecNames": "H.264",
        "VideoTime": 51
    },
    "x-traceID": ""
}
```

#### 已经获取到了（之前有转码过其它分辨率）
```json
{
    "code": 0,
    "message": "ok",
    "data": {
        "IsGetResolution": false,
        "Resolutions": "480p,720p,1080p",
        "NowOrFinishedResolutions": "1080p,720p",
        "CodecNames": "H.264",
        "VideoTime": 51
    },
    "x-traceID": ""
}
```

`



> 更新: 2025-03-17 19:16:38  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/apzlsgyoggmqwl36>