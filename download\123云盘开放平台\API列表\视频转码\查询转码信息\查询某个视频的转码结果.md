# 查询某个视频的转码结果

API： POST 域名 + /api/v1/transcode/video/result

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#000000;">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## Body 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| fileId | number | 必填 | 文件Id |


### body参数示例
```json
{
  "fileId": 2875008
}
```

## 返回数据
| **名称** | | **类型** | **是否必填** | **说明** |
| --- | --- | :---: | :---: | --- |
| UserTranscodeVideoList | | array | 必填 | 用户转码结果列表 |
| Uid | | number | 必填 | 用户id |
| Resolution | | string | 必填 | 分辨率 |
| Status | | number | 必填 | <font style="color:#000000;">1：准备转码   </font><font style="color:#000000;">2：正在转码中    </font><font style="color:#000000;">3-254：转码失败，时长会自动回退255：转码成功</font> |
| Files | | Array |  |  |
|  | FileName | string | 必填 | 转码文件名称 |
|  | FileSize | string | 必填 | 转码文件大小 |
|  | Resolution | string | 必填 | 转码文件分辨率 |
|  | CreateAt | string | 必填 | 转码文件创建时间 |
|  | Url | string | 必填 | 转码文件播放地址，只有m3u8文件有播放地址，ts文件没有 |


### **返回示例**
```json
{
    "code": 0,
    "message": "ok",
    "data": {
        "UserTranscodeVideoList": [
            {
                "Id": 497,
                "Uid": 1814435971,
                "Resolution": "720P",
                "Status": 255,
                "CreateAt": "2024-12-19 11:45:04",
                "UpdateAt": "2024-12-19 11:45:23",
                "Files": [
                    {
                        "FileName": "stream.m3u8",
                        "FileSize": "177B",
                        "Resolution": "720P",
                        "CreateAt": "2024-12-19 11:45:09",
                        "Url": "http://vipdev.123pan.com/1814435971/transcode/video.m3u8?extParams=m3u8&resolutions=720p&suffix=mp4&from=transcode"
                    },
                    {
                        "FileName": "000.ts",
                        "FileSize": "497.17KB",
                        "Resolution": "720P",
                        "CreateAt": "2024-12-19 11:45:09",
                        "Url": ""
                    }
                ]
            },
            {
                "Id": 498,
                "Uid": 1814435971,
                "Resolution": "1080P",
                "Status": 255,
                "CreateAt": "2024-12-19 11:45:04",
                "UpdateAt": "2024-12-19 11:45:23",
                "Files": [
                    {
                        "FileName": "stream.m3u8",
                        "FileSize": "177B",
                        "Resolution": "1080P",
                        "CreateAt": "2024-12-19 11:45:09",
                        "Url": "http://vipdev.123pan.com/1814435971/transcode/video.m3u8?extParams=m3u8&resolutions=1080p&suffix=mp4&from=transcode"
                    },
                    {
                        "FileName": "000.ts",
                        "FileSize": "452.93KB",
                        "Resolution": "1080P",
                        "CreateAt": "2024-12-19 11:45:09",
                        "Url": ""
                    }
                ]
            }
        ]
    },
    "x-traceID": ""
}
```



> 更新: 2025-03-17 19:16:42  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/iucbqgge0dgfc8sv>