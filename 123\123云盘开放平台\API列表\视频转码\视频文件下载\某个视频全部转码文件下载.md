# 某个视频全部转码文件下载

API： POST 域名 + /api/v1/transcode/file/download/all

<font style="color:#DF2A3F;">注意：</font>

<font style="color:#DF2A3F;">1、该接口需要轮询去查询结果，建议10s一次</font>

<font style="color:#DF2A3F;">2、这里只是返回下载地址，获取到下载地址之后，把地址放在浏览器中即可进行下载</font>

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#000000;">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## Body 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| fileId | number | 必填 | 文件Id |
| zipName | string | 必填 | 下载zip文件的名字 |


### body参数示例
```json
{
    "fileId": 2875008,
    "zipName": "good.mp4"
}
```

## 返回数据
| **名称** | | **类型** | **是否必填** | **说明** |
| :---: | --- | :---: | :---: | --- |
|  isDownloading | | boolean | 必填 | true 代表正在下载中<br/>false 下载完毕了 |
| isFull | | boolean | 必填 | true 代表转码空间满了<br/>false 代表转码空间未满 |
| downloadUrl | | string | 必填 | 下载地址，注意：只有在转码空间未满，并且已经下载完毕才有值 |


### **返回示例**
#### 转码空间满了
```json
{
    "code": 0,
    "message": "ok",
    "data": {
        "isDownloading": false,
        "isFull": true,
        "downloadUrl": ""
    },
    "x-traceID": ""
}
```

#### 获取中
```json
{
    "code": 0,
    "message": "ok",
    "data": {
        "isDownloading": true,
        "isFull": false,
        "downloadUrl": ""
    },
    "x-traceID": ""
}
```

#### 获取到了
```json
{
    "code": 0,
    "message": "ok",
    "data": {
        "isDownloading": false,
        "isFull": false,
        "downloadUrl": "https://dev3.123pan.com:8443/a/api/uncompress/statics/b3c048da66b362be09d93b18727bf104_3219013/1080p_480p_222.mp4.zip?fileId=2875054&token=Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3MzU3ODMyODEsImlhdCI6MTczNTc4Mjk4MSwiaWQiOjE4MTQ0MzU5NzEsIm1haWwiOiIiLCJuaWNrbmFtZSI6IjE1MzIzNzAxMTM0IiwidXNlcm5hbWUiOjE1MzIzNzAxMTM0LCJ2Ijo5OH0.IqgK-7vN-QbpjwE1W4NRcb8QVm3FJvTqwO53fsggE14"
    },
    "x-traceID": "92c4d4f3-10d2-4423-9d26-7909cbdb7db4_test-kong-5bd74855d7-c2t4z"
}
```

`



> 更新: 2025-03-17 19:16:46  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/yb7hrb0x2gym7xic>