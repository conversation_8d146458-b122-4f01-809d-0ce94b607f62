#!/usr/bin/env python3
"""
夸克网盘上传工具使用示例
"""

from quark import QuarkPan
from exceptions import QuarkPanException
import os

def example_single_upload():
    """单文件上传示例"""
    print("=== 单文件上传示例 ===")
    
    try:
        # 创建夸克网盘客户端
        quark = QuarkPan()
        
        # 要上传的文件
        file_path = "quark.cookie.txt"  # 使用现有的cookie文件作为示例
        
        if not os.path.exists(file_path):
            print(f"示例文件 {file_path} 不存在")
            return
        
        # 获取文件信息
        file_info = quark.get_file_info(file_path)
        print(f"文件名: {file_info['name']}")
        print(f"文件大小: {file_info['size_human']}")
        print(f"文件类型: {file_info['mime_type']}")
        
        # 上传文件并创建永久分享链接
        print("\n开始上传...")
        result = quark.upload_file(file_path, share_expire=1)
        
        # 显示结果
        print("\n✅ 上传成功！")
        print(quark.format_share_info(result))
        
    except QuarkPanException as e:
        print(f"❌ 夸克网盘错误: {e}")
    except Exception as e:
        print(f"❌ 未知错误: {e}")

def example_batch_upload():
    """批量上传示例"""
    print("\n=== 批量上传示例 ===")
    
    try:
        quark = QuarkPan()
        
        # 要上传的文件列表
        files = [
            "config.yml",
            "requirements.txt",
            "README.md"
        ]
        
        # 过滤存在的文件
        existing_files = [f for f in files if os.path.exists(f)]
        
        if not existing_files:
            print("没有找到示例文件")
            return
        
        print(f"准备上传 {len(existing_files)} 个文件:")
        for file_path in existing_files:
            file_info = quark.get_file_info(file_path)
            print(f"  - {file_info['name']} ({file_info['size_human']})")
        
        # 批量上传
        print("\n开始批量上传...")
        results = quark.upload_files(existing_files, share_expire=2)  # 7天过期
        
        # 显示结果
        success_count = 0
        for result in results:
            if result["status"] == "success":
                success_count += 1
                print(f"\n✅ {result['file_path']} 上传成功")
                print(quark.format_share_info(result))
            else:
                print(f"\n❌ {result['file_path']} 上传失败: {result['error']}")
        
        print(f"\n📊 批量上传完成: {success_count}/{len(results)} 个文件成功")
        
    except QuarkPanException as e:
        print(f"❌ 夸克网盘错误: {e}")
    except Exception as e:
        print(f"❌ 未知错误: {e}")

def example_file_validation():
    """文件验证示例"""
    print("\n=== 文件验证示例 ===")
    
    try:
        quark = QuarkPan()
        
        test_files = [
            "config.yml",           # 存在的文件
            "nonexistent.txt",      # 不存在的文件
            ".",                    # 目录
        ]
        
        for file_path in test_files:
            print(f"\n验证文件: {file_path}")
            
            # 验证文件（不抛出异常）
            is_valid = quark.validate_file(file_path)
            print(f"  验证结果: {'✅ 有效' if is_valid else '❌ 无效'}")
            
            # 如果文件存在且有效，显示详细信息
            if is_valid:
                try:
                    file_info = quark.get_file_info(file_path)
                    print(f"  文件大小: {file_info['size_human']}")
                    print(f"  文件类型: {file_info['mime_type']}")
                except Exception as e:
                    print(f"  获取文件信息失败: {e}")
        
    except Exception as e:
        print(f"❌ 错误: {e}")

def example_share_options():
    """分享选项示例"""
    print("\n=== 分享选项示例 ===")
    
    try:
        quark = QuarkPan()
        
        # 获取分享过期选项
        options = quark.get_share_expire_options()
        
        print("可用的分享过期选项:")
        for code, description in options.items():
            print(f"  {code}: {description}")
        
        # 演示不同过期时间的分享创建
        file_path = "config.yml"
        if os.path.exists(file_path):
            print(f"\n使用文件 {file_path} 演示不同过期时间:")
            
            for expire_code in [1, 2]:  # 永久和7天
                try:
                    print(f"\n创建 {options[expire_code]} 分享链接...")
                    result = quark.upload_file(file_path, share_expire=expire_code)
                    print(f"✅ 成功: {result['share_url']}")
                except Exception as e:
                    print(f"❌ 失败: {e}")
                    break  # 避免重复上传同一文件
        
    except Exception as e:
        print(f"❌ 错误: {e}")

def main():
    """主函数"""
    print("夸克网盘上传工具使用示例")
    print("=" * 50)
    
    # 检查必要文件
    if not os.path.exists("quark.cookie.txt"):
        print("❌ 请先创建 quark.cookie.txt 文件并填入有效的Cookie")
        return
    
    if not os.path.exists("config.yml"):
        print("❌ 请先创建 config.yml 配置文件")
        return
    
    try:
        # 运行各种示例
        example_single_upload()
        example_batch_upload()
        example_file_validation()
        example_share_options()
        
    except KeyboardInterrupt:
        print("\n⚠️  操作被用户中断")
    except Exception as e:
        print(f"\n❌ 程序错误: {e}")

if __name__ == "__main__":
    main()
