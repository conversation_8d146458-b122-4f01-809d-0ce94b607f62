# 💡上传流程说明

1. **创建文件**
    1. 请求创建文件接口，接口返回的`reuse`为true时，表示秒传成功，上传结束。
    2. 非秒传情况将会返回预上传ID`preuploadID`与分片大小`sliceSize`，请将文件根据分片大小切分。
2. **获取上传地址**
    1. 非秒传时，携带返回的`preuploadID`，自定义分片序号`sliceNo`(从数字1开始)。
    2. 获取上传地址`presignedURL`。
3. **上传文件**
    1. 向返回的地址`presignedURL`发送PUT请求，上传文件分片。
    2. <font style="color:#000000;">注：</font>PUT请求的header中请不要携带Authorization、Platform参数。
4. **文件比对（非必需）**
    1. 所有分片上传后，调用列举已上传分片接口，将本地与云端的分片MD5比对。
    2. 注：如果您的文件小于`sliceSize`** ，**该操作将会返回空值**，**可以跳过此步。
5. **上传完成**
    1. 请求上传完毕接口，若接口返回的`async`为false且`fileID`不为0时，上传完成。
    2. 若接口返回的`async`为true时，则需下一步，调用异步轮询获取上传结果接口，获取上传最终结果。
6. **轮询查询**
    1. 若异步轮询获取上传结果接口返回的`completed`为false，请1秒后再次调用此接口，查询上传结果。
    2. 注：该步骤需要等待，建议轮询获取结果。123云盘服务器会校验用户预上传时的MD5与实际上传成功的MD5是否一致。

## 上传文件时序图
![1740047467017-a08f3220-54a2-4642-9d69-b9fdd78f98d0.png](./img/Gk8xFW2A7ulpdIZD/1740047467017-a08f3220-54a2-4642-9d69-b9fdd78f98d0-273852.png)

## 上传文件Demo
PHP： [index.php](https://123yunpan.yuque.com/attachments/yuque/0/2025/php/39215739/1742210262832-01c8c3f1-7269-496e-aba4-855e9fa1836d.php)



> 更新: 2025-03-17 19:17:43  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/di0url3qn13tk28t>