# 原文件下载

API： POST 域名 + /api/v1/transcode/file/download

**<font style="color:#DF2A3F;">注意：这里只是返回下载地址，获取到下载地址之后，把地址放在浏览器中即可进行下载</font>**

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#000000;">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## Body 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| fileId | number | 必填 | 文件Id |


### body参数示例
```json
{
    "fileId": 2875008
}
```

## 返回数据
| **名称** | | **类型** | **是否必填** | **说明** |
| :---: | --- | :---: | :---: | --- |
|  downloadUrl | | string | 必填 | 下载地址，如果转码空间满了，则返回空 |
| isFull | | boolean | 必填 | 转码空间容量是否满了，如果满了，则不会返回下载地址，需要用户购买转码空间之后才能下载 |


### **返回示例**
#### 转码空间满了
```json
{
    "code": 0,
    "message": "ok",
    "data": {
        "downloadUrl": "",
        "isFull": true
    },
    "x-traceID": ""
}
```

#### 转码空间未满
```json
{
    "code": 0,
    "message": "ok",
    "data": {
        "downloadUrl": "https://download-cdn-test.cjjd19.com/123-694/01895127/1814435971-0/018951270841b34118b72f38738952b6/c-m88?v=2&t=1734589251&s=ac5db70fd79485e248c1cb3bd82cecc9&bzc=666&bzs=1814435971&filename=video.mp4&cache_type=2",
        "isFull": false
    },
    "x-traceID": ""
}
```



> 更新: 2025-03-17 19:16:43  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/mlltlx57sty6g9gf>