#!/usr/bin/env python3
"""
测试部分链接的去重逻辑
"""

import pymysql
from datetime import datetime

# 数据库配置
db_config = {
    'host': '************',
    'user': 'sql23721_duanji',
    'password': '507877550@lihao',
    'database': 'sql23721_duanji',
    'charset': 'utf8mb4'
}

def add_single_link():
    """添加单个网盘链接进行测试"""
    print("=== 添加单个网盘链接 ===")
    
    try:
        connection = pymysql.connect(**db_config)
        
        with connection.cursor() as cursor:
            # 查找"开到荼靡"的play_id
            cursor.execute("SELECT id FROM play WHERE title = '开到荼靡'")
            play = cursor.fetchone()
            
            if not play:
                print("❌ 未找到'开到荼靡'短剧")
                return
            
            play_id = play[0]
            current_time = datetime.now()
            
            # 先清理现有链接
            cursor.execute("DELETE FROM play_link WHERE play_id = %s", (play_id,))
            
            # 只添加一个quark链接
            cursor.execute("""
                INSERT INTO play_link (play_id, link, code, platform, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s)
            """, (
                play_id,
                "https://pan.quark.cn/s/test123456",
                "",
                "quark",
                current_time,
                current_time
            ))
            
            connection.commit()
            print(f"✅ 添加了quark链接，play_id: {play_id}")
            
        connection.close()
        
    except Exception as e:
        print(f"❌ 添加链接失败: {e}")

def test_dedup_with_partial_links():
    """测试有部分链接时的去重逻辑"""
    print("\n=== 测试部分链接去重逻辑 ===")
    
    try:
        connection = pymysql.connect(**db_config)
        
        with connection.cursor() as cursor:
            # 查找"开到荼靡"的play_id
            cursor.execute("SELECT id FROM play WHERE title = '开到荼靡'")
            play = cursor.fetchone()
            
            if not play:
                print("❌ 未找到'开到荼靡'短剧")
                return
            
            play_id = play[0]
            
            # 查看现有链接
            cursor.execute("""
                SELECT platform, link, code FROM play_link 
                WHERE play_id = %s AND link IS NOT NULL AND link != ''
            """, (play_id,))
            existing_links = cursor.fetchall()
            
            print(f"📋 现有链接数量: {len(existing_links)}")
            for link in existing_links:
                platform, url, code = link
                print(f"  - {platform}: {url} (提取码: {code or '无'})")
            
            # 模拟去重逻辑
            platforms = [link[0] for link in existing_links]
            
            # 可用的网盘客户端
            available_clients = ["quark", "123pan"]
            
            # 检查缺失的平台
            missing_platforms = [client for client in available_clients if client not in platforms]
            
            print(f"🔍 可用网盘: {available_clients}")
            print(f"📋 已有平台: {platforms}")
            print(f"❓ 缺失平台: {missing_platforms}")
            
            if not missing_platforms:
                print("✅ 所有网盘都有链接，应该跳过")
                return "skip"
            else:
                print(f"📤 需要补充{', '.join(missing_platforms)}网盘链接")
                return "continue"
                
        connection.close()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return "error"

def add_both_links():
    """添加两个网盘链接"""
    print("\n=== 添加两个网盘链接 ===")
    
    try:
        connection = pymysql.connect(**db_config)
        
        with connection.cursor() as cursor:
            # 查找"开到荼靡"的play_id
            cursor.execute("SELECT id FROM play WHERE title = '开到荼靡'")
            play = cursor.fetchone()
            
            if not play:
                print("❌ 未找到'开到荼靡'短剧")
                return
            
            play_id = play[0]
            current_time = datetime.now()
            
            # 添加123pan链接
            cursor.execute("""
                INSERT INTO play_link (play_id, link, code, platform, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s)
            """, (
                play_id,
                "https://www.123pan.com/s/test789",
                "test",
                "123pan",
                current_time,
                current_time
            ))
            
            connection.commit()
            print(f"✅ 添加了123pan链接，play_id: {play_id}")
            
        connection.close()
        
    except Exception as e:
        print(f"❌ 添加链接失败: {e}")

if __name__ == "__main__":
    # 测试1: 只有一个链接
    add_single_link()
    result1 = test_dedup_with_partial_links()
    
    # 测试2: 添加第二个链接
    add_both_links()
    result2 = test_dedup_with_partial_links()
    
    print(f"\n=== 测试结果 ===")
    print(f"只有quark链接时: {result1} (应该是continue)")
    print(f"两个链接都有时: {result2} (应该是skip)")
