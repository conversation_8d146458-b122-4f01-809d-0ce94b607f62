#!/usr/bin/env python3
"""
夸克网盘文件上传工具 - 最终版本
专注于文件上传功能，分享链接需要手动创建
"""

import os
import sys
import argparse
from quark import QuarkPan
from exceptions import QuarkPanException

def upload_file_final(file_path: str, config_file: str = "config.yml", verbose: bool = False) -> dict:
    """
    上传文件到夸克网盘
    
    Args:
        file_path: 文件路径
        config_file: 配置文件路径
        verbose: 是否显示详细信息
        
    Returns:
        结果字典
    """
    try:
        # 检查文件
        if not os.path.exists(file_path):
            return {
                "success": False,
                "error": f"文件不存在: {file_path}",
                "file_path": file_path
            }
        
        # 创建夸克网盘客户端
        quark = QuarkPan(config_file)
        
        # 验证文件
        if not quark.validate_file(file_path):
            return {
                "success": False,
                "error": "文件验证失败",
                "file_path": file_path
            }
        
        # 获取文件信息
        file_info = quark.get_file_info(file_path)
        
        if verbose:
            print(f"📁 文件名: {file_info['name']}")
            print(f"📊 文件大小: {file_info['size_human']}")
            print(f"📄 文件类型: {file_info['mime_type']}")
            print()
        
        print(f"🚀 开始上传文件: {file_info['name']}")
        
        # 执行上传（仅上传，不创建分享）
        result = upload_file_only(quark, file_path)
        
        if result:
            print("✅ 文件上传成功！")
            print(f"📁 文件名: {file_info['name']}")
            print(f"🆔 文件ID: {result.get('fid', '未知')}")
            print()
            print("📋 手动创建分享链接步骤:")
            print("1. 打开夸克网盘网页版: https://pan.quark.cn")
            print("2. 找到刚上传的文件")
            print("3. 右键点击文件，选择'分享'")
            print("4. 设置分享参数并创建分享链接")
            print("5. 复制分享链接和提取码")
            
            return {
                "success": True,
                "file_path": file_path,
                "file_name": file_info['name'],
                "file_id": result.get('fid'),
                "upload_time": result.get('upload_time')
            }
        else:
            print("❌ 文件上传失败")
            return {
                "success": False,
                "error": "上传失败",
                "file_path": file_path
            }
            
    except QuarkPanException as e:
        error_msg = f"夸克网盘错误: {e}"
        print(f"❌ {error_msg}")
        return {
            "success": False,
            "error": error_msg,
            "file_path": file_path
        }
    except Exception as e:
        error_msg = f"未知错误: {e}"
        print(f"❌ {error_msg}")
        return {
            "success": False,
            "error": error_msg,
            "file_path": file_path
        }

def upload_file_only(quark: QuarkPan, file_path: str):
    """仅上传文件，不创建分享链接"""
    import time

    try:
        # 预上传
        upload_info = quark._pre_upload(file_path)

        # Hash检查（关键步骤！）
        if quark._check_hash(file_path, upload_info):
            print("✅ 文件已存在，跳过上传")
            return {
                "fid": upload_info["fid"],
                "file_name": os.path.basename(file_path),
                "status": "file_exists",
                "upload_time": time.strftime("%Y-%m-%d %H:%M:%S")
            }

        # 分片上传
        etags = quark._chunk_upload(file_path, upload_info)

        # 提交分片
        quark._commit_upload(upload_info, etags)

        # 尝试完成上传（忽略错误）
        try:
            quark._finish_upload(upload_info)
        except:
            print("⚠️  完成上传API调用失败，但文件已成功上传")

        return {
            "fid": upload_info["fid"],
            "file_name": os.path.basename(file_path),
            "status": "uploaded",
            "upload_time": time.strftime("%Y-%m-%d %H:%M:%S")
        }

    except Exception as e:
        print(f"❌ 上传失败: {e}")
        return None

def batch_upload_final(file_paths: list, **kwargs) -> list:
    """批量上传文件"""
    results = []
    total = len(file_paths)
    
    print(f"📦 开始批量上传 {total} 个文件")
    print("=" * 60)
    
    success_count = 0
    for i, file_path in enumerate(file_paths, 1):
        print(f"\n--- 第 {i}/{total} 个文件 ---")
        
        result = upload_file_final(file_path, **kwargs)
        results.append(result)
        
        if result["success"]:
            success_count += 1
    
    print(f"\n📊 批量上传完成: {success_count}/{total} 个文件成功")
    
    # 显示成功上传的文件
    if success_count > 0:
        print("\n✅ 成功上传的文件:")
        for i, result in enumerate(results, 1):
            if result["success"]:
                print(f"{i}. {result['file_name']} (ID: {result.get('file_id', '未知')})")
        
        print("\n📋 批量分享建议:")
        print("1. 登录夸克网盘网页版")
        print("2. 选中所有上传的文件")
        print("3. 批量创建分享链接")
    
    return results

def show_usage_tips():
    """显示使用提示"""
    print("💡 使用提示:")
    print("1. 确保 quark.cookie.txt 文件包含有效的Cookie")
    print("2. 大文件上传可能需要较长时间，请耐心等待")
    print("3. 上传成功后，请手动在网页端创建分享链接")
    print("4. 避免频繁上传，以免被限制")
    print()
    print("🔧 Cookie获取方法:")
    print("1. 打开 https://pan.quark.cn 并登录")
    print("2. 按F12打开开发者工具")
    print("3. 刷新页面，在Network中找到任意请求")
    print("4. 复制Cookie值到 quark.cookie.txt 文件")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="夸克网盘文件上传工具 - 最终版本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s file.txt                          # 上传单个文件
  %(prog)s file1.txt file2.txt               # 批量上传多个文件
  %(prog)s file.txt --verbose                # 显示详细信息
  %(prog)s --tips                            # 显示使用提示
        """
    )
    
    parser.add_argument('files', nargs='*', help='要上传的文件路径')
    parser.add_argument('--config', '-c', default='config.yml',
                       help='配置文件路径 (默认: config.yml)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='显示详细信息')
    parser.add_argument('--tips', action='store_true',
                       help='显示使用提示')
    
    args = parser.parse_args()
    
    # 显示使用提示
    if args.tips:
        show_usage_tips()
        return 0
    
    # 检查文件参数
    if not args.files:
        print("❌ 请指定要上传的文件")
        parser.print_help()
        return 1
    
    # 检查文件是否存在
    missing_files = [f for f in args.files if not os.path.exists(f)]
    if missing_files:
        print("❌ 以下文件不存在:")
        for f in missing_files:
            print(f"   - {f}")
        return 1
    
    print("夸克网盘文件上传工具 - 最终版本")
    print("=" * 50)
    
    try:
        if len(args.files) == 1:
            # 单文件上传
            result = upload_file_final(
                args.files[0],
                config_file=args.config,
                verbose=args.verbose
            )
            return 0 if result["success"] else 1
        else:
            # 批量上传
            results = batch_upload_final(
                args.files,
                config_file=args.config,
                verbose=args.verbose
            )
            success_count = sum(1 for r in results if r["success"])
            return 0 if success_count > 0 else 1
    
    except KeyboardInterrupt:
        print("\n⚠️  操作被用户中断")
        return 1
    except Exception as e:
        print(f"❌ 程序错误: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
