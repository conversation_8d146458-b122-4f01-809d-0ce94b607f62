# 短剧导入系统统一配置文件

# qBittorrent配置
qbittorrent:
  host: "***********"
  port: 49850
  username: "admin"
  password: "507877550@lihao"
  download_path: "/downloads" # QB下载路径
  monitor_interval: 30 # 监控间隔（秒）
  auto_upload_to_pan: true # 自动上传到网盘
  delete_after_upload: false # 上传后删除本地文件

# 网盘存储配置
cloud_storage:
  # 123网盘配置
  yunpan123:
    enabled: true
    config_path: "../123/config.json"
    default_directory: "/短剧资源"
    default_expire_days: 0 # 0表示永久

  # 夸克网盘配置
  quark:
    enabled: true
    config_path: "../quark/config.yml"
    default_expire_type: 1 # 1表示永久

# 导入流程配置
import:
  auto_upload: true # 是否自动上传下载完成的文件
  preferred_storage: "quark" # 首选网盘: "quark" 或 "123"
  crawl_limit: 10 # 默认爬取数量

# 爬取站点配置
crawler:
  sites:
    agsvpt:
      name: "AGSVPT"
      base_url: "https://www.agsvpt.com"
      rss_url: "https://www.agsvpt.com/torrentrss.php"
      params:
        passkey: "0d4feadb8f31654b72763a3dad167361"
        rows: 10 # 每次获取数量
        cat419: 419 # 短剧分类
        paid: 0 # 免费资源

      # 请求配置
      headers:
        User-Agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
      timeout: 30
      retry_times: 3
      retry_delay: 5

  # 爬取配置
  crawl_interval: 300 # 爬取间隔（秒）
  max_pages: 10 # 最大爬取页数
  enable_auto_crawl: true # 是否启用自动爬取

# Flask数据库配置
database:
  # 连接到flask项目的数据库
  db_path: "../flask/instance/shortplay.db"

  # 数据映射配置
  category_mapping:
    "短剧": 1
    "电视剧": 2
    "电影": 3
    "综艺": 4
    "动漫": 5

  # 默认值配置
  default_category_id: 1
  default_poster_url: "/static/images/default-poster.jpg"

# 日志配置
logging:
  level: "INFO" # DEBUG, INFO, WARNING, ERROR
  log_file: "import.log"
  max_log_size: "10MB"
  backup_count: 5

# 通知配置（可选）
notification:
  enable: false
  webhook_url: "" # 可配置钉钉、企业微信等webhook
