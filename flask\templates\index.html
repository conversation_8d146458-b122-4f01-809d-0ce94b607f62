{% extends "base.html" %}

{% block title %}短剧星球 - 精彩短剧在线观看{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- 筛选和排序栏 -->
    <div class="mb-8" x-data="{ showFilters: false }">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <!-- 统计信息 -->
            <div class="text-gray-600">
                <span class="text-sm">共找到 <strong class="text-gray-900">{{ total_plays }}</strong> 部短剧</span>
                {% if search %}
                <span class="text-sm ml-2">搜索: "<strong class="text-blue-600">{{ search }}</strong>"</span>
                {% endif %}
            </div>
            
            <!-- 排序选项 -->
            <div class="flex items-center gap-4">
                <button @click="showFilters = !showFilters" 
                        class="md:hidden flex items-center gap-2 px-3 py-2 rounded-md bg-white/80 border border-gray-300 text-sm font-medium hover:bg-white/90">
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12"/>
                    </svg>
                    筛选排序
                </button>
                
                <div class="hidden md:flex items-center gap-2" :class="{ 'flex': showFilters, 'hidden md:flex': !showFilters }">
                    <span class="text-sm text-gray-600">排序:</span>
                    <div class="flex border border-gray-300 rounded-md overflow-hidden">
                        <a href="{{ url_for('main.index', search=search, category=category_slug, sort='latest') }}" 
                           class="px-3 py-1.5 text-sm font-medium border-r border-gray-300 transition-colors {{ 'bg-blue-500 text-white' if sort_by == 'latest' else 'bg-white text-gray-700 hover:bg-gray-50' }}">
                            最新
                        </a>
                        <a href="{{ url_for('main.index', search=search, category=category_slug, sort='popular') }}" 
                           class="px-3 py-1.5 text-sm font-medium transition-colors {{ 'bg-blue-500 text-white' if sort_by == 'popular' else 'bg-white text-gray-700 hover:bg-gray-50' }}">
                            热门
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 移动端筛选栏 -->
        <div x-show="showFilters" x-cloak x-transition class="md:hidden mt-4 p-4 bg-white/80 rounded-lg border border-gray-300">
            <div class="flex flex-col gap-3">
                <div>
                    <span class="text-sm text-gray-600 block mb-2">排序方式:</span>
                    <div class="flex gap-2">
                        <a href="{{ url_for('main.index', search=search, category=category_slug, sort='latest') }}" 
                           class="px-3 py-1.5 text-sm font-medium rounded-md transition-colors {{ 'bg-blue-500 text-white' if sort_by == 'latest' else 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                            最新
                        </a>
                        <a href="{{ url_for('main.index', search=search, category=category_slug, sort='popular') }}" 
                           class="px-3 py-1.5 text-sm font-medium rounded-md transition-colors {{ 'bg-blue-500 text-white' if sort_by == 'popular' else 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                            热门
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 短剧网格 -->
    {% if plays.items %}
    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 mb-8">
        {% for play in plays.items %}
        <div class="group cursor-pointer" @click="window.location.href='{{ url_for('main.play_detail', id=play.id) }}'">
            <div class="bg-white rounded-lg shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden">
                <!-- 海报图片 -->
                <div class="relative aspect-[3/4] bg-gray-200">
                    {% if play.poster_url %}
                    <img src="{{ play.poster_url }}" 
                         alt="{{ play.title }}" 
                         class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                         loading="lazy">
                    {% else %}
                    <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-400 to-purple-500">
                        <svg class="h-12 w-12 text-white/80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                        </svg>
                    </div>
                    {% endif %}
                    
                    <!-- 分类标签 -->
                    <div class="absolute top-2 right-2 bg-black/70 text-white px-2 py-1 rounded text-xs">
                        {{ play.primary_category.name if play.primary_category else '未分类' }}
                    </div>
                </div>
                
                <!-- 信息区域 - 固定高度 -->
                <div class="p-3 h-24 flex flex-col justify-between">
                    <!-- 标题 - 固定2行 -->
                    <h3 class="font-medium text-gray-900 group-hover:text-blue-600 transition-colors text-sm leading-tight line-clamp-2 mb-1">
                        {{ play.title }}
                    </h3>
                    
                    <!-- 简介 - 固定1行 -->
                    <p class="text-xs text-gray-600 line-clamp-1 mb-2">
                        {% if play.description %}
                            {% if play.description|length > 25 %}
                                {{ play.description[:25] }}...
                            {% else %}
                                {{ play.description }}
                            {% endif %}
                        {% else %}
                            精彩短剧，值得观看
                        {% endif %}
                    </p>
                    
                    <!-- 元数据 - 固定1行 -->
                    <div class="flex items-center justify-between text-xs text-gray-500">
                        <div class="flex items-center gap-2">
                            {% if play.year %}
                            <span class="flex items-center gap-1">
                                <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                </svg>
                                {{ play.year }}
                            </span>
                            {% endif %}
                            {% if play.episodes %}
                            <span class="flex items-center gap-1">
                                <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                </svg>
                                {{ play.episodes }}
                            </span>
                            {% endif %}
                        </div>
                        {% if play.views %}
                        <span class="flex items-center gap-1">
                            <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                            </svg>
                            {{ play.views }}
                        </span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- 分页 -->
    {% if plays.pages > 1 %}
    <div class="flex flex-col items-center gap-4">
        <!-- 分页信息 -->
        <div class="text-sm text-gray-600">
            显示第 {{ (plays.page - 1) * plays.per_page + 1 }} - {{ plays.page * plays.per_page if plays.page * plays.per_page <= plays.total else plays.total }} 条，
            共 {{ plays.total }} 条记录，{{ plays.pages }} 页
        </div>
        
        <!-- 分页导航 -->
        <nav class="flex items-center gap-2" x-data="{ loading: false }">
            <!-- 首页 -->
            {% if plays.page > 1 %}
            <a href="{{ url_for('main.index', page=1, search=search, category=category_slug, sort=sort_by) }}" 
               class="px-3 py-2 rounded-md bg-white border border-gray-300 text-sm text-gray-700 hover:bg-gray-50 transition-colors touch-target"
               @click="loading = true">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7"/>
                </svg>
            </a>
            {% endif %}
            
            <!-- 上一页 -->
            {% if plays.has_prev %}
            <a href="{{ url_for('main.index', page=plays.prev_num, search=search, category=category_slug, sort=sort_by) }}" 
               class="px-3 py-2 rounded-md bg-white border border-gray-300 text-sm text-gray-700 hover:bg-gray-50 transition-colors touch-target"
               @click="loading = true">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                </svg>
                <span class="ml-1 hidden sm:inline">上一页</span>
            </a>
            {% endif %}
            
            <!-- 页码 -->
            {% set start_page = [1, plays.page - 2]|max %}
            {% set end_page = [plays.pages, plays.page + 2]|min %}
            
            {% if start_page > 1 %}
            <a href="{{ url_for('main.index', page=1, search=search, category=category_slug, sort=sort_by) }}" 
               class="px-3 py-2 rounded-md bg-white border border-gray-300 text-sm text-gray-700 hover:bg-gray-50 transition-colors touch-target"
               @click="loading = true">1</a>
            {% if start_page > 2 %}
            <span class="px-2 text-gray-400">…</span>
            {% endif %}
            {% endif %}
            
            {% for page_num in range(start_page, end_page + 1) %}
                {% if page_num != plays.page %}
                <a href="{{ url_for('main.index', page=page_num, search=search, category=category_slug, sort=sort_by) }}" 
                   class="px-3 py-2 rounded-md bg-white border border-gray-300 text-sm text-gray-700 hover:bg-gray-50 transition-colors touch-target"
                   @click="loading = true">
                    {{ page_num }}
                </a>
                {% else %}
                <span class="px-3 py-2 rounded-md bg-blue-500 text-white text-sm font-medium touch-target">
                    {{ page_num }}
                </span>
                {% endif %}
            {% endfor %}
            
            {% if end_page < plays.pages %}
            {% if end_page < plays.pages - 1 %}
            <span class="px-2 text-gray-400">…</span>
            {% endif %}
            <a href="{{ url_for('main.index', page=plays.pages, search=search, category=category_slug, sort=sort_by) }}" 
               class="px-3 py-2 rounded-md bg-white border border-gray-300 text-sm text-gray-700 hover:bg-gray-50 transition-colors touch-target"
               @click="loading = true">{{ plays.pages }}</a>
            {% endif %}
            
            <!-- 下一页 -->
            {% if plays.has_next %}
            <a href="{{ url_for('main.index', page=plays.next_num, search=search, category=category_slug, sort=sort_by) }}" 
               class="px-3 py-2 rounded-md bg-white border border-gray-300 text-sm text-gray-700 hover:bg-gray-50 transition-colors touch-target"
               @click="loading = true">
                <span class="mr-1 hidden sm:inline">下一页</span>
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                </svg>
            </a>
            {% endif %}
            
            <!-- 末页 -->
            {% if plays.page < plays.pages %}
            <a href="{{ url_for('main.index', page=plays.pages, search=search, category=category_slug, sort=sort_by) }}" 
               class="px-3 py-2 rounded-md bg-white border border-gray-300 text-sm text-gray-700 hover:bg-gray-50 transition-colors touch-target"
               @click="loading = true">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"/>
                </svg>
            </a>
            {% endif %}
        </nav>
        
        <!-- 快速跳转 -->
        <div class="flex items-center gap-2 text-sm">
            <span class="text-gray-600">跳转到</span>
            <form method="GET" class="flex items-center gap-2" @submit.prevent="handlePageJump($event)">
                <input type="hidden" name="search" value="{{ search }}">
                <input type="hidden" name="category" value="{{ category_slug }}">
                <input type="hidden" name="sort" value="{{ sort_by }}">
                <input type="number" 
                       name="page" 
                       min="1" 
                       max="{{ plays.pages }}" 
                       value="{{ plays.page }}"
                       class="w-16 px-2 py-1 border border-gray-300 rounded text-center text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 touch-manipulation"
                       x-model="jumpPage">
                <button type="submit" 
                        class="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 transition-colors touch-target">
                    跳转
                </button>
            </form>
        </div>
    </div>
    {% endif %}
    
    {% else %}
    <!-- 空状态 -->
    <div class="text-center py-16">
        <svg class="mx-auto h-16 w-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.5-.676-6.332-1.84C7.614 11.223 9.23 10 12 10c2.77 0 4.386 1.223 6.332 3.16A7.962 7.962 0 0112 15z"/>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无短剧</h3>
        <p class="text-gray-600 mb-4">
            {% if search %}
            没有找到相关的短剧，试试其他关键词吧
            {% else %}
            暂时没有短剧内容，请稍后再来
            {% endif %}
        </p>
        {% if search %}
        <a href="{{ url_for('main.index') }}" 
           class="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors">
            浏览全部短剧
        </a>
        {% endif %}
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // 页面特定的Alpine.js组件
    function indexPageData() {
        return {
            plays: {{ plays_data | tojson }},
            currentPage: {{ plays.page }},
            totalPages: {{ plays.pages }},
            loading: false,
            filterLoading: false,
            
            init() {
                this.initInfiniteScroll();
                this.initFilterAnimations();
            },
            
            // 无限滚动加载
            initInfiniteScroll() {
                let isLoading = false;
                const threshold = 200; // 距离底部200px时开始加载
                
                window.addEventListener('scroll', () => {
                    if (isLoading || this.currentPage >= this.totalPages) return;
                    
                    const scrollY = window.scrollY;
                    const visible = document.documentElement.clientHeight;
                    const pageHeight = document.documentElement.scrollHeight;
                    const bottomOfPage = visible + scrollY >= pageHeight - threshold;
                    
                    if (bottomOfPage) {
                        this.loadMorePlays();
                    }
                });
            },
            
            // 加载更多短剧
            async loadMorePlays() {
                if (this.loading || this.currentPage >= this.totalPages) return;
                
                this.loading = true;
                try {
                    const nextPage = this.currentPage + 1;
                    const params = new URLSearchParams(window.location.search);
                    params.set('page', nextPage);
                    
                    const response = await fetch(`/api/plays?${params.toString()}`);
                    const data = await response.json();
                    
                    if (data.success && data.data.length > 0) {
                        this.plays = [...this.plays, ...data.data];
                        this.currentPage = nextPage;
                        
                        // 动画添加新项目
                        setTimeout(() => {
                            const newItems = document.querySelectorAll('.play-item:nth-last-child(-n+' + data.data.length + ')');
                            newItems.forEach((item, index) => {
                                setTimeout(() => {
                                    item.classList.add('animate-fade-in-up');
                                }, index * 100);
                            });
                        }, 100);
                        
                        showToast(`加载了 ${data.data.length} 部短剧`, 'info', 2000);
                    }
                } catch (error) {
                    console.error('加载失败:', error);
                    showToast('加载失败，请稍后再试', 'error');
                } finally {
                    this.loading = false;
                }
            },
            
            // 筛选动画
            initFilterAnimations() {
                // 监听筛选按钮点击
                document.querySelectorAll('a[href*="sort="]').forEach(link => {
                    link.addEventListener('click', (e) => {
                        if (!e.ctrlKey && !e.metaKey) {
                            this.filterLoading = true;
                            showPageLoader();
                        }
                    });
                });
            },
            
            // 快速跳转到分类
            goToCategory(slug) {
                this.filterLoading = true;
                showPageLoader();
                window.location.href = `/category/${slug}`;
            },
            
            // 收藏功能（示例）
            async toggleFavorite(playId) {
                try {
                    // 这里可以实现收藏逻辑
                    showToast('收藏功能开发中...', 'info');
                } catch (error) {
                    showToast('操作失败', 'error');
                }
            },
            
            // 验证链接功能 - 已移除
            
            // 处理页面跳转
            handlePageJump(event) {
                const formData = new FormData(event.target);
                const page = formData.get('page');
                const search = formData.get('search');
                const category = formData.get('category');
                const sort = formData.get('sort');
                
                if (page && page >= 1 && page <= this.totalPages) {
                    const params = new URLSearchParams();
                    if (page !== '1') params.set('page', page);
                    if (search) params.set('search', search);
                    if (category && category !== 'all') params.set('category', category);
                    if (sort && sort !== 'latest') params.set('sort', sort);
                    
                    const queryString = params.toString();
                    window.location.href = window.location.pathname + (queryString ? '?' + queryString : '');
                } else {
                    showToast('页码超出范围', 'error');
                }
            }
        }
    }
    
    // 键盘导航支持
    document.addEventListener('keydown', function(e) {
        // 按数字键快速排序
        if (e.key >= '1' && e.key <= '2') {
            e.preventDefault();
            const sortOptions = ['latest', 'popular'];
            const sortIndex = parseInt(e.key) - 1;
            if (sortIndex < sortOptions.length) {
                const params = new URLSearchParams(window.location.search);
                params.set('sort', sortOptions[sortIndex]);
                window.location.search = params.toString();
            }
        }
        
        // 按 / 键聚焦搜索框
        if (e.key === '/') {
            e.preventDefault();
            document.querySelector('input[name="q"]').focus();
        }
    });
    
    // 图片加载错误处理
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('img').forEach(img => {
            img.addEventListener('error', function() {
                this.style.display = 'none';
                const placeholder = this.nextElementSibling || this.parentElement.querySelector('.placeholder');
                if (placeholder) {
                    placeholder.style.display = 'flex';
                }
            });
        });
    });
</script>

<!-- 自定义样式 -->
<style>
    /* 文本截断样式 */
    .line-clamp-1 {
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    
    /* 卡片动画 */
    .group:hover {
        transform: translateY(-2px);
    }
    
    /* 确保卡片高度一致 */
    .grid > div {
        height: auto;
    }
</style>
{% endblock %}