#!/usr/bin/env python3
"""
测试夸克网盘分享API
尝试不同的API路径和域名
"""

import requests
import json

def test_share_apis():
    """测试不同的分享API路径"""
    
    # 读取Cookie
    try:
        with open("quark.cookie.txt", 'r', encoding='utf-8') as f:
            cookie = f.read().strip()
    except FileNotFoundError:
        print("❌ Cookie文件不存在")
        return
    
    # 设置请求头
    headers = {
        "Cookie": cookie,
        "Referer": "https://pan.quark.cn",
        "Origin": "https://pan.quark.cn",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Content-Type": "application/json;charset=UTF-8"
    }
    
    params = {"pr": "ucpro", "fr": "pc"}
    
    # 测试数据
    test_data = {
        "fid_list": ["0"],  # 使用根目录作为测试
        "title": "测试分享",
        "expired_type": 1,
        "url_type": 1
    }
    
    # 要测试的API组合
    api_combinations = [
        ("https://drive.quark.cn/1/clouddrive", "/share"),
        ("https://drive-pc.quark.cn/1/clouddrive", "/share"),
        ("https://drive-h.quark.cn/1/clouddrive", "/share"),
        ("https://drive.quark.cn/1/clouddrive", "/share/create"),
        ("https://drive-pc.quark.cn/1/clouddrive", "/share/create"),
        ("https://drive.quark.cn/1/clouddrive", "/file/share"),
        ("https://drive-pc.quark.cn/1/clouddrive", "/file/share"),
        ("https://drive.quark.cn/1/clouddrive", "/share/link"),
        ("https://drive-pc.quark.cn/1/clouddrive", "/share/link"),
    ]
    
    print("🔍 测试夸克网盘分享API...")
    print("=" * 60)
    
    for base_url, endpoint in api_combinations:
        url = base_url + endpoint
        print(f"\n🔗 测试: {url}")
        
        try:
            session = requests.Session()
            session.headers.update(headers)
            
            response = session.post(url, params=params, json=test_data, timeout=30)
            
            print(f"📊 状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"📋 响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
                    
                    if result.get("code") == 0:
                        print("✅ API调用成功！")
                        task_id = result.get("data", {}).get("task_id")
                        if task_id:
                            print(f"🎯 获得任务ID: {task_id}")
                            return base_url, endpoint, task_id
                    else:
                        print(f"⚠️  API返回错误: {result.get('message', '未知错误')}")
                except json.JSONDecodeError:
                    print(f"📄 响应内容: {response.text[:200]}...")
            elif response.status_code == 404:
                print("❌ 404 - API路径不存在")
            elif response.status_code == 400:
                print("❌ 400 - 请求参数错误")
                try:
                    result = response.json()
                    print(f"📋 错误详情: {json.dumps(result, indent=2, ensure_ascii=False)}")
                except:
                    print(f"📄 响应内容: {response.text[:200]}...")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"📄 响应内容: {response.text[:200]}...")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求失败: {e}")
        except Exception as e:
            print(f"❌ 未知错误: {e}")
    
    print("\n❌ 所有API测试都失败了")
    return None, None, None

def test_task_api(base_url, task_id):
    """测试任务状态API"""
    
    # 读取Cookie
    try:
        with open("quark.cookie.txt", 'r', encoding='utf-8') as f:
            cookie = f.read().strip()
    except FileNotFoundError:
        print("❌ Cookie文件不存在")
        return
    
    headers = {
        "Cookie": cookie,
        "Referer": "https://pan.quark.cn",
        "Origin": "https://pan.quark.cn",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Accept": "application/json, text/plain, */*"
    }
    
    params = {
        "pr": "ucpro", 
        "fr": "pc",
        "task_id": task_id,
        "retry_index": 0
    }
    
    url = base_url + "/task"
    print(f"\n🔍 测试任务状态API: {url}")
    
    try:
        session = requests.Session()
        session.headers.update(headers)
        
        response = session.get(url, params=params, timeout=30)
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📋 任务状态: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get("code") == 0:
                task_data = result.get("data", {})
                status = task_data.get("status")
                print(f"🎯 任务状态: {status}")
                
                if status == 2:  # 成功
                    share_id = task_data.get("share_id")
                    if share_id:
                        print(f"✅ 分享ID: {share_id}")
                        return share_id
        
    except Exception as e:
        print(f"❌ 任务状态查询失败: {e}")
    
    return None

def main():
    """主函数"""
    print("夸克网盘分享API测试工具")
    print("=" * 50)
    
    # 测试分享API
    base_url, endpoint, task_id = test_share_apis()
    
    if base_url and task_id:
        print(f"\n🎉 找到可用的分享API: {base_url}{endpoint}")
        print(f"📝 任务ID: {task_id}")
        
        # 测试任务状态API
        share_id = test_task_api(base_url, task_id)
        
        if share_id:
            print(f"🎉 分享创建成功，分享ID: {share_id}")
        else:
            print("⚠️  分享任务可能还在处理中")
    else:
        print("\n💡 建议:")
        print("1. 检查Cookie是否有效")
        print("2. 确认网盘中有文件可以分享")
        print("3. 夸克网盘API可能已经更新")

if __name__ == "__main__":
    main()
