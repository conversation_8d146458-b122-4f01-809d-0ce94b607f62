# 上传分片

API： POST   上传域名 + /upload/v2/file/slice

说明：

+ 上传域名是`创建文件`接口响应中的`servers`
+ <font style="color:#DF2A3F;">Content-Type: multipart/form-data</font>

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#DF2A3F;">必填</font> | 鉴权access_token |
| Platform | string | <font style="color:#DF2A3F;">必填</font> | 固定为:open_platform |


## Body 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| preuploadID | string | <font style="color:#DF2A3F;">必填</font> | 预上传ID |
| sliceNo | number | <font style="color:#DF2A3F;">必填</font> | 分片序号，从1开始自增 |
| sliceMD5 | string | <font style="color:#DF2A3F;">必填</font> | 当前分片md5 |
| slice | file | <font style="color:#DF2A3F;">必填</font> | 分片二进制流 |


## 返回数据 
无

## 示例
**请求示例**

```shell
curl --request POST \
  --url http://openapi-upload-dev.123242.com/upload/v2/file/slice \
  --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5c...(过长省略)' \
  --header 'Platform: open_platform' \
  --header 'content-type: multipart/form-data' \
  --form preuploadID=WvjyUgonimrlI4sjB3sLG5sRBn3x43...(过长省略) \
  --form sliceNo=1 \
  --form sliceMD5=58f06dd588d8ffb3beb46ada6309436b \
  --form 'slice=@E:\新建文件夹\测试分片.txt.part1'
```

```java
OkHttpClient client = new OkHttpClient().newBuilder().build();
MediaType mediaType = MediaType.parse("text/plain");
RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM)
  .addFormDataPart("preuploadID","WvjyUgonimrlI4sjB3sLG5sRBn3x43...(过长省略)")
  .addFormDataPart("sliceNo","1")
  .addFormDataPart("sliceMD5","58f06dd588d8ffb3beb46ada6309436b")
  .addFormDataPart("slice","/D:/新建 文本文档 (4).txt",
    RequestBody.create(MediaType.parse("application/octet-stream"),
    new File("/D:/新建 文本文档 (4).txt")))
  .build();
Request request = new Request.Builder()
  .url("http://openapi-upload-dev.123242.com/upload/v2/file/slice")
  .method("POST", body)
  .addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5c...(过长省略)")
  .addHeader("Platform", "open_platform")
  .build();
Response response = client.newCall(request).execute();
```

```javascript
var form = new FormData();
form.append("preuploadID", "WvjyUgonimrlI4sjB3sLG5sRBn3x43...(过长省略)");
form.append("sliceNo", "1");
form.append("sliceMD5", "58f06dd588d8ffb3beb46ada6309436b");
form.append("slice", fileInput.files[0], "/D:/新建 文本文档 (4).txt");

var settings = {
  "url": "http://openapi-upload-dev.123242.com/upload/v2/file/slice",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5c...(过长省略)",
    "Platform": "open_platform"
  },
  "processData": false,
  "mimeType": "multipart/form-data",
  "contentType": false,
  "data": form
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
```

```javascript
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
let data = new FormData();
data.append('preuploadID', 'WvjyUgonimrlI4sjB3sLG5sRBn3x43...(过长省略)');
data.append('sliceNo', '1');
data.append('sliceMD5', '58f06dd588d8ffb3beb46ada6309436b');
data.append('slice', fs.createReadStream('/D:/新建 文本文档 (4).txt'));

let config = {
  method: 'post',
  maxBodyLength: Infinity,
  url: 'http://openapi-upload-dev.123242.com/upload/v2/file/slice',
  headers: { 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5c...(过长省略)', 
    'Platform': 'open_platform', 
    ...data.getHeaders()
  },
  data : data
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});

```

```python
import http.client
import mimetypes
from codecs import encode

conn = http.client.HTTPConnection("openapi-upload-dev.123242.com")
dataList = []
boundary = 'wL36Yn8afVp8Ag7AmP8qZ0SA4n1v9T'
dataList.append(encode('--' + boundary))
dataList.append(encode('Content-Disposition: form-data; name=preuploadID;'))

dataList.append(encode('Content-Type: {}'.format('text/plain')))
dataList.append(encode(''))

dataList.append(encode("WvjyUgonimrlI4sjB3sLG5sRBn3x43..."))
dataList.append(encode('--' + boundary))
dataList.append(encode('Content-Disposition: form-data; name=sliceNo;'))

dataList.append(encode('Content-Type: {}'.format('text/plain')))
dataList.append(encode(''))

dataList.append(encode("1"))
dataList.append(encode('--' + boundary))
dataList.append(encode('Content-Disposition: form-data; name=sliceMD5;'))

dataList.append(encode('Content-Type: {}'.format('text/plain')))
dataList.append(encode(''))

dataList.append(encode("58f06dd588d8ffb3beb46ada6309436b"))
dataList.append(encode('--' + boundary))
dataList.append(encode('Content-Disposition: form-data; name=slice; filename={0}'.format('/D:/新建 文本文档 (4).txt')))

fileType = mimetypes.guess_type('/D:/新建 文本文档 (4).txt')[0] or 'application/octet-stream'
dataList.append(encode('Content-Type: {}'.format(fileType)))
dataList.append(encode(''))

with open('/D:/新建 文本文档 (4).txt', 'rb') as f:
  dataList.append(f.read())
dataList.append(encode('--'+boundary+'--'))
dataList.append(encode(''))
body = b'\r\n'.join(dataList)
payload = body
headers = {
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5c...(过长省略)',
  'Platform': 'open_platform',
  'Content-type': 'multipart/form-data; boundary={}'.format(boundary)
}
conn.request("POST", "/upload/v2/file/slice", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

**响应示例**

```json
{
	"code": 0,
	"message": "ok",
	"data": null,
	"x-traceID": ""
}
```



> 更新: 2025-06-23 09:14:01  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/scs8yg89yz8immus>