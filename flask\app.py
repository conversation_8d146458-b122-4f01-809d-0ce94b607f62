import os
from flask import Flask
from flask_migrate import Migrate
from config import config
from models import db

migrate = Migrate()

def create_app(config_name=None):
    """应用工厂函数 - 生产环境"""
    # 强制使用生产环境配置
    config_name = 'production'
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 初始化扩展
    db.init_app(app)
    migrate.init_app(app, db)
    
    # 确保上传文件夹存在
    try:
        os.makedirs(app.config['UPLOAD_FOLDER'])
    except OSError:
        pass
    
    # 注册蓝图
    from routes.main import main_bp
    from routes.api import api_bp
    
    app.register_blueprint(main_bp)
    app.register_blueprint(api_bp, url_prefix='/api')
    
    # CLI命令
    @app.cli.command()
    def init_db():
        """初始化数据库"""
        from models import init_db, seed_data
        init_db()
        seed_data()
        print('生产数据库初始化完成！')
    
    @app.cli.command()
    def seed():
        """填充示例数据"""
        from models import seed_data
        seed_data()
        print('示例数据创建完成！')
        
    @app.cli.command()
    def reset():
        """重置数据库"""
        from models import db
        db.drop_all()
        db.create_all()
        print('数据库重置完成！')
    
    # 上下文处理器
    @app.context_processor
    def inject_categories():
        """向模板注入分类数据"""
        from models import Category, Play
        from sqlalchemy import func
        
        # 获取所有分类
        all_categories = Category.query.order_by(Category.order_index).all()
        
        # 获取热门分类（根据关联的短剧数量排序，取前6个，排除"全部"）并返回字典格式
        popular_categories_result = db.session.execute(
            db.select(Category, func.count(Play.id).label('play_count'))
            .join(Category.plays)
            .filter(Category.slug != 'all')
            .group_by(Category.id)
            .order_by(func.count(Play.id).desc())
            .limit(6)
        ).all()
        
        # 转换为字典格式，以便在模板中使用
        popular_categories = []
        for category, play_count in popular_categories_result:
            category_dict = {
                'id': category.id,
                'name': category.name,
                'slug': category.slug,
                'description': category.description,
                'play_count': play_count
            }
            popular_categories.append(category_dict)
        
        return dict(
            categories=all_categories,
            popular_categories=popular_categories
        )
    
    return app

# 创建应用实例
app = create_app()

if __name__ == '__main__':
    app.run(debug=False, host='0.0.0.0', port=5000)