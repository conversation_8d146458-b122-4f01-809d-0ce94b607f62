# 单步上传

API： POST   上传域名 + /upload/v2/file/single/create

说明：

+ 文件名要小于256个字符且不能包含以下任何字符："\/:*?|><
+ 文件名不能全部是空格
+ 此接口限制开发者上传单文件大小为1GB
+ 上传域名是`获取上传域名`接口响应中的域名
+ 此接口用于实现小文件单步上传一次HTTP请求交互即可完成上传

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#DF2A3F;">必填</font> | 鉴权access_token |
| Platform | string | <font style="color:#DF2A3F;">必填</font> | 固定为:open_platform |


## Body 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | --- |
| parentFileID | number | <font style="color:#DF2A3F;">必填</font> | 父目录id，上传到根目录时填写 0 |
| filename | string | <font style="color:#DF2A3F;">必填</font> | 文件名要小于255个字符且不能包含以下任何字符："\/:*?|><。（注：不能重名）<br/>containDir 为 true 时，传入路径+文件名，例如：/你好/123/测试文件.mp4 |
| etag | string | <font style="color:#DF2A3F;">必填</font> | 文件md5 |
| size | number | <font style="color:#DF2A3F;">必填</font> | 文件大小，单位为 byte 字节 |
| file | file | <font style="color:#DF2A3F;">必填</font> | 文件二进制流 |
| duplicate | number | 非必填 | 当有相同文件名时，文件处理策略（1保留两者，新文件名将自动添加后缀，2覆盖原文件） |
| containDir | bool | 非必填 | 上传文件是否包含路径，默认false |


## 返回数据 
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | --- |
| fileID | number | 必填 | 文件ID。当123云盘已有该文件,则会发生秒传。此时会将文件ID字段返回。唯一 |
| completed | bool | 必填 | 是否上传完成（如果 completed 为 true 时，则说明上传完成） |


## 示例
**请求示例**

```shell
curl --request POST \
  --url http://openapi-upload.123242.com/upload/v2/file/single/create \
  --header 'Authorization: Bearer eyJhbGciOiJIUzI1...(过长省略)' \
  --header 'Platform: open_platform' \
  --header 'content-type: multipart/form-data' \
  --form 'file=@C:\Users\<USER>\Downloads\测试.exe' \
  --form parentFileID=11522394 \
  --form 'filename=测试.exe' \
  --form etag=511215951b857390c3f30c17d0dae8ee \
  --form size=35763200
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
  .build();
MediaType mediaType = MediaType.parse("text/plain");
RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM)
  .addFormDataPart("file","/D:/新建 文本文档 (4).txt",
    RequestBody.create(MediaType.parse("application/octet-stream"),
    new File("/D:/新建 文本文档 (4).txt")))
  .addFormDataPart("parentFileID","11522394")
  .addFormDataPart("filename","新建 文本文档 (4).txt")
  .addFormDataPart("etag","511215951b857390c3f30c17d0dae8ee")
  .addFormDataPart("size","35763200")
  .build();
Request request = new Request.Builder()
  .url("http://openapi-upload.123242.com/upload/v2/file/single/create")
  .method("POST", body)
  .addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1...(过长省略)")
  .addHeader("Platform", "open_platform")
  .build();
Response response = client.newCall(request).execute();
```

```javascript
var form = new FormData();
form.append("file", fileInput.files[0], "/D:/新建 文本文档 (4).txt");
form.append("parentFileID", "11522394");
form.append("filename", "新建 文本文档(4).txt");
form.append("etag", "511215951b857390c3f30c17d0dae8ee");
form.append("size", "35763200");

var settings = {
  "url": "http://openapi-upload.123242.com/upload/v2/file/single/create",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "Authorization": "Bearer eyJhbGciOiJIUzI1...(过长省略)",
    "Platform": "open_platform"
  },
  "processData": false,
  "mimeType": "multipart/form-data",
  "contentType": false,
  "data": form
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
```

```javascript
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
let data = new FormData();
data.append('file', fs.createReadStream('/D:/新建 文本文档 (4).txt'));
data.append('parentFileID', '11522394');
data.append('filename', '新建 文本文档(4).txt');
data.append('etag', '511215951b857390c3f30c17d0dae8ee');
data.append('size', '35763200');

let config = {
  method: 'post',
  maxBodyLength: Infinity,
  url: 'http://openapi-upload.123242.com/upload/v2/file/single/create',
  headers: { 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1...(过长省略)', 
    'Platform': 'open_platform', 
    ...data.getHeaders()
  },
  data : data
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});

```

```python
import http.client
import mimetypes
from codecs import encode

conn = http.client.HTTPConnection("openapi-upload.123242.com")
dataList = []
boundary = 'wL36Yn8afVp8Ag7AmP8qZ0SA4n1v9T'
dataList.append(encode('--' + boundary))
dataList.append(encode('Content-Disposition: form-data; name=file; filename={0}'.format('/D:/新建 文本文档 (4).txt')))

fileType = mimetypes.guess_type('/D:/新建 文本文档 (4).txt')[0] or 'application/octet-stream'
dataList.append(encode('Content-Type: {}'.format(fileType)))
dataList.append(encode(''))

with open('/D:/新建 文本文档 (4).txt', 'rb') as f:
  dataList.append(f.read())
dataList.append(encode('--' + boundary))
dataList.append(encode('Content-Disposition: form-data; name=parentFileID;'))

dataList.append(encode('Content-Type: {}'.format('text/plain')))
dataList.append(encode(''))

dataList.append(encode("11522394"))
dataList.append(encode('--' + boundary))
dataList.append(encode('Content-Disposition: form-data; name=filename;'))

dataList.append(encode('Content-Type: {}'.format('text/plain')))
dataList.append(encode(''))

dataList.append(encode("新建 文本文档(4).txt"))
dataList.append(encode('--' + boundary))
dataList.append(encode('Content-Disposition: form-data; name=etag;'))

dataList.append(encode('Content-Type: {}'.format('text/plain')))
dataList.append(encode(''))

dataList.append(encode("511215951b857390c3f30c17d0dae8ee"))
dataList.append(encode('--' + boundary))
dataList.append(encode('Content-Disposition: form-data; name=size;'))

dataList.append(encode('Content-Type: {}'.format('text/plain')))
dataList.append(encode(''))

dataList.append(encode("35763200"))
dataList.append(encode('--'+boundary+'--'))
dataList.append(encode(''))
body = b'\r\n'.join(dataList)
payload = body
headers = {
  'Authorization': 'Bearer eyJhbGciOiJIUzI1...(过长省略)',
  'Platform': 'open_platform',
  'Content-type': 'multipart/form-data; boundary={}'.format(boundary)
}
conn.request("POST", "/upload/v2/file/single/create", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

**响应示例**

```json
{
	"code": 0,
	"message": "ok",
	"data": {
		"fileID": 11522653,
		"completed": true
	},
	"x-traceID": ""
}
```



> 更新: 2025-06-23 09:13:51  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/xhiht1uh3yp92pzc>