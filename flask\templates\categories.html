{% extends "base.html" %}

{% block title %}所有分类 - 短剧星球{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- 页面标题 -->
    <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">所有分类</h1>
        <p class="text-gray-600">探索不同类型的精彩短剧</p>
    </div>

    <!-- 分类网格 -->
    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
        {% for category in categories %}
        <a href="{{ url_for('main.category', slug=category.slug) }}" 
           class="group bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden card-hover">
            
            <!-- 分类图标背景 -->
            <div class="h-24 bg-gradient-to-br from-blue-500 to-purple-600 relative overflow-hidden">
                <!-- 装饰性图案 -->
                <div class="absolute inset-0 opacity-20">
                    <svg class="w-full h-full" viewBox="0 0 100 100" fill="none">
                        <circle cx="20" cy="20" r="15" fill="currentColor"/>
                        <circle cx="80" cy="80" r="20" fill="currentColor"/>
                        <circle cx="70" cy="30" r="10" fill="currentColor"/>
                    </svg>
                </div>
                
                <!-- 分类名称首字符 -->
                <div class="absolute inset-0 flex items-center justify-center">
                    <span class="text-2xl font-bold text-white">
                        {{ category.name[0] }}
                    </span>
                </div>
                
                <!-- 悬浮效果 -->
                <div class="absolute inset-0 bg-black opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
            </div>
            
            <!-- 分类信息 -->
            <div class="p-4">
                <h3 class="font-semibold text-gray-900 text-center mb-2 group-hover:text-blue-600 transition-colors">
                    {{ category.name }}
                </h3>
                
                <div class="flex items-center justify-center space-x-2 text-sm text-gray-500">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                    </svg>
                    <span>{{ category.play_count or 0 }} 部</span>
                </div>
            </div>
        </a>
        {% endfor %}
    </div>

    <!-- 返回首页 -->
    <div class="text-center mt-12">
        <a href="{{ url_for('main.index') }}" 
           class="inline-flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
            </svg>
            <span>返回首页</span>
        </a>
    </div>
</div>
{% endblock %}

{% block extra_head %}
<style>
/* 分类卡片渐变背景变化 */
.gradient-1 { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.gradient-2 { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.gradient-3 { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.gradient-4 { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
.gradient-5 { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
.gradient-6 { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }

/* 动态分配渐变 */
.group:nth-child(6n+1) .bg-gradient-to-br { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.group:nth-child(6n+2) .bg-gradient-to-br { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.group:nth-child(6n+3) .bg-gradient-to-br { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.group:nth-child(6n+4) .bg-gradient-to-br { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
.group:nth-child(6n+5) .bg-gradient-to-br { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
.group:nth-child(6n+6) .bg-gradient-to-br { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
</style>
{% endblock %}