# 获取复制失败文件列表

API： GET 域名 + /api/v1/oss/source/copy/fail

说明：查询图床复制任务失败文件列表（注：记录的是符合对应格式、大小的图片的复制失败原因）

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#000000;">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## QueryString 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| taskID | string | 必填 | 复制任务ID |
| limit | number | 必填 | 每页文件数量，最大不超过100 |
| page | number | 必填 | 页码数 |


## 返回数据
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| total | number | 必填 | 总数 |
| list | array | 必填 | 失败文件列表 |
|  | fileId | number | 文件Id |
|  | filename | string | 文件名 |


## 示例
请求示例

```shell
curl --location --request GET 'https://open-api.123pan.com/api/v1/oss/source/copy/fail' \
--header 'Content-Type: application/json' \
--header 'Platform: open_platform' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)' \
--data '{
    "taskID": "1ketmmu1000d86isuyq33su100w1bnp6",
    "limit": 100,
    "page": 1
}'
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
.build();
MediaType mediaType = MediaType.parse("application/json");
RequestBody body = RequestBody.create(mediaType, "{\n    \"taskID\": \"1ketmmu1000d86isuyq33su100w1bnp6\",\n    \"limit\": 100,\n    \"page\": 1\n}");
Request request = new Request.Builder()
.url("https://open-api.123pan.com/api/v1/oss/source/copy/fail")
.method("GET", body)
.addHeader("Content-Type", "application/json")
.addHeader("Platform", "open_platform")
.addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)")
.build();
Response response = client.newCall(request).execute();
```

```javascript
var settings = {
  "url": "https://open-api.123pan.com/api/v1/oss/source/copy/fail",
  "method": "GET",
  "timeout": 0,
  "headers": {
    "Content-Type": "application/json",
    "Platform": "open_platform",
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)"
  },
  "data": JSON.stringify({
    "taskID": "1ketmmu1000d86isuyq33su100w1bnp6",
    "limit": 100,
    "page": 1
  }),
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
```

```javascript
const axios = require('axios');
let data = JSON.stringify({
  "taskID": "1ketmmu1000d86isuyq33su100w1bnp6",
  "limit": 100,
  "page": 1
});

let config = {
  method: 'get',
  maxBodyLength: Infinity,
  url: 'https://open-api.123pan.com/api/v1/oss/source/copy/fail',
  headers: { 
    'Content-Type': 'application/json', 
    'Platform': 'open_platform', 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
  },
  data : data
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});

```

```python
import http.client
import json

conn = http.client.HTTPSConnection("open-api.123pan.com")
payload = json.dumps({
    "taskID": "1ketmmu1000d86isuyq33su100w1bnp6",
    "limit": 100,
    "page": 1
})
headers = {
    'Content-Type': 'application/json',
    'Platform': 'open_platform',
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
}
conn.request("GET", "/api/v1/oss/source/copy/fail", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

响应示例

```json
{
    "code": 0,
    "message": "ok",
    "data": {
        "total": 0,
        "list": null
    },
    "x-traceID": "61f58d82-056c-496c-abf8-c52adb2a52a9_kong-db-5898fdd8c6-wnv6h"
}
```



> 更新: 2025-03-17 19:16:58  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/tlug9od3xlw2w23v>