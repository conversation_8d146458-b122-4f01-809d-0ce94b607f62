<!DOCTYPE html>
<html lang="zh-CN" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>{% block title %}短剧星球 - 精彩短剧在线观看{% endblock %}</title>
    
    <!-- PWA配置 -->
    <link rel="manifest" href="/static/manifest.json">
    <meta name="theme-color" content="#16213e">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="短剧平台">
    
    <!-- 图标配置 -->
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/static/icons/icon-192x192.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/static/icons/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/static/icons/icon-16x16.png">
    
    <!-- SEO和社交媒体 -->
    <meta name="description" content="{% block description %}专业的短剧追播平台，海量精彩短剧随时观看，支持离线缓存{% endblock %}">
    <meta name="keywords" content="短剧,在线观看,追剧,重生,都市,古装">
    <meta property="og:title" content="{% block og_title %}短剧星球{% endblock %}">
    <meta property="og:description" content="{% block og_description %}专业的短剧追播平台{% endblock %}">
    <meta property="og:type" content="website">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- 自定义样式 -->
    <style>
        [x-cloak] { display: none !important; }
        
        /* 移动端优化 */
        .line-clamp-2 {
            overflow: hidden;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
        }
        
        /* 触摸优化 */
        .touch-manipulation {
            touch-action: manipulation;
        }
        
        /* 安全区域适配 */
        .safe-area-inset {
            padding-top: env(safe-area-inset-top);
            padding-bottom: env(safe-area-inset-bottom);
            padding-left: env(safe-area-inset-left);
            padding-right: env(safe-area-inset-right);
        }
        
        /* 防止橡皮筋效果 */
        body {
            overscroll-behavior: none;
        }
        
        /* 平滑滚动 */
        html {
            scroll-behavior: smooth;
        }
        
        /* 移动端点击反馈 */
        .tap-highlight {
            -webkit-tap-highlight-color: rgba(59, 130, 246, 0.3);
        }
        
        /* PWA安装按钮样式 */
        .pwa-install-banner {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #f1f5f9 0%, #e0f2fe 50%, #e0e7ff 100%);
        }
        
        .dark .gradient-bg {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #374151 100%);
        }
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .dark .glass-effect {
            background: rgba(0, 0, 0, 0.9);
        }
        
        /* 卡片悬浮效果优化 */
        .card-hover {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .card-hover:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        /* 移动端适配媒体查询 */
        @media (max-width: 768px) {
            .mobile-hidden {
                display: none;
            }
            
            .card-hover:hover {
                transform: none;
            }
            
            /* 优化移动端触摸目标大小 */
            .touch-target {
                min-height: 44px;
                min-width: 44px;
            }
        }
        
        /* 滑动手势支持 */
        .swipe-container {
            overflow-x: auto;
            scroll-snap-type: x mandatory;
            -webkit-overflow-scrolling: touch;
        }
        
        .swipe-item {
            scroll-snap-align: start;
        }
    </style>
    
    {% block extra_head %}{% endblock %}
</head>
<body class="h-full gradient-bg safe-area-inset" x-data="appData()" x-init="init()">
    <!-- PWA安装提示横幅 -->
    <div x-show="showInstallPrompt" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="transform -translate-y-full"
         x-transition:enter-end="transform translate-y-0"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="transform translate-y-0"
         x-transition:leave-end="transform -translate-y-full"
         class="fixed top-0 left-0 right-0 z-50 pwa-install-banner text-white p-4 shadow-lg">
        <div class="flex items-center justify-between max-w-screen-xl mx-auto">
            <div class="flex items-center space-x-3">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                </svg>
                <div>
                    <p class="font-medium">安装短剧平台应用</p>
                    <p class="text-sm opacity-90">离线观看，更流畅体验</p>
                </div>
            </div>
            <div class="flex items-center space-x-2">
                <button @click="installPWA()" 
                        class="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg text-sm font-medium transition-colors touch-target">
                    安装
                </button>
                <button @click="dismissInstallPrompt()" 
                        class="p-2 hover:bg-white/10 rounded-lg transition-colors touch-target">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>
    <!-- 导航栏 -->
    <header class="sticky top-0 z-50 glass-effect">
        <div class="container mx-auto px-4">
            <div class="flex h-16 items-center justify-between">
                <!-- Logo -->
                <a href="{{ url_for('main.index') }}" class="flex items-center space-x-2">
                    <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10l1 16H6L7 4z"/>
                    </svg>
                    <span class="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        短剧星球
                    </span>
                </a>

                <!-- 桌面导航 -->
                <nav class="hidden md:flex items-center space-x-1">
                    <a href="{{ url_for('main.index') }}" 
                       class="px-3 py-2 rounded-md text-sm font-medium hover:bg-white/20 transition-colors"
                       :class="{ 'bg-white/20': isCurrentPath('/') }">
                        首页
                    </a>
                    
                    <!-- 分类下拉菜单 -->
                    <div class="relative" x-data="{ open: false }" @click.away="open = false">
                        <button @click="open = !open" 
                                class="px-3 py-2 rounded-md text-sm font-medium hover:bg-white/20 transition-colors flex items-center space-x-1">
                            <span>分类</span>
                            <svg class="w-4 h-4 transition-transform duration-200" 
                                 :class="{ 'rotate-180': open }"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </button>
                        
                        <!-- 下拉菜单 -->
                        <div x-show="open" 
                             x-transition:enter="transition ease-out duration-100"
                             x-transition:enter-start="transform opacity-0 scale-95"
                             x-transition:enter-end="transform opacity-100 scale-100"
                             x-transition:leave="transition ease-in duration-75"
                             x-transition:leave-start="transform opacity-100 scale-100"
                             x-transition:leave-end="transform opacity-0 scale-95"
                             class="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 z-10 py-2">
                            
                            <!-- 热门分类 -->
                            <div class="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider border-b border-gray-100">
                                热门分类
                            </div>
                            {% for category in popular_categories %}
                            <a href="{{ url_for('main.category', slug=category.slug) }}" 
                               class="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-gray-900 flex items-center justify-between"
                               @click="open = false">
                                <span>{{ category.name }}</span>
                                <span class="bg-blue-100 text-blue-600 text-xs px-2 py-0.5 rounded-full">
                                    {{ category.play_count }}
                                </span>
                            </a>
                            {% endfor %}
                            
                            <!-- 查看所有分类 -->
                            <div class="border-t border-gray-100 mt-2 pt-2">
                                <a href="{{ url_for('main.categories') }}" 
                                   class="block px-3 py-2 text-sm text-blue-600 hover:bg-blue-50 hover:text-blue-700 font-medium"
                                   @click="open = false">
                                    查看所有分类 →
                                </a>
                            </div>
                        </div>
                    </div>
                </nav>

                <!-- 搜索栏 -->
                <form method="GET" action="{{ url_for('main.search') }}" 
                      class="flex-1 max-w-md mx-4 relative" 
                      @submit.prevent="handleSearch()">
                    <div class="relative">
                        <svg class="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m21 21-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                        <input type="search" 
                               name="q" 
                               placeholder="搜索短剧名称..." 
                               class="w-full pl-9 pr-4 py-2 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/80 touch-manipulation"
                               x-model="searchQuery"
                               @focus="showSuggestions = searchSuggestions.length > 0"
                               @blur="setTimeout(() => showSuggestions = false, 200)"
                               @touchstart="this.focus()"
                               autocomplete="off"
                               value="{{ request.args.get('q', '') }}">
                        
                        <!-- 搜索建议下拉框 -->
                        <div x-show="showSuggestions && searchSuggestions.length > 0" 
                             x-cloak
                             x-transition:enter="transition ease-out duration-100"
                             x-transition:enter-start="opacity-0 scale-95"
                             x-transition:enter-end="opacity-100 scale-100"
                             x-transition:leave="transition ease-in duration-75"
                             x-transition:leave-start="opacity-100 scale-100"
                             x-transition:leave-end="opacity-0 scale-95"
                             class="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto">
                            <template x-for="(suggestion, index) in searchSuggestions" :key="suggestion.id">
                                <div @click="selectSuggestion(suggestion)" 
                                     class="px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0 flex items-center gap-3">
                                    <div class="w-10 h-12 bg-gray-200 rounded flex-shrink-0 flex items-center justify-center overflow-hidden">
                                        <template x-if="suggestion.poster_url">
                                            <img :src="suggestion.poster_url" 
                                                 :alt="suggestion.title" 
                                                 class="w-full h-full object-cover">
                                        </template>
                                        <template x-if="!suggestion.poster_url">
                                            <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                            </svg>
                                        </template>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <div class="text-sm font-medium text-gray-900 truncate" x-text="suggestion.title"></div>
                                        <div class="text-xs text-gray-500 flex items-center gap-2">
                                            <span x-text="suggestion.category.name"></span>
                                        </div>
                                    </div>
                                </div>
                            </template>
                            
                            <!-- 查看更多搜索结果 -->
                            <div class="px-4 py-2 bg-gray-50 border-t border-gray-200">
                                <div @click="handleSearch()" class="text-sm text-blue-600 hover:text-blue-800 cursor-pointer">
                                    查看所有"<span x-text="searchQuery"></span>"相关结果
                                </div>
                            </div>
                        </div>
                    </div>
                </form>

                <!-- 移动端菜单按钮 -->
                <button @click="mobileMenuOpen = !mobileMenuOpen" 
                        class="md:hidden p-2 rounded-md hover:bg-white/20 touch-target touch-manipulation tap-highlight"
                        :aria-expanded="mobileMenuOpen"
                        aria-label="切换菜单">
                    <svg class="h-6 w-6 transition-transform duration-200" 
                         :class="{ 'rotate-90': mobileMenuOpen }"
                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              :d="mobileMenuOpen ? 'M6 18L18 6M6 6l12 12' : 'M4 6h16M4 12h16M4 18h16'"/>
                    </svg>
                </button>
            </div>
        </div>

        <!-- 移动端菜单 -->
        <div x-show="mobileMenuOpen" 
             x-cloak
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 -translate-y-2"
             x-transition:enter-end="opacity-100 translate-y-0"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100 translate-y-0"
             x-transition:leave-end="opacity-0 -translate-y-2"
             class="md:hidden border-t border-gray-200 bg-white/95 backdrop-blur-sm"
             @click.outside="mobileMenuOpen = false">
            <div class="px-4 py-3 space-y-1 max-h-80 overflow-y-auto touch-manipulation">
                <a href="{{ url_for('main.index') }}" 
                   class="block px-4 py-3 rounded-lg text-base font-medium hover:bg-blue-50 transition-colors touch-target tap-highlight"
                   @click="mobileMenuOpen = false">
                    <div class="flex items-center space-x-3">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                        </svg>
                        <span>首页</span>
                    </div>
                </a>
                {% for category in popular_categories %}
                    {% if category.slug != 'all' %}
                    <a href="{{ url_for('main.category', slug=category.slug) }}" 
                       class="block px-4 py-3 rounded-lg text-base font-medium hover:bg-blue-50 transition-colors touch-target tap-highlight"
                       @click="mobileMenuOpen = false">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                                </svg>
                                <span>{{ category.name }}</span>
                            </div>
                            {% if category.play_count %}
                            <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-medium">
                                {{ category.play_count }}
                            </span>
                            {% endif %}
                        </div>
                    </a>
                    {% endif %}
                {% endfor %}
                
                <!-- 查看所有分类链接 -->
                <a href="{{ url_for('main.categories') }}" 
                   class="block px-4 py-3 rounded-lg text-base font-medium text-blue-600 hover:bg-blue-50 transition-colors touch-target tap-highlight border-t border-gray-100 mt-2 pt-4"
                   @click="mobileMenuOpen = false">
                    <div class="flex items-center space-x-3">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                        </svg>
                        <span>查看所有分类</span>
                    </div>
                </a>
            </div>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="min-h-screen pb-8">
        {% block content %}{% endblock %}
    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-800 text-white">
        <div class="container mx-auto px-4 py-8">
            <div class="text-center">
                <p class="text-sm text-gray-400">
                    © 2024 短剧星球. All rights reserved.
                </p>
                <p class="text-xs text-gray-500 mt-2">
                    本站仅提供短剧信息展示，不存储任何视频文件
                </p>
            </div>
        </div>
    </footer>

    <!-- Alpine.js 应用数据 -->
    <script>
        // 主应用数据
        function appData() {
            return {
                mobileMenuOpen: false,
                searchQuery: '{{ request.args.get("q", "") }}',
                loading: false,
                searchSuggestions: [],
                showSuggestions: false,
                
                // PWA相关状态
                showInstallPrompt: false,
                installPromptEvent: null,
                isOnline: navigator.onLine,
                serviceWorkerReady: false,
                
                init() {
                    console.log('短剧星球 App initialized');
                    this.initKeyboardShortcuts();
                    this.initPWA();
                    this.initNetworkStatus();
                    this.initServiceWorker();
                    this.initSearchDebounce();
                },
                
                initKeyboardShortcuts() {
                    document.addEventListener('keydown', (e) => {
                        // 按 / 键聚焦搜索框
                        if (e.key === '/' && !['INPUT', 'TEXTAREA'].includes(e.target.tagName)) {
                            e.preventDefault();
                            document.querySelector('input[name="q"]').focus();
                        }
                        // 按 Escape 键关闭移动端菜单
                        if (e.key === 'Escape') {
                            this.mobileMenuOpen = false;
                            this.showSuggestions = false;
                        }
                    });
                },
                
                initSearchDebounce() {
                    let timeout;
                    this.$watch('searchQuery', (value) => {
                        clearTimeout(timeout);
                        if (value && value.length > 1) {
                            timeout = setTimeout(() => {
                                this.getSearchSuggestions(value);
                            }, 300);
                        } else {
                            this.showSuggestions = false;
                        }
                    });
                },
                
                async getSearchSuggestions(query) {
                    try {
                        const response = await fetch(`/api/search/suggestions?q=${encodeURIComponent(query)}&limit=5`);
                        const data = await response.json();
                        if (data.success && data.data.length > 0) {
                            this.searchSuggestions = data.data;
                            this.showSuggestions = true;
                        } else {
                            this.showSuggestions = false;
                        }
                    } catch (error) {
                        console.error('获取搜索建议失败:', error);
                        this.showSuggestions = false;
                    }
                },
                
                selectSuggestion(suggestion) {
                    this.searchQuery = suggestion.title;
                    this.showSuggestions = false;
                    this.handleSearch();
                },
                
                isCurrentPath(path) {
                    return window.location.pathname === path;
                },
                
                handleSearch() {
                    if (this.searchQuery.trim()) {
                        this.loading = true;
                        window.location.href = `{{ url_for('main.search') }}?q=${encodeURIComponent(this.searchQuery.trim())}`;
                    }
                },
                
                closeMobileMenu() {
                    this.mobileMenuOpen = false;
                },
                
                // 页面加载状态管理
                setLoading(isLoading) {
                    this.loading = isLoading;
                },
                
                // 平滑滚动到顶部
                scrollToTop() {
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                }
            }
        }
        
        // Toast 通知系统
        function showToast(message, type = 'success', duration = 3000) {
            // 移除已存在的toast
            const existingToast = document.querySelector('.toast-notification');
            if (existingToast) {
                existingToast.remove();
            }
            
            const toast = document.createElement('div');
            toast.className = `toast-notification fixed top-4 right-4 px-4 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300 translate-x-full opacity-0`;
            
            // 根据类型设置样式
            switch (type) {
                case 'success':
                    toast.classList.add('bg-green-500', 'text-white');
                    break;
                case 'error':
                    toast.classList.add('bg-red-500', 'text-white');
                    break;
                case 'warning':
                    toast.classList.add('bg-yellow-500', 'text-white');
                    break;
                case 'info':
                    toast.classList.add('bg-blue-500', 'text-white');
                    break;
                default:
                    toast.classList.add('bg-gray-800', 'text-white');
            }
            
            toast.innerHTML = `
                <div class="flex items-center gap-2">
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-white/80 hover:text-white">
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>
            `;
            
            document.body.appendChild(toast);
            
            // 显示动画
            setTimeout(() => {
                toast.classList.remove('translate-x-full', 'opacity-0');
            }, 100);
            
            // 自动隐藏
            setTimeout(() => {
                toast.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => {
                    if (toast.parentElement) {
                        toast.remove();
                    }
                }, 300);
            }, duration);
        }
        
        // 复制到剪贴板功能
        async function copyToClipboard(text, button) {
            try {
                if (navigator.clipboard && window.isSecureContext) {
                    await navigator.clipboard.writeText(text);
                } else {
                    // 降级方案
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.position = 'fixed';
                    textArea.style.left = '-999999px';
                    textArea.style.top = '-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    document.execCommand('copy');
                    textArea.remove();
                }
                
                showToast('复制成功！', 'success');
                
                // 按钮状态反馈
                if (button) {
                    const originalText = button.textContent;
                    const originalClass = button.className;
                    button.textContent = '已复制';
                    button.classList.add('bg-green-600');
                    
                    setTimeout(() => {
                        button.textContent = originalText;
                        button.className = originalClass;
                    }, 2000);
                }
            } catch (error) {
                console.error('复制失败:', error);
                showToast('复制失败，请手动复制', 'error');
            }
        }
        
        // 页面加载动画
        function showPageLoader() {
            const loader = document.createElement('div');
            loader.id = 'page-loader';
            loader.className = 'fixed inset-0 bg-white/80 backdrop-blur-sm z-50 flex items-center justify-center';
            loader.innerHTML = `
                <div class="flex flex-col items-center gap-4">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                    <p class="text-gray-600">加载中...</p>
                </div>
            `;
            document.body.appendChild(loader);
        }
        
        function hidePageLoader() {
            const loader = document.getElementById('page-loader');
            if (loader) {
                loader.style.opacity = '0';
                setTimeout(() => loader.remove(), 200);
            }
        }
        
        // 图片懒加载
        function initLazyLoading() {
            const images = document.querySelectorAll('img[loading="lazy"]');
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src || img.src;
                        img.classList.remove('opacity-0');
                        img.classList.add('opacity-100');
                        observer.unobserve(img);
                    }
                });
            });
            
            images.forEach(img => {
                img.classList.add('opacity-0', 'transition-opacity', 'duration-300');
                imageObserver.observe(img);
            });
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initLazyLoading();
            
            // 为所有链接添加加载状态
            document.querySelectorAll('a[href^="/"]').forEach(link => {
                link.addEventListener('click', function(e) {
                    if (!e.ctrlKey && !e.metaKey) {
                        showPageLoader();
                    }
                });
            });
        });
        
        // 页面可见性变化处理
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                // 页面重新可见时可以刷新数据
                console.log('页面重新可见');
            }
        });
        
        // PWA功能函数
        function addPWAMethods(scope) {
            // 初始化PWA
            scope.initPWA = function() {
                // 监听PWA安装事件
                window.addEventListener('beforeinstallprompt', (e) => {
                    console.log('PWA安装提示事件触发');
                    e.preventDefault();
                    this.installPromptEvent = e;
                    
                    // 检查是否已安装或已显示过提示
                    const hasShownPrompt = localStorage.getItem('pwa-install-prompt-shown');
                    const isStandalone = window.matchMedia('(display-mode: standalone)').matches || 
                                        window.navigator.standalone === true;
                    
                    if (!hasShownPrompt && !isStandalone) {
                        // 延迟显示，提升用户体验
                        setTimeout(() => {
                            this.showInstallPrompt = true;
                        }, 3000);
                    }
                });
                
                // 监听PWA安装完成
                window.addEventListener('appinstalled', () => {
                    console.log('PWA安装完成');
                    this.showInstallPrompt = false;
                    this.showToast('应用安装成功！', 'success');
                });
            };
            
            // 安装PWA
            scope.installPWA = async function() {
                if (!this.installPromptEvent) {
                    this.showToast('当前浏览器不支持安装', 'warning');
                    return;
                }
                
                try {
                    const result = await this.installPromptEvent.prompt();
                    console.log('PWA安装结果:', result.outcome);
                    
                    if (result.outcome === 'accepted') {
                        this.showToast('正在安装应用...', 'info');
                    } else {
                        this.showToast('安装已取消', 'info');
                    }
                    
                    this.installPromptEvent = null;
                    this.showInstallPrompt = false;
                    localStorage.setItem('pwa-install-prompt-shown', 'true');
                } catch (error) {
                    console.error('PWA安装失败:', error);
                    this.showToast('安装失败，请稍后重试', 'error');
                }
            };
            
            // 关闭安装提示
            scope.dismissInstallPrompt = function() {
                this.showInstallPrompt = false;
                localStorage.setItem('pwa-install-prompt-shown', 'true');
                // 一周后重新显示
                setTimeout(() => {
                    localStorage.removeItem('pwa-install-prompt-shown');
                }, 7 * 24 * 60 * 60 * 1000);
            };
            
            // 初始化网络状态监听
            scope.initNetworkStatus = function() {
                const updateNetworkStatus = () => {
                    const wasOnline = this.isOnline;
                    this.isOnline = navigator.onLine;
                    
                    if (!wasOnline && this.isOnline) {
                        this.showToast('网络连接已恢复', 'success');
                    } else if (wasOnline && !this.isOnline) {
                        this.showToast('网络连接断开，正在使用离线模式', 'warning');
                    }
                };
                
                window.addEventListener('online', updateNetworkStatus);
                window.addEventListener('offline', updateNetworkStatus);
            };
            
            // 初始化Service Worker
            scope.initServiceWorker = function() {
                if ('serviceWorker' in navigator) {
                    navigator.serviceWorker.register('/static/sw.js')
                        .then((registration) => {
                            console.log('Service Worker注册成功:', registration);
                            this.serviceWorkerReady = true;
                            
                            // 监听Service Worker更新
                            registration.addEventListener('updatefound', () => {
                                const newWorker = registration.installing;
                                newWorker.addEventListener('statechange', () => {
                                    if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                        // 有新版本可用
                                        this.showUpdatePrompt();
                                    }
                                });
                            });
                        })
                        .catch((error) => {
                            console.error('Service Worker注册失败:', error);
                        });
                }
            };
            
            // 显示更新提示
            scope.showUpdatePrompt = function() {
                if (confirm('发现新版本，是否立即更新？')) {
                    window.location.reload();
                }
            };
        }
        
        // 为appData添加PWA方法
        const originalAppData = window.appData;
        window.appData = function() {
            const scope = originalAppData();
            addPWAMethods(scope);
            return scope;
        };
    </script>

    {% block extra_scripts %}{% endblock %}
</body>
</html>