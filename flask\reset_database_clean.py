#!/usr/bin/env python3
"""
MariaDB数据库完全清空脚本
用于完全重置生产环境数据库，不添加任何示例数据
"""

import os
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def reset_database_clean():
    """完全清空MariaDB数据库，不添加示例数据"""
    try:
        from app import create_app
        from models import db
        
        # 强制使用生产环境
        os.environ['FLASK_CONFIG'] = 'production'
        app = create_app('production')
        
        print("=== MariaDB数据库完全清空 ===")
        print("连接到生产环境数据库 (MariaDB)")
        print("数据库: sql23721_duanji@160.30.208.2")
        
        with app.app_context():
            print("\n警告: 此操作将完全清空数据库中的所有数据!")
            print("包括: 短剧数据、分类数据、播放链接数据")
            
            # 确认操作
            confirm = input("\n确认要继续吗? 输入 'YES' 继续: ")
            if confirm != 'YES':
                print("操作已取消")
                return False
            
            print("\n开始清空数据库...")
            
            # 删除所有表
            try:
                db.drop_all()
                print("+ 所有表已删除")
            except Exception as e:
                print(f"删除表时出现警告: {e}")
            
            # 重新创建表结构
            db.create_all()
            print("+ 表结构已重新创建")
            
            # 提交更改
            db.session.commit()
            
            print("\n=== 数据库清空完成 ===")
            print("数据库状态: 完全清空，仅保留表结构")
            print("表结构: Category, Play, PlayLink, play_categories")
            print("数据记录: 0 条")
            
            return True
            
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保在正确的Flask环境中运行")
        return False
    except Exception as e:
        print(f"重置失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_database_status():
    """检查数据库状态"""
    try:
        from app import create_app
        from models import db, Category, Play, PlayLink
        
        os.environ['FLASK_CONFIG'] = 'production'
        app = create_app('production')
        
        with app.app_context():
            category_count = Category.query.count()
            play_count = Play.query.count()
            link_count = PlayLink.query.count()
            
            print(f"\n=== 数据库状态 ===")
            print(f"分类数量: {category_count}")
            print(f"短剧数量: {play_count}")
            print(f"播放链接: {link_count}")
            print(f"总记录数: {category_count + play_count + link_count}")
            
            return category_count + play_count + link_count == 0
            
    except Exception as e:
        print(f"检查状态失败: {e}")
        return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="MariaDB数据库完全清空工具")
    parser.add_argument("--check", action="store_true", help="仅检查数据库状态")
    parser.add_argument("--force", action="store_true", help="强制清空，无需确认")
    
    args = parser.parse_args()
    
    if args.check:
        is_empty = check_database_status()
        if is_empty:
            print("数据库为空")
        else:
            print("数据库包含数据")
        return
    
    # 显示警告信息
    print("WARNING: 这是一个危险操作!")
    print("此脚本将完全清空MariaDB生产数据库中的所有数据")
    print("操作不可逆，请确保您知道自己在做什么")
    
    if not args.force:
        print("\n如果确定要继续，请使用 --force 参数")
        print("例如: python reset_database_clean.py --force")
        return
    
    # 执行清空操作
    success = reset_database_clean()
    
    if success:
        # 验证清空结果
        print("\n正在验证清空结果...")
        is_empty = check_database_status()
        if is_empty:
            print("+ 数据库已完全清空")
        else:
            print("! 数据库可能未完全清空，请检查")
    else:
        print("X 数据库清空失败")

if __name__ == "__main__":
    main()