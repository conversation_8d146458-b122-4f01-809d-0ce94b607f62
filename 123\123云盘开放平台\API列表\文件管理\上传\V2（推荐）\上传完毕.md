# 上传完毕

API： POST   域名 + /upload/v2/file/upload_complete

说明：分片上传完成后请求

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#DF2A3F;">必填</font> | 鉴权access_token |
| Platform | string | <font style="color:#DF2A3F;">必填</font> | 固定为:open_platform |


## Body 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| preuploadID | string | <font style="color:#DF2A3F;">必填</font> | 预上传ID |


## 返回数据
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | --- |
| completed | bool | 必填 | 上传是否完成 |
| fileID | number | 必填 | 上传完成文件id |


## 示例
请求示例

```shell
curl --request POST \
  --url https://open-api.123pan.com/upload/v2/file/upload_complete \
  --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5c...(过长省略)' \
  --header 'Platform: open_platform' \
  --data '{
    "preuploadID":"WvjyUgonimrlI4sjB3sLG5sRBn3x43VRBx2dB...(过长省略)"
}'
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
  .build();
MediaType mediaType = MediaType.parse("application/json");
RequestBody body = RequestBody.create(mediaType, "{\"preuploadID\": \"WvjyUgonimrlI4sjB3sLG5sRBn3x43VRBx2dB...(过长省略)\"}");
Request request = new Request.Builder()
  .url("https://open-api.123pan.com/upload/v2/file/upload_complete")
  .method("POST", body)
  .addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5c...(过长省略)")
  .addHeader("Platform", "open_platform")
  .addHeader("Content-Type", "application/json")
  .build();
Response response = client.newCall(request).execute();
```

```javascript
OkHttpClient client = new OkHttpClient().newBuilder()
  .build();
MediaType mediaType = MediaType.parse("application/json");
RequestBody body = RequestBody.create(mediaType, "{\"preuploadID\": \"WvjyUgonimrlI4sjB3sLG5sRBn3x43VRBx2dB...(过长省略)\"}");
Request request = new Request.Builder()
  .url("https://open-api.123pan.com/upload/v2/file/upload_complete")
  .method("POST", body)
  .addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5c...(过长省略)")
  .addHeader("Platform", "open_platform")
  .addHeader("Content-Type", "application/json")
  .build();
Response response = client.newCall(request).execute();
```

```javascript
const axios = require('axios');
let data = JSON.stringify({
  "preuploadID": "WvjyUgonimrlI4sjB3sLG5sRBn3x43VRBx2dB...(过长省略)"
});

let config = {
  method: 'post',
  maxBodyLength: Infinity,
  url: 'https://open-api.123pan.com/upload/v2/file/upload_complete',
  headers: { 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5c...(过长省略)', 
    'Platform': 'open_platform', 
    'Content-Type': 'application/json'
  },
  data : data
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});

```

```python
import http.client
import json

conn = http.client.HTTPSConnection("open-api.123pan.com")
payload = json.dumps({
  "preuploadID": "WvjyUgonimrlI4sjB3sLG5sRBn3x43VRBx2dB...(过长省略)"
})
headers = {
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5c...(过长省略)',
  'Platform': 'open_platform',
  'Content-Type': 'application/json'
}
conn.request("POST", "/upload/v2/file/upload_complete", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

响应示例

```json
{
	"code": 0,
	"message": "ok",
	"data": {
		"completed": true,
		"fileID": 11522654
	},
	"x-traceID": "65562117-5c67-4d69-98cb-0f65201f83d3_test-kong-7787db5b5-wggzb"
}
```



> 更新: 2025-06-23 09:14:06  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/fzzc5o8gok517720>