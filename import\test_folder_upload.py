#!/usr/bin/env python3
"""
测试文件夹上传功能
"""

import os
import sys
from pathlib import Path

# 添加网盘模块路径
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.append(str(project_root / "123"))
sys.path.append(str(project_root / "quark"))

try:
    from upload_share import YunPan123Client
    print("✅ 123网盘模块导入成功")
except ImportError as e:
    print(f"❌ 123网盘模块导入失败: {e}")
    YunPan123Client = None

try:
    from quark import QuarkPan
    print("✅ 夸克网盘模块导入成功")
except ImportError as e:
    print(f"❌ 夸克网盘模块导入失败: {e}")
    QuarkPan = None

def test_123_folder_upload():
    """测试123网盘文件夹上传"""
    if not YunPan123Client:
        print("❌ 123网盘客户端不可用")
        return
    
    try:
        config_path = str(project_root / "123" / "config.json")
        client = YunPan123Client(config_path)
        
        # 检查是否有upload_folder_and_share方法
        if hasattr(client, 'upload_folder_and_share'):
            print("✅ 123网盘支持文件夹上传")
        else:
            print("❌ 123网盘不支持文件夹上传")
            # 检查其他可能的方法
            methods = [method for method in dir(client) if 'upload' in method.lower()]
            print(f"可用的上传方法: {methods}")
            
    except Exception as e:
        print(f"❌ 123网盘测试失败: {e}")

def test_quark_folder_upload():
    """测试夸克网盘文件夹上传"""
    if not QuarkPan:
        print("❌ 夸克网盘客户端不可用")
        return
    
    try:
        config_path = str(project_root / "quark" / "config.yml")
        
        # 临时切换到quark目录
        original_cwd = os.getcwd()
        try:
            os.chdir(str(project_root / "quark"))
            client = QuarkPan(config_path)
            
            # 检查是否有upload_folder方法
            if hasattr(client, 'upload_folder'):
                print("✅ 夸克网盘支持文件夹上传")
            else:
                print("❌ 夸克网盘不支持文件夹上传")
                # 检查其他可能的方法
                methods = [method for method in dir(client) if 'upload' in method.lower()]
                print(f"可用的上传方法: {methods}")
                
        finally:
            os.chdir(original_cwd)
            
    except Exception as e:
        print(f"❌ 夸克网盘测试失败: {e}")

def main():
    print("=== 测试文件夹上传功能 ===")
    test_123_folder_upload()
    print()
    test_quark_folder_upload()

if __name__ == "__main__":
    main()
