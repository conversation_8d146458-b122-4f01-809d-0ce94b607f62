# 视频转码列表（三方挂载应用授权使用）

API：GET 域名 + /api/v1/video/transcode/list

<font style="color:#DF2A3F;">注：此接口仅限授权</font>`<font style="color:#DF2A3F;">access_token</font>`<font style="color:#DF2A3F;">调用</font>

## <font style="color:rgb(38, 38, 38);">Header 参数</font>
| **<font style="color:rgb(38, 38, 38);">名称</font>** | **<font style="color:rgb(38, 38, 38);">类型</font>** | **<font style="color:rgb(38, 38, 38);">是否必填</font>** | **<font style="color:rgb(38, 38, 38);">说明</font>** |
| :---: | :---: | :---: | :---: |
| <font style="color:rgb(38, 38, 38);">Authorization</font> | <font style="color:rgb(38, 38, 38);">string</font> | <font style="color:rgb(0, 0, 0);">必填</font><font style="color:rgb(38, 38, 38);"></font> | <font style="color:rgb(38, 38, 38);">鉴权access_token</font> |
| <font style="color:rgb(38, 38, 38);">Platform</font> | <font style="color:rgb(38, 38, 38);">string</font> | <font style="color:rgb(38, 38, 38);">必填</font> | <font style="color:rgb(38, 38, 38);">固定为:open_platform</font> |


## QueryString 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| fileId | number | 是 | 文件id |


## 返回数据
| **名称** | **类型** | **是否必填** | **说明** |
| --- | :---: | :---: | --- |
| status | number | 是 | 转码状态[1：待转码；3：转码失败；254：部分成功；255：全部成功] |
| list | array | 是 | 视频转码列表 |
| list[*].url | string | 是 | 地址 |
| list[*].resolution | string | 是 | 分辨率 |
| list[*].duration | float | 是 | 转码后的时长（秒） |
| list[*].height | number | 是 | 高度 |
| list[*].status | number | 是 | 转码状态[255：全部成功] |
| list[*].mc | string | 是 | 存储集群 |
| list[*].bitRate | number | 是 | 码率 |
| list[*].progress | number | 是 | 转码进度 |
| list[*].updateAt | string | 是 | 更新时间 |


## 示例
**请求示例**

```shell
curl --location 'https://open-api.123pan.com/api/v1/video/transcode/list?fileId=14749954' \
--header 'Content-Type: application/json' \
--header 'Platform: open_platform' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)' \
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
.build();
MediaType mediaType = MediaType.parse("application/json");
RequestBody body = RequestBody.create(mediaType, "");
Request request = new Request.Builder()
.url("https://open-api.123pan.com/api/v1/video/transcode/list?fileId=14749954")
.method("GET", body)
.addHeader("Content-Type", "application/json")
.addHeader("Platform", "open_platform")
.addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)")
.build();
Response response = client.newCall(request).execute();
```

```javascript
var settings = {
  "url": "https://open-api.123pan.com/api/v1/video/transcode/list?fileId=14749954",
  "method": "GET",
  "timeout": 0,
  "headers": {
    "Content-Type": "application/json",
    "Platform": "open_platform",
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)"
  },
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
```

```javascript
const axios = require('axios');

let config = {
  method: 'get',
  maxBodyLength: Infinity,
  url: 'https://open-api.123pan.com/api/v1/video/transcode/list?fileId=14749954',
  headers: { 
    'Content-Type': 'application/json', 
    'Platform': 'open_platform', 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
  }
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});

```

```python
import http.client
import json

conn = http.client.HTTPSConnection("open-api.123pan.com")
payload = ''
headers = {
  'Content-Type': 'application/json',
  'Platform': 'open_platform',
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
}
conn.request("GET", "/api/v1/video/transcode/list?fileId=14749954", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

**响应示例**

```json
{
  "code": 0,
  "message": "ok",
  "data": {
    "list": [
      {
        "url": "https://download-v.cjjd09.com/m88/123-hls...(过长省略)",
        "resolution": "2160p",
        "duration": 19.83300018310547,
        "height": 2160,
        "status": 255,
        "mc": "m88_123-hls-888",
        "bitRate": 4000000,
        "progress": 100,
        "updateAt": "2024-07-11 14:56:19"
      }
    ],
    "status": 255
  },
  "x-traceID": ""
}
```



> 更新: 2025-03-17 19:17:23  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/tgg6g84gdrmyess5>