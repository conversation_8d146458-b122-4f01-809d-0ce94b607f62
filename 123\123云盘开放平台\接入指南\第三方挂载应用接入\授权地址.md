# 授权地址

```http
https://www.123pan.com/auth?client_id=__CLIENT_ID__&redirect_uri=__REDIRECT_URL__&scope=__SCOPE__&state=__STATE__
```

| **名称** | **类型****<font style="color:rgb(38, 38, 38);"></font>** | **是否必填****<font style="color:rgb(38, 38, 38);"></font>** | **说明****<font style="color:rgb(38, 38, 38);"></font>** |
| --- | :---: | :---: | --- |
| <font style="color:rgb(38, 38, 38);">__CLIENT_ID__</font> | <font style="color:rgb(38, 38, 38);">string</font> | <font style="color:rgb(38, 38, 38);">是</font> | <font style="color:rgb(38, 38, 38);">client_id（app_id）</font> |
| <font style="color:rgb(38, 38, 38);">__REDIRECT_URL__</font> | <font style="color:rgb(38, 38, 38);">string</font> | <font style="color:rgb(38, 38, 38);">是</font> | <font style="color:rgb(38, 38, 38);">回调地址</font> |
| <font style="color:rgb(38, 38, 38);">__SCOPE__</font> | <font style="color:rgb(38, 38, 38);">string</font> | <font style="color:rgb(38, 38, 38);">是</font> | <font style="color:rgb(38, 38, 38);">固定为：user:base,file:all:read,file:all:write</font> |
| <font style="color:rgb(38, 38, 38);">__STATE__</font> | <font style="color:rgb(38, 38, 38);">string</font> | <font style="color:rgb(38, 38, 38);">是</font> | <font style="color:rgb(38, 38, 38);">自定义参数</font> |


**<font style="color:rgb(38, 38, 38);">请求示例</font>**

```http
https://www.123pan.com/auth?client_id=s23oy2xv3efafyut6yf7tf8vnbn96soz&redirect_uri=vidhub%3A%2F%2Foauth-callback%2Faliyundrive&scope=user:base,file:all:read,file:all:write&state=VidHubStateiOS
```



> 更新: 2025-03-17 19:17:16  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/gr7ggimkcysm18ap>