#!/usr/bin/env python3
"""
测试qBittorrent上传管理器
"""

from qb_upload_manager import QBUploadManager

def test_title_extraction():
    """测试标题提取功能"""
    print("=== 测试标题提取功能 ===")
    
    manager = QBUploadManager()
    
    test_cases = [
        "侯门主母温黎传.Hou.Men.Zhu.Mu.Wen.Li.Chu<PERSON>.2025.S01.720p.WEB-DL.H265.AAC-GodDramas",
        "开到荼靡.Kai.Dao.Tu.Mi.2025.S01.1080p.WEB-DL.H264.AAC-GodDramas",
        "爆款女王.Bao.Kuan.Nu.Wang.2024.S01.720p.WEB-DL.H265.AAC-GodDramas",
        "豪门主母.Hao.Men.Zhu.Mu.2024.S01.1080p.WEB-DL.H264.AAC-GodDramas"
    ]
    
    for test_name in test_cases:
        extracted = manager.extract_drama_title(test_name)
        print(f"种子: {test_name}")
        print(f"提取: {extracted}")
        print("-" * 50)

def test_qb_connection():
    """测试qBittorrent连接"""
    print("\n=== 测试qBittorrent连接 ===")
    
    manager = QBUploadManager()
    torrents = manager.get_completed_torrents()
    
    print(f"找到 {len(torrents)} 个已完成的agsvpt种子")
    
    for i, torrent in enumerate(torrents[:3]):  # 只显示前3个
        print(f"\n种子 {i+1}:")
        print(f"  名称: {torrent['name']}")
        print(f"  路径: {torrent['save_path']}")
        print(f"  分类: {torrent.get('category', 'N/A')}")
        print(f"  状态: {torrent['state']}")
        
        # 测试标题提取
        extracted = manager.extract_drama_title(torrent['name'])
        print(f"  提取标题: {extracted}")

def test_database_match():
    """测试数据库匹配"""
    print("\n=== 测试数据库匹配 ===")
    
    manager = QBUploadManager()
    
    # 测试一些种子名称
    test_names = [
        "侯门主母温黎传.Hou.Men.Zhu.Mu.Wen.Li.Chuan.2025.S01.720p.WEB-DL.H265.AAC-GodDramas",
        "开到荼靡.Kai.Dao.Tu.Mi.2025.S01.1080p.WEB-DL.H264.AAC-GodDramas"
    ]
    
    for test_name in test_names:
        print(f"\n测试种子: {test_name}")
        play = manager.find_matching_play(test_name)
        if play:
            print(f"✅ 找到匹配短剧: {play.title} (ID: {play.id})")
        else:
            print("❌ 未找到匹配短剧")

if __name__ == "__main__":
    try:
        test_title_extraction()
        test_qb_connection()
        test_database_match()
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()
