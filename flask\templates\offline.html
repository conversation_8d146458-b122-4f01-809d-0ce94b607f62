<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>离线模式 - 短剧平台</title>
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1a1a2e',
                        secondary: '#16213e',
                        accent: '#0f3460'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-primary via-secondary to-accent min-h-screen flex items-center justify-center">
    <div class="max-w-md mx-auto px-6 text-center">
        <!-- 离线图标 -->
        <div class="mb-8">
            <div class="w-24 h-24 mx-auto bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm">
                <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M18.364 5.636l-12.728 12.728m0-12.728l12.728 12.728M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z"/>
                </svg>
            </div>
        </div>
        
        <!-- 标题和描述 -->
        <h1 class="text-2xl font-bold text-white mb-4">
            网络连接异常
        </h1>
        
        <p class="text-gray-300 mb-8 leading-relaxed">
            当前无法连接到网络，您可以浏览已缓存的内容或检查网络连接后重试。
        </p>
        
        <!-- 操作按钮 -->
        <div class="space-y-4">
            <button 
                onclick="window.location.reload()" 
                class="w-full bg-white/20 hover:bg-white/30 text-white font-medium py-3 px-6 rounded-lg backdrop-blur-sm transition-colors duration-200 border border-white/20">
                重新连接
            </button>
            
            <button 
                onclick="goHome()" 
                class="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105">
                返回首页
            </button>
        </div>
        
        <!-- 缓存内容提示 -->
        <div class="mt-12 p-4 bg-white/5 rounded-lg backdrop-blur-sm border border-white/10">
            <h3 class="text-white font-medium mb-2">离线功能</h3>
            <ul class="text-sm text-gray-300 space-y-1">
                <li>• 浏览已缓存的短剧内容</li>
                <li>• 查看离线保存的分类</li>
                <li>• 访问基本功能页面</li>
            </ul>
        </div>
        
        <!-- 网络状态指示器 -->
        <div class="mt-8 flex items-center justify-center space-x-2">
            <div id="network-status" class="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
            <span id="network-text" class="text-sm text-gray-400">离线状态</span>
        </div>
    </div>

    <script>
        // 网络状态监测
        function updateNetworkStatus() {
            const statusDot = document.getElementById('network-status');
            const statusText = document.getElementById('network-text');
            
            if (navigator.onLine) {
                statusDot.className = 'w-3 h-3 bg-green-500 rounded-full';
                statusText.textContent = '网络已连接';
                
                // 网络恢复后自动刷新
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                statusDot.className = 'w-3 h-3 bg-red-500 rounded-full animate-pulse';
                statusText.textContent = '离线状态';
            }
        }
        
        // 返回首页
        function goHome() {
            try {
                window.location.href = '/';
            } catch (error) {
                console.log('无法导航到首页');
            }
        }
        
        // 监听网络状态变化
        window.addEventListener('online', updateNetworkStatus);
        window.addEventListener('offline', updateNetworkStatus);
        
        // 初始化网络状态
        updateNetworkStatus();
        
        // 定期检查网络状态
        setInterval(updateNetworkStatus, 3000);
        
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 页面加载动画
            document.body.style.opacity = '0';
            document.body.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                document.body.style.transition = 'all 0.5s ease-out';
                document.body.style.opacity = '1';
                document.body.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>