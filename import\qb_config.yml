# qBittorrent上传管理器配置文件

# qBittorrent配置
qbittorrent:
  host: "***********"
  port: 49850
  username: "admin"
  password: "507877550@lihao"

  # 种子过滤配置
  category: "agsvpt" # 只处理此分类的种子
  download_path: "/downloads/agsvpt" # qBittorrent内部路径
  host_download_path: "/vol1/1000/downloads/agsvpt" # 宿主机实际路径

# MariaDB数据库配置
database:
  host: "************"
  user: "sql23721_duanji"
  password: "507877550@lihao"
  database: "sql23721_duanji"
  charset: "utf8mb4"

# 网盘配置
cloud_storage:
  # 123网盘配置
  yunpan123:
    enabled: true
    config_path: "../123/config.json"
    upload_directory: "/网站/短剧/2025" # 上传目录

  # 夸克网盘配置
  quark:
    enabled: true
    config_path: "../quark/config.yml"

  # 上传策略配置
  upload_strategy: "dual" # dual: 双网盘上传, single: 单网盘上传
  preferred: "quark" # 首选网盘 (quark 或 yunpan123)

# 运行配置
runtime:
  # 监控间隔（秒）
  monitor_interval: 300

  # 文件处理配置
  enable_fuzzy_search: true # 启用模糊文件查找
  compress_folders: true # 自动压缩文件夹
  compression_format: "zip" # 压缩格式: zip, 7z, tar.gz

  # 日志配置
  log_level: "INFO" # DEBUG, INFO, WARNING, ERROR

  # 重试配置
  max_retries: 3
  retry_delay: 5 # 秒
