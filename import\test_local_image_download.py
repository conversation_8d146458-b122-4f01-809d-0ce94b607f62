#!/usr/bin/env python3
"""
本地图片下载功能测试脚本
测试修改后的 agsvpt_crawler.py 中的图片下载功能
"""

import sys
import os
import tempfile
sys.path.insert(0, os.path.dirname(__file__))

def test_download_function():
    """测试图片下载函数"""
    print("=== 测试图片下载函数 ===")
    
    try:
        # 导入修改后的爬虫模块
        from agsvpt_crawler import download_image_to_local
        
        # 使用临时目录进行测试，但要创建images子目录来模拟正确的结构
        test_base_dir = tempfile.mkdtemp()
        test_dir = os.path.join(test_base_dir, 'images')  # 模拟 static/images 结构
        print(f"测试目录: {test_dir}")
        
        # 测试图片URL
        test_url = "https://httpbin.org/image/jpeg"
        
        print(f"测试URL: {test_url}")
        
        # 调用下载函数
        result = download_image_to_local(test_url, test_dir)
        
        if result:
            print(f"[OK] 下载成功: {result}")
            
            # 检查文件是否实际存在
            if result.startswith('/static/images/'):
                # 转换为实际文件路径检查
                # 移除 /static/images/ 前缀，并转换路径分隔符
                relative_path = result.replace('/static/images/', '').replace('/', os.sep)
                actual_file = os.path.join(test_dir, relative_path)
            else:
                actual_file = os.path.join(test_dir, result)
            
            print(f"检查文件: {actual_file}")
            
            if os.path.exists(actual_file):
                file_size = os.path.getsize(actual_file)
                print(f"[OK] 文件存在，大小: {file_size} bytes")
                
                # 检查路径格式
                import re
                if re.match(r'.*/\d{4}/\d{2}/\d{2}/\d+\.jpg$', result):
                    print("[OK] 路径格式正确")
                    return True
                else:
                    print(f"[FAIL] 路径格式错误: {result}")
                    return False
            else:
                print(f"[FAIL] 文件不存在: {actual_file}")
                return False
        else:
            print("[FAIL] 下载失败")
            return False
            
    except Exception as e:
        print(f"[ERROR] 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理测试目录
        try:
            import shutil
            if 'test_base_dir' in locals():
                shutil.rmtree(test_base_dir, ignore_errors=True)
        except:
            pass

def test_crawler_integration():
    """测试爬虫集成（模拟数据）"""
    print("\n=== 测试爬虫集成 ===")
    
    try:
        # 导入修改后的爬虫模块
        from agsvpt_crawler import import_to_database
        
        # 模拟爬虫数据
        mock_items = [
            {
                'title': '测试短剧：本地图片下载测试',
                'cover': 'https://httpbin.org/image/jpeg',  # 测试图片
                'episodes': 10,
                'year': '2024',
                'category': '测试分类',
                'synopsis': '这是一个测试短剧，用于验证本地图片下载功能',
                'link': 'https://example.com/test',
                'pub_date': '2024-08-02'
            }
        ]
        
        print("模拟数据:")
        for item in mock_items:
            print(f"  标题: {item['title']}")
            print(f"  封面: {item['cover']}")
        
        print("\n注意: 这个测试需要Flask环境才能完全运行")
        print("如果没有Flask环境，只会测试图片下载部分")
        
        # 由于可能没有Flask环境，我们只测试数据准备
        return True
        
    except Exception as e:
        print(f"[ERROR] 集成测试失败: {e}")
        return False

def show_implementation_details():
    """显示实现细节"""
    print("\n=== 实现细节 ===")
    
    details = [
        "[OK] 添加了必要的导入: datetime, urlparse, Path",
        "[OK] 创建了 download_image_to_local 函数",
        "[OK] 使用时间戳命名: YYYY/MM/DD/timestamp.jpg", 
        "[OK] 自动创建目录结构",
        "[OK] 添加User-Agent防止反爬",
        "[OK] 完整的错误处理",
        "[OK] 在 import_to_database 中集成",
        "[OK] 优先使用本地路径，失败时fallback到原URL"
    ]
    
    for detail in details:
        print(f"  {detail}")
    
    print("\n=== 文件路径设计 ===")
    print("  物理存储: ../flask/static/images/YYYY/MM/DD/timestamp.jpg")
    print("  数据库存储: /static/images/YYYY/MM/DD/timestamp.jpg")
    print("  Flask访问: http://域名/static/images/YYYY/MM/DD/timestamp.jpg")

def main():
    """主测试函数"""
    print("=" * 60)
    print("本地图片下载功能测试")
    print("=" * 60)
    
    # 显示实现细节
    show_implementation_details()
    
    # 测试下载函数
    test1_success = test_download_function()
    
    # 测试集成
    test2_success = test_crawler_integration()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if test1_success:
        print("[OK] 图片下载功能测试通过")
        print("[OK] 时间戳命名格式正确")
        print("[OK] 文件保存成功")
        
        print("\n使用方法:")
        print("1. 直接运行爬虫: python agsvpt_crawler.py")
        print("2. 指定数量: python agsvpt_crawler.py 5")
        print("3. 图片会自动下载到: ../flask/static/images/YYYY/MM/DD/")
        
    else:
        print("[WARN] 部分测试失败，请检查网络连接")
    
    return test1_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)