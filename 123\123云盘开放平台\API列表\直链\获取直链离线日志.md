# 获取直链离线日志

API：GET 域名 + /api/v1/direct-link/offline/logs

注：此接口需要开通开发者权益，并且仅限查询近30天的日志数据

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#000000;">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## QueryString 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| startHour | string | 必填 | 开始时间，格式：2025010115 |
| endHour | string | 必填 | 结束时间，格式：2025010116 |
| pageNum | number | 必填 | 页数，从1开始 |
| pageSize | number | 必填 | 分页大小 |


## 返回数据
| **名称** | | **类型** | **是否必填** | **说明** |
| --- | --- | :---: | :---: | :---: |
| total | | number | 必填 | 总数 |
| list | | array | 必填 |  |
|  | id | string | 必填 | 唯一id |
|  | fileName | string | 必填 | 文件名 |
|  | fileSize | number | 必填 | 文件大小（字节） |
|  | logTimeRange | string | 必填 | 日志时间范围 |
|  | downloadURL | string | 必填 | 下载地址 |


## 示例
**请求示例**

```shell
curl --location --request GET 'https://open-api.123pan.com/api/v1/direct-link/offline/logs' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...过长省略' \
--header 'Platform: open_platform' \
--header 'Content-Type: application/json' \
--data '{
    "startHour": "2025062001",
    "endHour": "2025062023",
    "pageNum": 1,
    "pageSize": 5
}'
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
  .build();
MediaType mediaType = MediaType.parse("application/json");
RequestBody body = RequestBody.create(mediaType, "{\r\n    \"startHour\": \"2025062001\",\r\n    \"endHour\": \"2025062023\",\r\n    \"pageNum\": 1,\r\n    \"pageSize\": 5\r\n}");
Request request = new Request.Builder()
  .url("https://open-api.123pan.com/api/v1/direct-link/offline/logs")
  .method("GET", body)
  .addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...过长省略")
  .addHeader("Platform", "open_platform")
  .addHeader("Content-Type", "application/json")
  .build();
Response response = client.newCall(request).execute();
```

```javascript
var settings = {
  "url": "https://open-api.123pan.com/api/v1/direct-link/offline/logs",
  "method": "GET",
  "timeout": 0,
  "headers": {
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...过长省略",
    "Platform": "open_platform",
    "Content-Type": "application/json"
  },
  "data": JSON.stringify({
    "startHour": "2025062001",
    "endHour": "2025062023",
    "pageNum": 1,
    "pageSize": 5
  }),
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
```

```javascript
const axios = require('axios');
let data = JSON.stringify({
  "startHour": "2025062001",
  "endHour": "2025062023",
  "pageNum": 1,
  "pageSize": 5
});

let config = {
  method: 'get',
  maxBodyLength: Infinity,
  url: 'https://open-api.123pan.com/api/v1/direct-link/offline/logs',
  headers: { 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...过长省略', 
    'Platform': 'open_platform', 
    'Content-Type': 'application/json'
  },
  data : data
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});

```

```python
import http.client
import json

conn = http.client.HTTPSConnection("open-api.123pan.com")
payload = json.dumps({
  "startHour": "2025062001",
  "endHour": "2025062023",
  "pageNum": 1,
  "pageSize": 5
})
headers = {
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...过长省略',
  'Platform': 'open_platform',
  'Content-Type': 'application/json'
}
conn.request("GET", "/api/v1/direct-link/offline/logs", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

**响应示例**

```json
{
    "code": 0,
    "message": "ok",
    "data": {
        "list": [
            {
                "id": 12,
                "fileName": "202506201516.gz",
                "fileSize": 317,
                "logTimeRange": "2025-06-20 15:00~16:00",
                "downloadURL": "https://m88.cjjd20.com/123-direct-link-logs/2025...过长省略"
            },
            {
                "id": 11,
                "fileName": "202506201516.gz",
                "fileSize": 195,
                "logTimeRange": "2025-06-20 15:00~16:00",
                "downloadURL": "https://m88.cjjd20.com/123-direct-link-logs/2025...过长省略"
            },
            {
                "id": 10,
                "fileName": "202506201314.log.gz",
                "fileSize": 208,
                "logTimeRange": "2025-06-20 13:00~14:00",
                "downloadURL": "https://m88.cjjd20.com/123-direct-link-logs/2025...过长省略"
            },
            {
                "id": 8,
                "fileName": "202506201213.gz",
                "fileSize": 195,
                "logTimeRange": "2025-06-20 12:00~13:00",
                "downloadURL": "https://m88.cjjd20.com/123-direct-link-logs/2025...过长省略"
            },
            {
                "id": 7,
                "fileName": "202506201213.gz",
                "fileSize": 195,
                "logTimeRange": "2025-06-20 12:00~13:00",
                "downloadURL": "https://m88.cjjd20.com/123-direct-link-logs/2025...过长省略"
            }
        ],
        "total": 0
    },
    "x-traceID": ""
}
```



> 更新: 2025-06-28 10:48:30  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/yz4bdynw9yx5erqb>