{% extends "base.html" %}

{% block title %}{{ category_name }} - 短剧星球{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- 分类标题和描述 -->
    <div class="text-center mb-8">
        <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">{{ category_name }}</h1>
        <p class="text-gray-600 max-w-2xl mx-auto">
            {% if category_slug == 'all' %}
            汇集各类精彩短剧，总有一款适合你的口味
            {% else %}
            精选{{ category_name }}，为你带来极致的观影体验
            {% endif %}
        </p>
    </div>

    <!-- 筛选和排序栏 -->
    <div class="mb-8" x-data="{ showFilters: false }">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <!-- 统计信息 -->
            <div class="text-gray-600">
                <span class="text-sm">共 <strong class="text-gray-900">{{ plays.total }}</strong> 部短剧</span>
            </div>
            
            <!-- 排序选项 -->
            <div class="flex items-center gap-4">
                <button @click="showFilters = !showFilters" 
                        class="md:hidden flex items-center gap-2 px-3 py-2 rounded-md bg-white/80 border border-gray-300 text-sm font-medium hover:bg-white/90">
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12"/>
                    </svg>
                    筛选排序
                </button>
                
                <div class="hidden md:flex items-center gap-2" :class="{ 'flex': showFilters, 'hidden md:flex': !showFilters }">
                    <span class="text-sm text-gray-600">排序:</span>
                    <div class="flex border border-gray-300 rounded-md overflow-hidden">
                        <a href="{{ url_for('main.category', slug=category_slug, sort='latest') }}" 
                           class="px-3 py-1.5 text-sm font-medium border-r border-gray-300 transition-colors {{ 'bg-blue-500 text-white' if sort_by == 'latest' else 'bg-white text-gray-700 hover:bg-gray-50' }}">
                            最新
                        </a>
                        <a href="{{ url_for('main.category', slug=category_slug, sort='popular') }}" 
                           class="px-3 py-1.5 text-sm font-medium transition-colors {{ 'bg-blue-500 text-white' if sort_by == 'popular' else 'bg-white text-gray-700 hover:bg-gray-50' }}">
                            热门
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 移动端筛选栏 -->
        <div x-show="showFilters" x-cloak x-transition class="md:hidden mt-4 p-4 bg-white/80 rounded-lg border border-gray-300">
            <div class="flex flex-col gap-3">
                <div>
                    <span class="text-sm text-gray-600 block mb-2">排序方式:</span>
                    <div class="flex gap-2">
                        <a href="{{ url_for('main.category', slug=category_slug, sort='latest') }}" 
                           class="px-3 py-1.5 text-sm font-medium rounded-md transition-colors {{ 'bg-blue-500 text-white' if sort_by == 'latest' else 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                            最新
                        </a>
                        <a href="{{ url_for('main.category', slug=category_slug, sort='popular') }}" 
                           class="px-3 py-1.5 text-sm font-medium rounded-md transition-colors {{ 'bg-blue-500 text-white' if sort_by == 'popular' else 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                            热门
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 面包屑导航 -->
    <nav class="flex items-center gap-2 text-sm text-gray-600 mb-6">
        <a href="{{ url_for('main.index') }}" class="hover:text-blue-600 transition-colors">首页</a>
        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
        </svg>
        <span class="text-gray-900 font-medium">{{ category_name }}</span>
    </nav>

    <!-- 短剧网格 -->
    {% if plays.items %}
    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 mb-8">
        {% for play in plays.items %}
        <div class="group cursor-pointer" @click="window.location.href='{{ url_for('main.play_detail', id=play.id) }}'">
            <div class="bg-white rounded-lg shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden">
                <!-- 海报图片 -->
                <div class="relative aspect-[3/4] bg-gray-200">
                    {% if play.poster_url %}
                    <img src="{{ play.poster_url }}" 
                         alt="{{ play.title }}" 
                         class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                         loading="lazy">
                    {% else %}
                    <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-400 to-purple-500">
                        <svg class="h-12 w-12 text-white/80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                        </svg>
                    </div>
                    {% endif %}
                    
                    <!-- 状态徽章 -->
                    {% if sort_by == 'latest' %}
                    <div class="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded text-xs">
                        最新
                    </div>
                    {% elif sort_by == 'popular' and loop.index <= 3 %}
                    <div class="absolute top-2 right-2 bg-orange-500 text-white px-2 py-1 rounded text-xs">
                        热门
                    </div>
                    {% endif %}
                </div>
                
                <!-- 信息区域 - 固定高度 -->
                <div class="p-3 h-24 flex flex-col justify-between">
                    <!-- 标题 - 固定2行 -->
                    <h3 class="font-medium text-gray-900 group-hover:text-blue-600 transition-colors text-sm leading-tight line-clamp-2 mb-1">
                        {{ play.title }}
                    </h3>
                    
                    <!-- 简介 - 固定1行 -->
                    <p class="text-xs text-gray-600 line-clamp-1 mb-2">
                        {% if play.description %}
                            {% if play.description|length > 25 %}
                                {{ play.description[:25] }}...
                            {% else %}
                                {{ play.description }}
                            {% endif %}
                        {% else %}
                            精彩短剧，值得观看
                        {% endif %}
                    </p>
                    
                    <!-- 元数据 - 固定1行 -->
                    <div class="flex items-center justify-between text-xs text-gray-500">
                        <div class="flex items-center gap-2">
                            {% if play.year %}
                            <span class="flex items-center gap-1">
                                <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                </svg>
                                {{ play.year }}
                            </span>
                            {% endif %}
                            {% if play.episodes %}
                            <span class="flex items-center gap-1">
                                <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                </svg>
                                {{ play.episodes }}
                            </span>
                            {% endif %}
                        </div>
                        {% if play.views %}
                        <span class="flex items-center gap-1">
                            <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                            </svg>
                            {{ play.views }}
                        </span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- 分页 -->
    {% if plays.pages > 1 %}
    <div class="flex justify-center">
        <nav class="flex items-center gap-2">
            {% if plays.has_prev %}
            <a href="{{ url_for('main.category', slug=category_slug, page=plays.prev_num, sort=sort_by) }}" 
               class="px-3 py-2 rounded-md bg-white border border-gray-300 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                上一页
            </a>
            {% endif %}
            
            {% for page_num in plays.iter_pages() %}
                {% if page_num %}
                    {% if page_num != plays.page %}
                    <a href="{{ url_for('main.category', slug=category_slug, page=page_num, sort=sort_by) }}" 
                       class="px-3 py-2 rounded-md bg-white border border-gray-300 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                        {{ page_num }}
                    </a>
                    {% else %}
                    <span class="px-3 py-2 rounded-md bg-blue-500 text-white text-sm font-medium">
                        {{ page_num }}
                    </span>
                    {% endif %}
                {% else %}
                <span class="px-2 text-gray-400">…</span>
                {% endif %}
            {% endfor %}
            
            {% if plays.has_next %}
            <a href="{{ url_for('main.category', slug=category_slug, page=plays.next_num, sort=sort_by) }}" 
               class="px-3 py-2 rounded-md bg-white border border-gray-300 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                下一页
            </a>
            {% endif %}
        </nav>
    </div>
    {% endif %}
    
    {% else %}
    <!-- 空状态 -->
    <div class="text-center py-16">
        <svg class="mx-auto h-16 w-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无{{ category_name }}短剧</h3>
        <p class="text-gray-600 mb-4">该分类下暂时没有短剧内容</p>
        <a href="{{ url_for('main.index') }}" 
           class="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors">
            浏览全部短剧
        </a>
    </div>
    {% endif %}

    <!-- 其他分类推荐 -->
    {% if category_slug != 'all' %}
    <div class="mt-12 bg-white rounded-lg shadow-lg p-6">
        <h2 class="text-xl font-bold text-gray-900 mb-6 flex items-center gap-2">
            <svg class="h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
            </svg>
            其他分类
        </h2>
        
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {% for other_category in categories %}
                {% if other_category.slug != 'all' and other_category.slug != category_slug %}
                <a href="{{ url_for('main.category', slug=other_category.slug) }}" 
                   class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors group">
                    <div>
                        <h3 class="font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
                            {{ other_category.name }}
                        </h3>
                        {% if other_category.play_count %}
                        <p class="text-sm text-gray-500">{{ other_category.play_count }} 部</p>
                        {% endif %}
                    </div>
                    <svg class="h-5 w-5 text-gray-400 group-hover:text-blue-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </a>
                {% endif %}
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>

<!-- 返回顶部按钮 -->
<button x-data="{ show: false }" 
        x-show="show" 
        @scroll.window="show = (window.pageYOffset > 400)"
        @click="window.scrollTo({top: 0, behavior: 'smooth'})"
        class="fixed bottom-6 right-6 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-all z-40">
    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"/>
    </svg>
</button>
{% endblock %}

{% block extra_scripts %}
<script>
    // 键盘导航支持
    document.addEventListener('keydown', function(e) {
        if (e.key === '/') {
            e.preventDefault();
            document.querySelector('input[name="q"]').focus();
        }
    });
</script>

<!-- 自定义样式 -->
<style>
    /* 文本截断样式 */
    .line-clamp-1 {
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    
    /* 卡片动画 */
    .group:hover {
        transform: translateY(-2px);
    }
</style>
{% endblock %}