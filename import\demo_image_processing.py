#!/usr/bin/env python3
"""
图片处理功能演示脚本
演示完整的封面图片下载、处理、上传B2流程
"""

import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

def demo_image_processing():
    """演示图片处理完整流程"""
    print("=== 图片处理功能演示 ===\n")
    
    try:
        from image_processor import create_image_processor
        
        # 创建图片处理器
        processor = create_image_processor()
        print("[OK] 图片处理器初始化完成")
        
        # 使用一个可靠的测试图片URL
        test_url = "https://httpbin.org/image/jpeg"
        
        print(f"\n演示URL: {test_url}")
        print("预期结果: https://djimage.denlu.top/2025/08/02/时间戳.jpg")
        
        # 执行完整处理流程
        try:
            result_url = processor.process_image_url(test_url)
            print(f"\n[OK] 处理成功!")
            print(f"  原始URL: {test_url}")
            print(f"  最终URL: {result_url}")
            
            # 验证URL格式
            if "djimage.denlu.top" in result_url and "/2025/08/02/" in result_url:
                print("[OK] URL格式符合预期")
                return True
            else:
                print("[WARN] URL格式与预期不符")
                return False
                
        except Exception as e:
            print(f"[ERROR] 处理失败: {e}")
            return False
            
    except Exception as e:
        print(f"[ERROR] 初始化失败: {e}")
        return False

def demo_crawler_integration():
    """演示与爬虫的集成使用"""
    print("\n=== 爬虫集成演示 ===\n")
    
    try:
        # 导入增强版爬虫模块
        from agsvpt_crawler_enhanced import process_cover_image, create_image_processor
        
        # 模拟爬虫数据
        mock_data = {
            "title": "测试短剧：霸总的小妻子",
            "cover": "https://httpbin.org/image/jpeg",
            "episodes": 84,
            "year": 2024,
            "category": "都市 言情 甜宠"
        }
        
        print("模拟爬虫数据:")
        print(f"  标题: {mock_data['title']}")
        print(f"  封面: {mock_data['cover']}")
        print(f"  分类: {mock_data['category']}")
        
        # 创建图片处理器
        image_processor = create_image_processor()
        
        # 处理封面
        processed_url = process_cover_image(
            mock_data["cover"], 
            image_processor, 
            mock_data["title"]
        )
        
        print(f"\n[OK] 封面处理完成")
        print(f"  处理前: {mock_data['cover']}")
        print(f"  处理后: {processed_url}")
        
        return True
        
    except Exception as e:
        print(f"[ERROR] 集成演示失败: {e}")
        return False

def show_features():
    """展示功能特性"""
    print("\n=== 功能特性说明 ===\n")
    
    features = [
        "[OK] 自动下载网络图片",
        "[OK] 基于时间戳的文件命名 (YYYY/MM/DD/timestamp.jpg)",
        "[OK] 图片格式转换和尺寸优化",
        "[OK] 上传到B2 Cloud Storage",
        "[OK] 返回CDN加速链接 (https://djimage.denlu.top/...)",
        "[OK] 完整的错误处理和重试机制",
        "[OK] 与爬虫无缝集成",
        "[OK] 支持批量处理"
    ]
    
    for feature in features:
        print(f"  {feature}")
    
    print("\n=== 配置信息 ===")
    print("  B2 存储桶: duanjusp")
    print("  CDN 域名: djimage.denlu.top")
    print("  图片格式: JPEG (优化后)")
    print("  最大尺寸: 1920x1080")
    print("  文件路径: YYYY/MM/DD/timestamp.jpg")

def main():
    """主演示函数"""
    print("=" * 50)
    print("图片处理系统演示")
    print("=" * 50)
    
    # 显示功能特性
    show_features()
    
    # 演示基本功能
    demo1_success = demo_image_processing()
    
    # 演示集成功能
    demo2_success = demo_crawler_integration()
    
    # 总结
    print("\n" + "=" * 50)
    print("演示总结")
    print("=" * 50)
    
    if demo1_success and demo2_success:
        print("[OK] 所有演示成功完成!")
        print("[OK] 图片处理系统已就绪，可以投入生产使用")
        
        print("\n使用方法:")
        print("1. 在爬虫中启用图片处理: crawl_and_import(10, True)")
        print("2. 禁用图片处理: crawl_and_import(10, False)")
        print("3. 单独处理图片: processor.process_image_url(url)")
        
    else:
        print("[WARN] 部分演示失败，请检查网络连接和B2配置")
    
    return demo1_success and demo2_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)