#!/usr/bin/env python3
"""
测试Linux环境下的模块导入
"""

import os
import sys
from pathlib import Path

def test_import():
    print("=== Linux环境模块导入测试 ===")
    print(f"Python版本: {sys.version}")
    print(f"操作系统: {os.name}")
    print(f"当前工作目录: {os.getcwd()}")
    
    # 设置路径
    current_dir = Path(__file__).parent
    project_root = current_dir.parent
    
    print(f"当前脚本目录: {current_dir}")
    print(f"项目根目录: {project_root}")
    
    # 检查目录结构
    path_123 = project_root / "123"
    path_quark = project_root / "quark"
    
    print(f"123路径: {path_123} (存在: {path_123.exists()})")
    print(f"quark路径: {path_quark} (存在: {path_quark.exists()})")
    
    if path_123.exists():
        files_123 = [f.name for f in path_123.iterdir() if f.is_file()]
        print(f"123目录文件: {files_123}")
    
    if path_quark.exists():
        files_quark = [f.name for f in path_quark.iterdir() if f.is_file()]
        print(f"quark目录文件: {files_quark}")
    
    # 添加到Python路径
    sys.path.insert(0, str(path_123))
    sys.path.insert(0, str(path_quark))
    
    print(f"已添加到sys.path: {str(path_123)}, {str(path_quark)}")
    
    # 测试123网盘导入
    print("\n--- 测试123网盘模块导入 ---")
    try:
        from upload_share import YunPan123Client
        print("✅ 123网盘模块导入成功")
        print(f"YunPan123Client类: {YunPan123Client}")
    except ImportError as e:
        print(f"❌ 123网盘模块导入失败: {e}")
    
    # 测试夸克网盘导入
    print("\n--- 测试夸克网盘模块导入 ---")
    try:
        from quark import QuarkPan
        print("✅ 夸克网盘模块导入成功")
        print(f"QuarkPan类: {QuarkPan}")
    except ImportError as e:
        print(f"❌ 夸克网盘模块导入失败: {e}")
        
        # 尝试直接导入quark.py文件
        print("尝试直接导入quark.py文件...")
        try:
            import importlib.util
            quark_file = path_quark / "quark.py"
            if quark_file.exists():
                print(f"找到quark.py文件: {quark_file}")
                spec = importlib.util.spec_from_file_location("quark", quark_file)
                quark_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(quark_module)
                QuarkPan = quark_module.QuarkPan
                print("✅ 夸克网盘模块通过文件路径导入成功")
                print(f"QuarkPan类: {QuarkPan}")
            else:
                print(f"❌ quark.py文件不存在: {quark_file}")
        except Exception as e2:
            print(f"❌ 文件路径导入也失败: {e2}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_import()
