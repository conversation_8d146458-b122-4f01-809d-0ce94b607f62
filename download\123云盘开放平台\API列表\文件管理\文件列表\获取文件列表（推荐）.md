# 获取文件列表（推荐）

API： GET 域名 + /api/v2/file/list

<font style="color:#DF2A3F;">注意：此接口查询结果包含回收站的文件，需自行根据字段</font>`<font style="color:#DF2A3F;">trashed</font>`<font style="color:#DF2A3F;">判断处理</font>

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#000000;">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## QueryString 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | --- |
| parentFileId | number | 必填 | 文件夹ID，根目录传 0 |
| limit | number | 必填 | 每页文件数量，最大不超过100 |
| searchData | string | 选填 | 搜索关键字将无视文件夹ID参数。将会进行全局查找 |
| searchMode | number | 选填 | 0:全文模糊搜索(注:将会根据搜索项分词,查找出相似的匹配项)<br/>1:精准搜索(注:精准搜索需要提供完整的文件名) |
| <br/> lastFileId | <br/>number | <br/>选填 | <br/>翻页查询时需要填写 |


## 返回数据
| **名称** | | **类型** | **是否必填** | **说明** |
| --- | --- | :---: | :---: | --- |
|  lastFileId<br/> | | number | 必填 | -1代表最后一页（无需再翻页查询）<br/>其他代表下一页开始的文件id，携带到请求参数中 |
| fileList | | array | 必填 | 文件列表 |
|  | fileId | number | 必填 | 文件Id |
|  | filename | string | 必填 | 文件名 |
|  | type | number | 必填 | 0-文件  1-文件夹 |
|  | size | number | 必填 | 文件大小 |
|  | etag | string | 必填 | md5 |
|  | status | number | 必填 | 文件审核状态。 大于 100 为审核驳回文件 |
|  | parentFileId | number | 必填 | 目录ID |
|  | category | number | 必填 | 文件分类：0-未知 1-音频 2-视频 3-图片 |
|  | trashed | int | 必填 | 文件是否在回收站标识：0 否 1是 |


## 示例
**请求示例**

```shell
curl --location 'https://open-api.123pan.com/api/v2/file/list?parentFileId=0&limit=100' \
--header 'Content-Type: application/json' \
--header 'Platform: open_platform' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)' \
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
.build();
MediaType mediaType = MediaType.parse("application/json");
RequestBody body = RequestBody.create(mediaType, "");
Request request = new Request.Builder()
.url("https://open-api.123pan.com/api/v2/file/list?parentFileId=0&limit=100")
.method("GET", body)
.addHeader("Content-Type", "application/json")
.addHeader("Platform", "open_platform")
.addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)")
.build();
Response response = client.newCall(request).execute();
```

```javascript
var settings = {
  "url": "https://open-api.123pan.com/api/v2/file/list?parentFileId=0&limit=100",
  "method": "GET",
  "timeout": 0,
  "headers": {
    "Content-Type": "application/json",
    "Platform": "open_platform",
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)"
  },
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
```

```javascript
const axios = require('axios');

let config = {
  method: 'get',
  maxBodyLength: Infinity,
  url: 'https://open-api.123pan.com/api/v2/file/list?parentFileId=0&limit=100',
  headers: { 
    'Content-Type': 'application/json', 
    'Platform': 'open_platform', 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
  }
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});

```

```python
import http.client
import json

conn = http.client.HTTPSConnection("open-api.123pan.com")
payload = ''
headers = {
    'Content-Type': 'application/json',
    'Platform': 'open_platform',
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
}
conn.request("GET", "/api/v2/file/list?parentFileId=0&limit=100", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

**响应示例**

```json
{
  "code": 0,
  "message": "ok",
  "data": {
    "lastFileId": -1,
    "fileList": [
      {
        "fileId": 5373646,
        "filename": "download.mp4",
        "parentFileId": 14663228,
        "type": 0,
        "etag": "af..(过长省略)",
        "size": 518564433,
        "category": 2,
        "status": 2,
        "punishFlag": 0,
        "s3KeyFlag": "x-0",
        "storageNode": "m16",
        "trashed": 0,
        "createAt": "2024-04-30 11:58:36",
        "updateAt": "2025-02-24 17:56:45"
      },
      {
        "fileId": 8903127,
        "filename": "2.json.gz",
        "parentFileId": 14663228,
        "type": 0,
        "etag": "46..(过长省略)",
        "size": 221476024,
        "category": 10,
        "status": 0,
        "punishFlag": 0,
        "s3KeyFlag": "x-0",
        "storageNode": "m50",
        "trashed": 0,
        "createAt": "2024-08-16 13:18:09",
        "updateAt": "2025-02-24 17:56:29"
      },
      {
        "fileId": 10171597,
        "filename": "6-m.mp4",
        "parentFileId": 14663228,
        "type": 0,
        "etag": "7a..(过长省略)",
        "size": 367628427,
        "category": 2,
        "status": 2,
        "punishFlag": 0,
        "s3KeyFlag": "x-0",
        "storageNode": "m51",
        "trashed": 0,
        "createAt": "2024-09-27 09:39:46",
        "updateAt": "2025-02-24 17:56:24"
      },
      {
        "fileId": 14710240,
        "filename": "测试二级目录",
        "parentFileId": 14663228,
        "type": 1,
        "etag": "",
        "size": 0,
        "category": 0,
        "status": 0,
        "punishFlag": 0,
        "s3KeyFlag": "x-0",
        "storageNode": "m0",
        "trashed": 0,
        "createAt": "2025-02-24 17:57:01",
        "updateAt": "2025-02-24 17:57:01"
      }
    ]
  },
  "x-traceID": "4d1623f9-5fd1-4169-b3d2-55c81b7a71d8_kong-db-5898fdd8c6-wnv6h"
}
```



> 更新: 2025-07-09 13:47:40  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/zrip9b0ye81zimv4>