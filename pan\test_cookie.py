#!/usr/bin/env python3
"""
测试夸克网盘Cookie是否有效
"""

import requests
import json

def test_cookie():
    """测试Cookie有效性"""
    
    # 读取Cookie
    try:
        with open("quark.cookie.txt", 'r', encoding='utf-8') as f:
            cookie = f.read().strip()
    except FileNotFoundError:
        print("❌ Cookie文件不存在")
        return False
    
    # 设置请求头
    headers = {
        "Cookie": cookie,
        "Referer": "https://pan.quark.cn",
        "Origin": "https://pan.quark.cn",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
        "Content-Type": "application/json;charset=UTF-8",
        "Sec-Ch-Ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        "Sec-Ch-Ua-Mobile": "?0",
        "Sec-Ch-Ua-Platform": '"Windows"',
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-origin"
    }
    
    params = {"pr": "ucpro", "fr": "pc"}
    
    # 测试API调用 - 获取文件列表
    print("🔍 测试Cookie有效性...")

    # 使用搜索API来测试Cookie
    api_url = "https://drive-pc.quark.cn/1/clouddrive/file/search"
    data = {
        "q": "",
        "_page": 1,
        "_size": 50,
        "_fetch_total": 1
    }

    print(f"🔍 尝试API: {api_url}")
    try:
        session = requests.Session()
        session.headers.update(headers)

        # 将data合并到params中进行GET请求
        get_params = {**params, **data}
        response = session.get(api_url, params=get_params, timeout=30)
        print(f"📊 响应状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print(f"📋 响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")

            if result.get("code") == 0:
                print("✅ Cookie有效！")
                file_list = result.get("data", {}).get("list", [])
                print(f"📁 网盘根目录文件数量: {len(file_list)}")
                return True
            else:
                print(f"❌ API返回错误: {result.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False


def test_pre_upload():
    """测试预上传API"""
    
    # 读取Cookie
    try:
        with open("quark.cookie.txt", 'r', encoding='utf-8') as f:
            cookie = f.read().strip()
    except FileNotFoundError:
        print("❌ Cookie文件不存在")
        return False
    
    # 设置请求头
    headers = {
        "Cookie": cookie,
        "Referer": "https://pan.quark.cn",
        "Origin": "https://pan.quark.cn",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Content-Type": "application/json;charset=UTF-8"
    }
    
    params = {"pr": "ucpro", "fr": "pc"}
    
    try:
        print("\n🔍 测试预上传API...")
        
        api_url = "https://drive-pc.quark.cn/1/clouddrive/file/upload/pre"
        data = {
            "pdir_fid": "0",
            "file_name": "test.txt",
            "size": 100,
            "format_type": "text/plain",
            "ccp_hash_update": True
        }
        
        session = requests.Session()
        session.headers.update(headers)
        
        response = session.post(api_url, params=params, json=data, timeout=30)
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📋 响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get("code") == 0:
                print("✅ 预上传API调用成功！")
                return True
            else:
                print(f"❌ 预上传API返回错误: {result.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ 预上传HTTP请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 预上传测试失败: {e}")
        return False

if __name__ == "__main__":
    print("夸克网盘Cookie测试工具")
    print("=" * 50)
    
    # 测试Cookie
    cookie_valid = test_cookie()
    
    if cookie_valid:
        # 测试预上传
        test_pre_upload()
    else:
        print("\n💡 解决方案:")
        print("1. 重新登录夸克网盘 (https://pan.quark.cn)")
        print("2. 按F12打开开发者工具")
        print("3. 刷新页面，在Network标签中找到任意请求")
        print("4. 复制完整的Cookie值到 quark.cookie.txt 文件中")
