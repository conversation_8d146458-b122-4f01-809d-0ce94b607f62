#!/usr/bin/env python3
"""
qBittorrent种子与短剧数据库匹配系统
读取qBittorrent种子信息，与Flask数据库中的短剧进行匹配，并添加网盘分享链接
"""

import os
import sys
import re
import json
import requests
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# 添加Flask项目路径
flask_path = Path(__file__).parent.parent / "flask"
sys.path.insert(0, str(flask_path))

from models import db, Play, PlayLink
from app import create_app

class QBDramaMatcher:
    """qBittorrent种子与短剧匹配器"""
    
    def __init__(self, qb_host="***********", qb_port=49850, qb_user="admin", qb_pass="507877550@lihao"):
        self.qb_host = qb_host
        self.qb_port = qb_port
        self.qb_user = qb_user
        self.qb_pass = qb_pass
        self.qb_base_url = f"http://{qb_host}:{qb_port}"
        self.session = requests.Session()
        self.app = None
        
        # 初始化Flask应用
        self._init_flask_app()
        
        # 登录qBittorrent
        self._login_qbittorrent()
    
    def _init_flask_app(self):
        """初始化Flask应用"""
        try:
            self.app = create_app()
            print("Flask应用初始化成功")
        except Exception as e:
            print(f"Flask应用初始化失败: {e}")
            sys.exit(1)
    
    def _login_qbittorrent(self):
        """登录qBittorrent"""
        try:
            login_url = f"{self.qb_base_url}/api/v2/auth/login"
            data = {
                'username': self.qb_user,
                'password': self.qb_pass
            }
            
            response = self.session.post(login_url, data=data)
            if response.text == "Ok.":
                print("qBittorrent登录成功")
            else:
                print(f"qBittorrent登录失败: {response.text}")
                sys.exit(1)
                
        except Exception as e:
            print(f"连接qBittorrent失败: {e}")
            sys.exit(1)
    
    def get_torrents(self, category: str = "agsvpt") -> List[Dict]:
        """获取指定分类的种子信息"""
        try:
            url = f"{self.qb_base_url}/api/v2/torrents/info"
            params = {}
            if category:
                params['category'] = category
            
            response = self.session.get(url, params=params)
            torrents = response.json()
            
            print(f"获取到 {len(torrents)} 个种子 (分类: {category or '全部'})")
            return torrents
            
        except Exception as e:
            print(f"获取种子信息失败: {e}")
            return []
    
    def extract_drama_name(self, torrent_name: str) -> str:
        """从种子名称中提取短剧名称"""
        # 去除常见的视频格式标识
        name = torrent_name
        
        # 去除年份 (2024, 2025等)
        name = re.sub(r'\.20\d{2}\.', '.', name)
        
        # 去除季数标识 (S01, S1等)
        name = re.sub(r'\.S\d+\.', '.', name)
        
        # 去除分辨率和编码信息
        name = re.sub(r'\.(720p|1080p|2160p|4K)\.', '.', name, flags=re.IGNORECASE)
        name = re.sub(r'\.(WEB-DL|BluRay|HDRip|BDRip)\.', '.', name, flags=re.IGNORECASE)
        name = re.sub(r'\.(H264|H265|x264|x265)\.', '.', name, flags=re.IGNORECASE)
        name = re.sub(r'\.(AAC|AC3|DTS)\.', '.', name, flags=re.IGNORECASE)
        
        # 去除制作组标识 (通常在最后)
        name = re.sub(r'-[A-Za-z0-9]+$', '', name)
        
        # 将点号替换为空格并清理
        name = name.replace('.', ' ')
        name = re.sub(r'\s+', ' ', name).strip()
        
        # 提取中文名称部分 (通常在开头)
        chinese_match = re.match(r'^([^\x00-\x7F]+)', name)
        if chinese_match:
            return chinese_match.group(1).strip()
        
        # 如果没有中文，返回清理后的名称
        return name
    
    def find_matching_drama(self, drama_name: str) -> Optional[Play]:
        """在数据库中查找匹配的短剧"""
        with self.app.app_context():
            # 精确匹配
            play = Play.query.filter_by(title=drama_name).first()
            if play:
                return play
            
            # 模糊匹配 - 去除空格和标点
            clean_name = re.sub(r'[^\w\u4e00-\u9fff]', '', drama_name)
            if len(clean_name) >= 2:
                plays = Play.query.all()
                for play in plays:
                    clean_title = re.sub(r'[^\w\u4e00-\u9fff]', '', play.title)
                    if clean_name in clean_title or clean_title in clean_name:
                        return play
            
            return None
    
    def get_netdisk_share_links(self, drama_name: str, file_path: str = None) -> Dict[str, str]:
        """获取网盘分享链接"""
        share_links = {}
        
        try:
            # 1. 获取123网盘分享链接
            print(f"正在为 '{drama_name}' 创建123网盘分享链接...")
            share_123 = self._create_123pan_share_link(drama_name, file_path)
            if share_123:
                share_links["123pan"] = share_123
            
            # 2. 获取夸克网盘分享链接 (如果需要)
            # 这里可以添加夸克网盘的API调用
            # share_quark = self._create_quark_share_link(drama_name, file_path)
            # if share_quark:
            #     share_links["quark"] = share_quark
            
        except Exception as e:
            print(f"创建分享链接失败: {e}")
        
        return share_links
    
    def _create_123pan_share_link(self, drama_name: str, file_path: str = None) -> Optional[str]:
        """创建123网盘分享链接"""
        try:
            # 只有当文件路径存在且文件真实存在时才处理
            if not file_path or not Path(file_path).exists():
                print(f"文件不存在，跳过为 '{drama_name}' 创建分享链接: {file_path}")
                return None
            
            # 导入123网盘API
            sys.path.append(str(Path(__file__).parent.parent / "123"))
            from upload_share import YunPan123Client
            
            # 初始化123网盘客户端
            config_path = Path(__file__).parent.parent / "123" / "config.json"
            client = YunPan123Client(str(config_path))
            
            # 上传文件并分享
            result = client.upload_and_share(
                file_path=file_path,
                directory_path="/短剧下载",
                share_name=f"短剧分享: {drama_name}",
                expire_days=30
            )
            return result["shareUrl"]
                
        except Exception as e:
            print(f"创建123网盘分享链接失败: {e}")
            return None
    
    def add_play_links_to_drama(self, play: Play, share_links: Dict[str, str]):
        """为短剧添加播放链接"""
        with self.app.app_context():
            try:
                # 检查是否已存在链接
                existing_links = PlayLink.query.filter_by(play_id=play.id).all()
                existing_platforms = {link.platform for link in existing_links}
                
                links_added = 0
                
                for platform, url in share_links.items():
                    if platform not in existing_platforms:
                        play_link = PlayLink(
                            play_id=play.id,
                            platform=platform,
                            link=url,
                            created_at=datetime.utcnow(),
                            updated_at=datetime.utcnow()
                        )
                        db.session.add(play_link)
                        links_added += 1
                        print(f"  添加 {platform} 链接: {url}")
                    else:
                        print(f"  {platform} 链接已存在，跳过")
                
                if links_added > 0:
                    db.session.commit()
                    print(f"成功为 '{play.title}' 添加 {links_added} 个播放链接")
                else:
                    print(f"'{play.title}' 的所有链接都已存在")
                    
            except Exception as e:
                db.session.rollback()
                print(f"添加播放链接失败: {e}")
    
    def process_torrents(self, category: str = "agsvpt"):
        """处理种子并匹配短剧"""
        print(f"开始处理qBittorrent种子 (分类: {category})")
        print("=" * 60)
        
        torrents = self.get_torrents(category)
        
        if not torrents:
            print("没有找到种子")
            return
        
        matched_count = 0
        
        for torrent in torrents:
            torrent_name = torrent.get('name', '')
            torrent_path = torrent.get('content_path', '')
            torrent_state = torrent.get('state', '')
            
            print(f"\n处理种子: {torrent_name}")
            print(f"状态: {torrent_state}")
            print(f"路径: {torrent_path}")
            
            # 只处理已完成的种子
            if torrent_state not in ['uploading', 'stalledUP', 'pausedUP', 'queuedUP', 'checkingUP', 'forcedUP']:
                print(f"跳过未完成的种子: {torrent_state}")
                continue
            
            # 提取短剧名称
            drama_name = self.extract_drama_name(torrent_name)
            print(f"提取的短剧名: {drama_name}")
            
            # 查找匹配的短剧
            play = self.find_matching_drama(drama_name)
            
            if play:
                print(f"找到匹配短剧: {play.title} (ID: {play.id})")
                matched_count += 1
                
                # 获取网盘分享链接
                share_links = self.get_netdisk_share_links(drama_name, torrent_path)
                
                # 添加播放链接
                if share_links:
                    self.add_play_links_to_drama(play, share_links)
                else:
                    print("未能创建分享链接")
                
            else:
                print(f"未找到匹配的短剧: {drama_name}")
        
        print("\n" + "=" * 60)
        print(f"处理完成! 共处理 {len(torrents)} 个种子，匹配 {matched_count} 个短剧")
    
    def list_categories(self):
        """列出qBittorrent所有分类"""
        try:
            url = f"{self.qb_base_url}/api/v2/torrents/categories"
            response = self.session.get(url)
            categories = response.json()
            
            print("qBittorrent分类列表:")
            for cat_name, cat_info in categories.items():
                print(f"  {cat_name}: {cat_info.get('savePath', 'N/A')}")
            
            return categories
            
        except Exception as e:
            print(f"获取分类列表失败: {e}")
            return {}

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="qBittorrent种子与短剧数据库匹配系统")
    parser.add_argument("--category", "-c", default="agsvpt", help="qBittorrent分类名 (默认: agsvpt)")
    parser.add_argument("--list-categories", action="store_true", help="列出所有分类")
    parser.add_argument("--dry-run", action="store_true", help="模拟运行，不实际添加链接")
    
    args = parser.parse_args()
    
    try:
        matcher = QBDramaMatcher()
        
        if args.list_categories:
            matcher.list_categories()
        else:
            if args.dry_run:
                print("*** 模拟运行模式 - 不会实际添加链接 ***")
            
            matcher.process_torrents(args.category)
            
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"程序错误: {e}")

if __name__ == "__main__":
    main()