# 短剧导入系统

这是一个完整的短剧资源监控、上传和导入系统，集成了以下功能：

## 功能特性

### 1. qBittorrent监控
- 监控qBittorrent下载完成状态
- 自动移动完成的文件到指定目录
- 支持自动上传到123网盘
- 可配置是否删除本地文件

### 2. AGSVPT网站数据爬取
- 爬取AGSVPT网站的短剧种子信息
- 支持RSS格式数据解析
- 提取种子标题、描述、大小、做种数等信息
- 自动解析集数、标签等元数据

### 3. 123网盘上传和分享
- 集成现有的123网盘API
- 自动上传文件/文件夹到网盘
- 自动创建分享链接
- 支持文件夹结构保持

### 4. Flask数据库导入
- 将爬取的数据导入Flask短剧平台
- 自动去重和数据清洗
- 支持标签和分类管理
- 生成播放链接（磁力链接、种子链接）

### 5. 统一控制系统
- 集成所有功能模块
- 支持定时任务调度
- 提供多种运行模式
- 完整的日志记录

## 文件结构

```
import/
├── config.yml              # 主配置文件
├── main.py                  # 主控制脚本
├── qb_monitor.py           # qBittorrent监控模块
├── agsvpt_crawler.py       # AGSVPT爬虫模块
├── pan123_uploader.py      # 123网盘上传模块
├── flask_importer.py       # Flask数据库导入模块
├── requirements.txt        # Python依赖包
└── README.md              # 说明文档
```

## 安装和配置

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置文件设置

编辑 `config.yml` 文件，配置以下信息：

- **qBittorrent配置**: Web UI地址、用户名密码
- **123网盘配置**: API密钥和上传设置
- **爬虫配置**: AGSVPT站点信息和爬取参数
- **数据库配置**: Flask数据库路径和映射规则

### 3. 准备工作

确保以下服务正常运行：
- qBittorrent客户端（开启Web UI）
- Flask短剧平台（数据库已创建）
- 123网盘API认证已完成

## 使用方法

### 1. 完整模式（推荐）

启动所有服务，包括qBittorrent监控和定时爬虫：

```bash
python main.py --mode full
```

### 2. 仅运行爬虫

只执行一次爬虫任务：

```bash
python main.py --mode crawler
```

### 3. 仅运行qBittorrent监控

只监控qBittorrent下载完成：

```bash
python main.py --mode qb
```

### 4. 测试模式

测试各个模块是否正常工作：

```bash
python main.py --mode test
```

### 5. 指定配置文件

```bash
python main.py --config /path/to/your/config.yml
```

## 工作流程

### 自动化流程

1. **定时爬取**: 系统按配置的间隔自动爬取AGSVPT网站
2. **数据导入**: 爬取的数据自动导入Flask数据库
3. **去重处理**: 自动检测和处理重复内容
4. **种子监控**: 监控qBittorrent中的下载完成状态
5. **自动上传**: 下载完成后自动上传到123网盘
6. **分享链接**: 自动创建网盘分享链接
7. **数据同步**: 将网盘链接更新到数据库

### 手动操作

- 可以随时手动执行爬虫任务
- 支持导入历史爬取的JSON数据
- 可以手动触发单个文件的上传

## 配置说明

### qBittorrent配置

```yaml
qbittorrent:
  host: "127.0.0.1"           # qBittorrent Web UI地址
  port: 8080                  # Web UI端口
  username: "admin"           # 用户名
  password: "adminadmin"      # 密码
  monitor_interval: 30        # 监控间隔（秒）
  auto_upload_to_pan: true    # 是否自动上传到网盘
  delete_after_upload: false  # 上传后是否删除本地文件
```

### 爬虫配置

```yaml
crawler:
  sites:
    agsvpt:
      rss_url: "https://www.agsvpt.com/torrentrss.php"
      params:
        passkey: "your_passkey"  # 你的passkey
        rows: 50                 # 每次获取数量
        cat419: 1               # 短剧分类
  crawl_interval: 300          # 爬取间隔（秒）
  max_pages: 10               # 最大爬取页数
```

### 123网盘配置

```yaml
pan123:
  upload_folder: "短剧资源"    # 上传文件夹名称
  auto_share: true            # 是否自动创建分享链接
  share_expire_days: 0        # 分享链接有效期（0=永久）
```

## 日志和监控

- 系统会生成详细的日志文件 `import.log`
- 支持配置日志级别和文件大小限制
- 可配置钉钉/企业微信通知webhook

## 注意事项

1. **网络环境**: 确保能正常访问AGSVPT网站
2. **API限制**: 注意123网盘API的调用频率限制
3. **存储空间**: 确保有足够的本地和网盘存储空间
4. **权限设置**: 确保程序有文件读写权限
5. **数据备份**: 建议定期备份重要配置和数据

## 故障排除

### 常见问题

1. **qBittorrent连接失败**: 检查Web UI是否开启，用户名密码是否正确
2. **爬虫无法获取数据**: 检查passkey是否有效，网络是否正常
3. **123网盘上传失败**: 检查API认证和网络连接
4. **数据库导入失败**: 确保Flask应用正常，数据库文件存在

### 日志分析

查看 `import.log` 文件获取详细错误信息：

```bash
tail -f import.log
```

## 扩展功能

系统设计为模块化，可以轻松扩展：

- 添加其他种子站点的爬虫
- 支持其他网盘服务
- 集成更多通知方式
- 添加Web管理界面

## 技术栈

- **Python 3.8+**: 主要开发语言
- **Flask**: Web框架和数据库
- **SQLAlchemy**: 数据库ORM
- **BeautifulSoup**: HTML解析
- **Requests**: HTTP客户端
- **qBittorrent API**: 种子客户端接口
- **123网盘API**: 网盘服务接口

## 版本历史

- **v1.0**: 初始版本，包含基础功能
- 支持qBittorrent监控
- 支持AGSVPT爬虫
- 支持123网盘上传
- 支持Flask数据库导入