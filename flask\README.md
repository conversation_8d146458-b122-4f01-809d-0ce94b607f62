# 短剧星球 - Flask 全栈应用

一个现代化的短剧分享平台，使用 Flask + SQLAlchemy + Alpine.js + Tailwind CSS 构建。

## 🌟 功能特性

- 📱 响应式设计，支持移动端和桌面端
- 🔍 实时搜索建议和全文搜索
- 📊 智能排序（最新、热门、评分）
- 🏷️ 分类管理和标签系统
- 📄 分页和无限滚动加载
- 🎨 现代化UI设计与动画效果
- 🔗 网盘链接管理（123盘、夸克网盘）
- 📱 移动端友好的导航体验

## 🛠️ 技术栈

**后端：**
- Flask 3.0 - Web框架
- SQLAlchemy 2.0 - ORM数据库
- Flask-Migrate - 数据库迁移
- SQLite - 数据库（开发环境）

**前端：**
- Alpine.js 3.x - 轻量级前端框架
- Tailwind CSS - CSS框架
- Jinja2 - 模板引擎

## 🚀 快速开始

### 1. 环境准备

确保已安装 Python 3.8+：

```bash
python --version
```

### 2. 安装依赖

```bash
# 进入项目目录
cd flask

# 安装Python依赖
pip install -r requirements.txt
```

### 3. 初始化数据库

```bash
# 初始化数据库和示例数据
python run.py init
```

### 4. 运行应用

```bash
# 启动开发服务器
python run.py

# 或者显式指定运行命令
python run.py run
```

访问 http://localhost:5000 查看应用。

## 📋 管理命令

```bash
# 初始化数据库和示例数据
python run.py init

# 只添加示例数据
python run.py seed

# 重置数据库（危险操作）
python run.py reset

# 运行开发服务器
python run.py run

  创建的脚本文件:

  1. reset_database_clean.py - 主重置脚本
    - 支持开发环境(SQLite)和生产环境(MariaDB)
    - 完全清空数据库，不添加示例数据
    - 包含安全确认机制
  2. reset_db_menu.py - 交互式菜单脚本
    - 图形化菜单选择
    - 分别处理开发和生产环境

  使用方法:

  # 方法1: 直接运行重置脚本
  python reset_database_clean.py development  # 重置开发数据库
  python reset_database_clean.py production   # 重置生产数据库

  # 方法2: 使用交互式菜单
  python reset_db_menu.py

  # 方法3: 使用默认参数(开发环境)
  python reset_database_clean.py

  功能特点:
  - ✅ 删除所有现有表
  - ✅ 重新创建表结构
  - ✅ 验证表创建成功
  - ✅ 确认数据库为空
  - ✅ 生产环境需要二次确认
  - ✅ 完整的错误处理
  - ✅ 兼容Windows控制台(移除Unicode字符)

  数据库现已重置为空白状态，可以开始导入新数据。
```

## 🗂️ 项目结构

```
flask/
├── app.py              # 应用工厂
├── config.py           # 配置文件
├── models.py           # 数据库模型
├── run.py              # 启动脚本
├── requirements.txt    # Python依赖
├── routes/
│   ├── __init__.py
│   ├── main.py         # 页面路由
│   └── api.py          # API路由
└── templates/
    ├── base.html       # 基础模板
    ├── index.html      # 首页
    ├── play_detail.html # 短剧详情
    ├── category.html   # 分类页面
    └── search.html     # 搜索结果
```

## 🎯 API 接口

### 短剧相关
- `GET /api/plays` - 获取短剧列表
- `GET /api/plays/<id>` - 获取短剧详情
- `GET /api/plays/popular` - 获取热门短剧
- `GET /api/plays/latest` - 获取最新短剧

### 搜索相关
- `GET /api/search` - 搜索短剧
- `GET /api/search/suggestions` - 搜索建议

### 分类相关
- `GET /api/categories` - 获取分类列表

### 统计相关
- `GET /api/statistics` - 获取统计信息
- `GET /api/health` - 健康检查

## 📝 数据模型

### Category（分类）
- 分类管理，支持排序
- 每个分类有slug用于URL友好

### Play（短剧）
- 短剧基本信息（标题、描述、海报等）
- 评分、观看次数、集数等统计信息
- 支持多种状态（active, inactive, draft）

### PlayLink（播放链接）
- 支持多种网盘类型（123盘、夸克网盘）
- 链接状态管理和更新时间追踪

### Tag（标签）
- 短剧标签系统，支持多对多关联

## ⚙️ 配置说明

主要配置项在 `config.py` 中：

- `SECRET_KEY` - Flask密钥
- `SQLALCHEMY_DATABASE_URI` - 数据库连接
- `PLAYS_PER_PAGE` - 每页显示数量
- `UPLOAD_FOLDER` - 上传文件夹

## 🎨 样式定制

应用使用 Tailwind CSS 构建，支持：
- 深色/浅色主题
- 响应式布局
- 玻璃态效果
- 渐变背景
- 流畅动画

## 🔧 开发说明

### 添加新的短剧分类
1. 在数据库中添加新的 Category 记录
2. 更新 seed_data() 函数

### 添加新的API端点
1. 在 `routes/api.py` 中添加新路由
2. 使用 `create_response()` 统一响应格式

### 修改前端交互
1. 更新对应的 HTML 模板
2. 在 Alpine.js 组件中添加交互逻辑

## 📱 移动端支持

- 响应式导航菜单
- 触摸友好的界面元素
- 移动端优化的搜索体验
- 适配不同屏幕尺寸

## 🚀 部署建议

### 生产环境
- 使用 PostgreSQL 替代 SQLite
- 配置 Nginx 作为反向代理
- 使用 Gunicorn 作为 WSGI 服务器
- 设置环境变量管理配置

### 示例部署配置
```bash
# 安装 Gunicorn
pip install gunicorn

# 运行生产服务器
gunicorn -w 4 -b 0.0.0.0:8000 "app:create_app()"
```

## 🤝 开发贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License - 详见 LICENSE 文件