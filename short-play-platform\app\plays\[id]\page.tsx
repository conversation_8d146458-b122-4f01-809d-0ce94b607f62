import { getPlayById, getPlays } from "@/lib/data"
import { notFound } from "next/navigation"
import Image from "next/image"
import type { Metadata } from "next"
import { CopyButton } from "@/components/copy-button"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Star, Eye, Calendar, ArrowLeft, Share2, Download } from "lucide-react"
import Link from "next/link"

type Props = {
  params: { id: string }
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const id = Number.parseInt(params.id, 10)
  const play = await getPlayById(id)

  if (!play) {
    return {
      title: "未找到短剧",
    }
  }

  return {
    title: `${play.title} - 短剧分享`,
    description: play.description,
  }
}

export async function generateStaticParams() {
  const plays = await getPlays()
  return plays.map((play) => ({
    id: play.id.toString(),
  }))
}

const ShareLink = ({
  platformName,
  link,
  code,
}: {
  platformName: string
  link: string
  code?: string
}) => {
  if (!link) return null
  const textToCopy = `链接: ${link}${code ? ` 提取码: ${code}` : ""}`

  return (
    <Card className="transition-all hover:shadow-md">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              <Badge variant="secondary" className="shrink-0">{platformName}</Badge>
              {code && (
                <Badge variant="outline" className="text-primary border-primary/20 bg-primary/5 shrink-0">
                  提取码: {code}
                </Badge>
              )}
            </div>
            <p className="text-sm text-muted-foreground truncate">{link}</p>
          </div>
          <div className="flex items-center gap-2 ml-4">
            <CopyButton textToCopy={textToCopy} platformName={platformName} />
            <Button size="sm" variant="outline" className="shrink-0">
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default async function PlayDetailPage({ params }: Props) {
  const id = Number.parseInt(params.id, 10)
  if (isNaN(id)) {
    notFound()
  }

  const play = await getPlayById(id)

  if (!play) {
    notFound()
  }

  const hasRating = play.rating && play.rating > 0
  const hasViews = play.views && play.views > 0
  const availablePlatforms = [
    play.pan123.link && '123网盘',
    play.quark.link && '夸克网盘'
  ].filter(Boolean)

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-700">
      <div className="container mx-auto max-w-6xl px-4 py-8">
        {/* Back Button */}
        <div className="mb-6">
          <Link href="/">
            <Button variant="ghost" className="mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回首页
            </Button>
          </Link>
        </div>

        <div className="grid lg:grid-cols-5 gap-8">
          {/* Poster Section */}
          <div className="lg:col-span-2">
            <Card className="overflow-hidden">
              <CardContent className="p-0">
                <div className="aspect-[2/3] relative">
                  <Image 
                    src={play.posterUrl || "/placeholder.svg"} 
                    alt={play.title} 
                    fill 
                    className="object-cover" 
                    priority
                  />
                </div>
              </CardContent>
            </Card>
            
            {/* Action Buttons */}
            <div className="mt-4 flex gap-2">
              <Button className="flex-1" size="lg">
                <Share2 className="h-4 w-4 mr-2" />
                分享短剧
              </Button>
            </div>
          </div>

          {/* Content Section */}
          <div className="lg:col-span-3">
            <div className="space-y-6">
              {/* Title and Basic Info */}
              <div>
                <div className="flex flex-wrap items-center gap-2 mb-3">
                  <Badge variant="secondary" className="px-3 py-1">
                    {play.category.name}
                  </Badge>
                  {hasRating && (
                    <Badge variant="outline" className="bg-yellow-50 border-yellow-200 text-yellow-700 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-300 px-3 py-1">
                      <Star className="h-3 w-3 mr-1 fill-current" />
                      {play.rating?.toFixed(1)} 分
                    </Badge>
                  )}
                  {hasViews && (
                    <Badge variant="outline" className="px-3 py-1">
                      <Eye className="h-3 w-3 mr-1" />
                      {play.views > 1000 ? `${(play.views / 1000).toFixed(1)}k` : play.views} 观看
                    </Badge>
                  )}
                  {play.updatedAt && (
                    <Badge variant="outline" className="px-3 py-1">
                      <Calendar className="h-3 w-3 mr-1" />
                      {new Date(play.updatedAt).toLocaleDateString('zh-CN')}
                    </Badge>
                  )}
                </div>
                
                <h1 className="text-3xl md:text-4xl font-bold tracking-tight mb-4 bg-gradient-to-r from-gray-900 to-gray-600 dark:from-gray-100 dark:to-gray-300 bg-clip-text text-transparent">
                  {play.title}
                </h1>
              </div>

              {/* Description */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">简介</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-base leading-relaxed text-muted-foreground">
                    {play.description}
                  </p>
                </CardContent>
              </Card>

              {/* Share Links */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Download className="h-5 w-5" />
                    观看链接
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">
                    支持 {availablePlatforms.join('、')} 在线观看
                  </p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ShareLink platformName="123网盘" link={play.pan123.link} code={play.pan123.code} />
                  <ShareLink platformName="夸克网盘" link={play.quark.link} code={play.quark.code} />
                  
                  <Separator />
                  
                  <div className="flex items-center justify-center text-sm text-muted-foreground">
                    <p>温馨提示：请点击复制按钮获取完整链接和提取码</p>
                  </div>
                </CardContent>
              </Card>

              {/* Additional Info */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">详细信息</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">分类：</span>
                      <span className="font-medium">{play.category.name}</span>
                    </div>
                    {hasRating && (
                      <div>
                        <span className="text-muted-foreground">评分：</span>
                        <span className="font-medium">{play.rating?.toFixed(1)} / 5.0</span>
                      </div>
                    )}
                    {hasViews && (
                      <div>
                        <span className="text-muted-foreground">观看量：</span>
                        <span className="font-medium">{play.views?.toLocaleString()} 次</span>
                      </div>
                    )}
                    {play.updatedAt && (
                      <div>
                        <span className="text-muted-foreground">更新时间：</span>
                        <span className="font-medium">{new Date(play.updatedAt).toLocaleDateString('zh-CN')}</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
