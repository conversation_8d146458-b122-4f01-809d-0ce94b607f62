// lib/data.ts
// In a real application, this would be your database query.

export type ShortPlay = {
  id: number
  title: string
  posterUrl: string
  description: string
  category: {
    name: string
    slug: string
  }
  pan123: {
    link: string
    code?: string
  }
  quark: {
    link: string
    code?: string
  }
  rating?: number
  views?: number
  updatedAt?: string
}

const shortPlaysData: ShortPlay[] = [
  {
    id: 1,
    title: "重生之我在霸总文里当保姆",
    posterUrl: "/placeholder.svg?width=400&height=600",
    description: "一场意外，她重生到一本霸总小说里，成了男主角家的保姆，且看她如何逆袭，俘获霸总的心。",
    category: { name: "重生", slug: "rebirth" },
    pan123: { link: "https://www.123pan.com/s/abc-123", code: "a1b2" },
    quark: { link: "https://pan.quark.cn/s/xyz-789", code: "x7y8" },
    rating: 4.5,
    views: 12580,
    updatedAt: "2024-07-28",
  },
  {
    id: 2,
    title: "我的女友是机器人",
    posterUrl: "/placeholder.svg?width=400&height=600",
    description: "一个普通的程序员，意外收到了一个来自未来的机器人女友，生活从此发生了翻天覆地的变化。",
    category: { name: "都市", slug: "urban" },
    pan123: { link: "https://www.123pan.com/s/def-456" },
    quark: { link: "https://pan.quark.cn/s/uvw-456", code: "u4v5" },
    rating: 4.2,
    views: 8920,
    updatedAt: "2024-07-30",
  },
  {
    id: 3,
    title: "穿越时空来爱你",
    posterUrl: "/placeholder.svg?width=400&height=600",
    description: "她来自21世纪，他来自古代王朝，一根红线将他们跨越时空紧紧相连。",
    category: { name: "古装", slug: "historical" },
    pan123: { link: "https://www.123pan.com/s/ghi-789", code: "g7h8" },
    quark: { link: "https://pan.quark.cn/s/rst-123" },
    rating: 4.7,
    views: 24350,
    updatedAt: "2024-07-29",
  },
  {
    id: 4,
    title: "都市奇缘",
    posterUrl: "/placeholder.svg?width=400&height=600",
    description: "在繁华的都市中，两个陌生人因为一次偶然的相遇，展开了一段奇妙的缘分。",
    category: { name: "都市", slug: "urban" },
    pan123: { link: "https://www.123pan.com/s/jkl-012", code: "j0k1" },
    quark: { link: "https://pan.quark.cn/s/opq-789", code: "o7p8" },
    rating: 3.9,
    views: 6750,
    updatedAt: "2024-07-31",
  },
  {
    id: 5,
    title: "千金归来",
    posterUrl: "/placeholder.svg?width=400&height=600",
    description: "被调换人生的千金，在历经磨难后，强势归来，夺回属于自己的一切。",
    category: { name: "重生", slug: "rebirth" },
    pan123: { link: "https://www.123pan.com/s/mno-345" },
    quark: { link: "https://pan.quark.cn/s/lmn-456" },
    rating: 4.3,
    views: 15670,
    updatedAt: "2024-07-27",
  },
  {
    id: 6,
    title: "王爷，王妃又跑了",
    posterUrl: "/placeholder.svg?width=400&height=600",
    description: "她是21世纪的特agent，穿越成王妃，每天想着的不是宫斗，而是如何逃出王府。",
    category: { name: "古装", slug: "historical" },
    pan123: { link: "https://www.123pan.com/s/pqr-678", code: "p6q7" },
    quark: { link: "https://pan.quark.cn/s/ijk-123", code: "i1j2" },
    rating: 4.1,
    views: 9820,
    updatedAt: "2024-07-26",
  },
  // ... add more data as needed
]

export async function getPlays() {
  // Simulate a network delay
  await new Promise((resolve) => setTimeout(resolve, 100))
  return shortPlaysData
}

export async function getPlayById(id: number) {
  await new Promise((resolve) => setTimeout(resolve, 100))
  return shortPlaysData.find((play) => play.id === id)
}

export async function getCategories() {
  await new Promise((resolve) => setTimeout(resolve, 100))
  const categories = shortPlaysData.reduce(
    (acc, play) => {
      if (!acc.some((cat) => cat.slug === play.category.slug)) {
        acc.push(play.category)
      }
      return acc
    },
    [] as { name: string; slug: string }[],
  )
  return categories
}

export async function getPlaysByCategory(slug: string) {
  await new Promise((resolve) => setTimeout(resolve, 100))
  return shortPlaysData.filter((play) => play.category.slug === slug)
}
