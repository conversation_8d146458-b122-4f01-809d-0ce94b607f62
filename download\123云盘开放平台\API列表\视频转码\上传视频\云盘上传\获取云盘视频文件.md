# 获取云盘视频文件

API： GET 域名 + /api/v2/file/list

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#000000;">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## QueryString 参数
| **名称** | **类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| parentFileId | number | 必填 | 文件夹ID，根目录传 0 |
| limit | number | 必填 | 每页文件数量，最大不超过100 |
| searchData | string | 选填 | 搜索关键字将无视文件夹ID参数。将会进行全局查找 |
| searchMode | number | 选填 | 0:全文模糊搜索(注:将会根据搜索项分词,查找出相似的匹配项)<br/>1:精准搜索(注:精准搜索需要提供完整的文件名) |
|  lastFileId | number | 选填 | 翻页查询时需要填写 |
| category | number | 必填 | 固定为2，2代表视频 |


## 返回数据
| **名称** | | **类型** | **是否必填** | **说明** |
| --- | --- | :---: | :---: | --- |
|  lastFileId<br/> | | number | 必填 | -1代表最后一页（无需再翻页查询）<br/>其他代表下一页开始的文件id，携带到请求参数中 |
| fileList | | array | 必填 | 文件列表 |
|  | fileId | number | 必填 | 文件Id |
|  | filename | string | 必填 | 文件名 |
|  | type | number | 必填 | 0-文件  1-文件夹 |
|  | size | number | 必填 | 文件大小 |
|  | etag | string | 必填 | md5 |
|  | status | number | 必填 | 文件审核状态。 大于 100 为审核驳回文件 |
|  | parentFileId | number | 必填 | 目录ID |
|  | category | number | 必填 | 文件分类：0-未知 1-音频 2-视频 3-图片 |




> 更新: 2025-03-17 19:16:35  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/yqyi3rqrmrpvdf0d>