# 夸克网盘配置文件

# 基本配置
quark:
  # Cookie文件路径
  cookie_file: "quark.cookie.txt"
  
  # 父目录ID (0为根目录)
  parent_dir_id: "0"
  
  # 分片大小 (字节，默认8MB)
  chunk_size: 8388608
  
  # 请求超时时间 (秒)
  timeout: 30
  
  # 分片上传超时时间 (秒)
  upload_timeout: 120

# 分享配置
share:
  # 默认分享过期类型
  # 0: 180天 (SVIP可1年)
  # 1: 永久
  # 2: 7天
  default_expire: 1
  
  # 分享链接类型
  # 1: 标准短链
  url_type: 1

# 代理配置 (可选)
proxy:
  # 是否启用代理
  enabled: false
  
  # HTTP代理地址
  http: ""
  
  # HTTPS代理地址
  https: ""

# 日志配置
logging:
  # 日志级别 (DEBUG, INFO, WARNING, ERROR)
  level: "INFO"
  
  # 日志格式
  format: "%(asctime)s - %(levelname)s - %(message)s"
  
  # 是否输出到文件
  to_file: false
  
  # 日志文件路径
  file_path: "quark.log"

# 重试配置
retry:
  # 最大重试次数
  max_attempts: 3
  
  # 重试间隔 (秒)
  delay: 1
  
  # 重试间隔倍数
  backoff: 2
