#!/usr/bin/env python3
"""
测试文件夹压缩功能
"""

import os
import tempfile
from qb_upload_manager import QBUpload<PERSON>anager

def create_test_folder():
    """创建测试文件夹"""
    temp_dir = tempfile.mkdtemp()
    test_folder = os.path.join(temp_dir, "测试短剧")
    os.makedirs(test_folder)
    
    # 创建一些测试文件
    test_files = [
        "第01集.mp4",
        "第02集.mp4", 
        "第03集.mp4",
        "字幕.srt",
        "说明.txt"
    ]
    
    for filename in test_files:
        file_path = os.path.join(test_folder, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(f"这是测试文件: {filename}\n" * 100)  # 写入一些内容
    
    print(f"✅ 创建测试文件夹: {test_folder}")
    return test_folder

def test_compression():
    """测试压缩功能"""
    print("=== 测试文件夹压缩功能 ===")
    
    # 创建测试文件夹
    test_folder = create_test_folder()
    
    try:
        # 初始化管理器
        manager = QBUploadManager()
        
        # 测试压缩
        zip_path = manager.compress_folder(test_folder, "测试短剧")
        
        if os.path.exists(zip_path):
            file_size = os.path.getsize(zip_path)
            print(f"✅ 压缩成功: {zip_path}")
            print(f"📦 压缩文件大小: {file_size / 1024:.1f} KB")
            
            # 清理压缩文件
            os.remove(zip_path)
            print(f"🗑️  清理压缩文件")
        else:
            print("❌ 压缩失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件夹
        import shutil
        shutil.rmtree(os.path.dirname(test_folder))
        print(f"🗑️  清理测试文件夹")

if __name__ == "__main__":
    test_compression()
