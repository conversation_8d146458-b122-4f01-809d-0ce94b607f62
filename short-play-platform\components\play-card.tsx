import Link from "next/link"
import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Star, Play, Eye, Calendar } from "lucide-react"
import type { ShortPlay } from "@/lib/data"

export function PlayCard({ play }: { play: ShortPlay }) {
  const hasRating = play.rating && play.rating > 0
  const hasViews = play.views && play.views > 0
  const availablePlatforms = [
    play.pan123.link && '123',
    play.quark.link && '夸克'
  ].filter(Boolean)

  return (
    <Link href={`/plays/${play.id}`} className="group">
      <Card className="overflow-hidden transition-all hover:shadow-lg hover:-translate-y-1 h-full bg-gradient-to-b from-background to-muted/20">
        <CardContent className="p-0 flex flex-col h-full">
          <div className="aspect-[2/3] relative">
            <Image
              src={play.posterUrl || "/placeholder.svg"}
              alt={play.title}
              fill
              className="object-cover transition-transform group-hover:scale-105"
              sizes="(max-width: 768px) 50vw, (max-width: 1200px) 25vw, 20vw"
            />
            
            {/* Category Badge */}
            <div className="absolute top-2 left-2">
              <Badge variant="secondary" className="text-xs bg-background/80 backdrop-blur-sm">
                {play.category.name}
              </Badge>
            </div>
            
            {/* Rating Badge */}
            {hasRating && (
              <div className="absolute top-2 right-2">
                <Badge variant="default" className="text-xs bg-yellow-500/90 text-white backdrop-blur-sm flex items-center gap-1">
                  <Star className="h-3 w-3 fill-current" />
                  {play.rating?.toFixed(1)}
                </Badge>
              </div>
            )}
            
            {/* Play Overlay */}
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center">
              <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                <div className="bg-white/90 rounded-full p-3">
                  <Play className="h-6 w-6 text-primary fill-current" />
                </div>
              </div>
            </div>
          </div>
          
          <div className="p-3 flex-grow flex flex-col">
            <h3 className="font-semibold text-sm line-clamp-2 group-hover:text-primary mb-2 min-h-[2.5rem]">
              {play.title}
            </h3>
            
            {/* Stats Row */}
            <div className="flex items-center justify-between text-xs text-muted-foreground mb-2">
              {hasViews && (
                <div className="flex items-center gap-1">
                  <Eye className="h-3 w-3" />
                  <span>{play.views > 1000 ? `${(play.views / 1000).toFixed(1)}k` : play.views}</span>
                </div>
              )}
              {play.updatedAt && (
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  <span>{new Date(play.updatedAt).toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' })}</span>
                </div>
              )}
            </div>
            
            {/* Platform Indicators */}
            {availablePlatforms.length > 0 && (
              <div className="flex gap-1 mt-auto">
                {availablePlatforms.map((platform) => (
                  <Badge 
                    key={platform} 
                    variant="outline" 
                    className="text-xs px-1.5 py-0.5 h-5 bg-primary/5 border-primary/20 text-primary"
                  >
                    {platform}
                  </Badge>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}
