#!/usr/bin/env python3
"""
夸克网盘转存工具
基于 https://api.moetp.com/docs/?doc=quark API
"""

import requests
import json
import time
import sys
from typing import Dict, Any, Optional
from urllib.parse import urlparse, parse_qs

class QuarkTransfer:
    """夸克网盘转存工具类"""
    
    def __init__(self, token: str, cookie_id: int):
        """
        初始化转存工具
        
        Args:
            token: 用户调用接口token
            cookie_id: cookie管理中的ID
        """
        self.api_base = "https://api.moetp.com/quark/"
        self.token = token
        self.cookie_id = cookie_id
        self.session = requests.Session()
        
    def transfer_file(self, 
                     share_url: str,
                     fid: Optional[str] = None,
                     share: bool = False,
                     url_type: int = 1,
                     code: Optional[str] = None,
                     time_limit: int = 1,
                     repeat: int = 1) -> Dict[str, Any]:
        """
        转存文件
        
        Args:
            share_url: 夸克网盘分享链接
            fid: 保存到指定的目录ID，默认保存到根目录
            share: 是否创建分享链接，默认False
            url_type: 分享类型，1=公开分享，2=密码分享，默认1
            code: 自定义提取码，url_type=2时可用
            time_limit: 有效期，1=永久，2=1天，3=7天，4=30天，默认1
            repeat: 去重方式，1-5，默认1
            
        Returns:
            API响应结果
        """
        # 构建请求参数
        params = {
            "url": share_url,
            "token": self.token,
            "cookie": self.cookie_id,
            "repeat": repeat
        }
        
        # 添加可选参数
        if fid:
            params["fid"] = fid
        if share:
            params["share"] = "true"
            params["url_type"] = url_type
            params["time"] = time_limit
            if url_type == 2 and code:
                params["code"] = code
        
        try:
            print(f"🔄 正在转存文件...")
            print(f"📎 分享链接: {share_url}")
            
            response = self.session.get(self.api_base, params=params, timeout=60)
            response.raise_for_status()
            
            result = response.json()
            
            if result.get("success"):
                print(f"✅ {result.get('message', '转存成功')}")
                return result
            else:
                print(f"❌ 转存失败: {result.get('message', '未知错误')}")
                return result
                
        except requests.exceptions.RequestException as e:
            error_msg = f"网络请求失败: {e}"
            print(f"❌ {error_msg}")
            return {"success": False, "message": error_msg, "data": None}
        except json.JSONDecodeError as e:
            error_msg = f"响应解析失败: {e}"
            print(f"❌ {error_msg}")
            return {"success": False, "message": error_msg, "data": None}
    
    def batch_transfer(self, share_urls: list, **kwargs) -> list:
        """
        批量转存文件
        
        Args:
            share_urls: 分享链接列表
            **kwargs: 其他转存参数
            
        Returns:
            转存结果列表
        """
        results = []
        total = len(share_urls)
        
        print(f"📦 开始批量转存 {total} 个文件")
        print("=" * 50)
        
        for i, url in enumerate(share_urls, 1):
            print(f"\n--- 第 {i}/{total} 个文件 ---")
            
            result = self.transfer_file(url, **kwargs)
            results.append({
                "url": url,
                "result": result,
                "index": i
            })
            
            # 避免请求过于频繁，添加延迟
            if i < total:
                print("⏳ 等待1秒...")
                time.sleep(1)
        
        # 统计结果
        success_count = sum(1 for r in results if r["result"].get("success"))
        print(f"\n📊 批量转存完成: {success_count}/{total} 个文件成功")
        
        return results
    
    def get_repeat_options(self) -> Dict[int, str]:
        """获取去重方式选项"""
        return {
            1: "删除原文件后再转存（默认）",
            2: "修改原文件名后再转存", 
            3: "将原文件移动到待删除目录后再转存",
            4: "忽略删除原文件的错误，继续转存",
            5: "纯转存模式，不检测重复文件，不记录到数据库"
        }
    
    def get_time_options(self) -> Dict[int, str]:
        """获取有效期选项"""
        return {
            1: "永久",
            2: "1天",
            3: "7天", 
            4: "30天"
        }
    
    def format_result(self, result: Dict[str, Any]) -> str:
        """格式化转存结果"""
        if not result.get("success"):
            return f"❌ 失败: {result.get('message', '未知错误')}"
        
        data = result.get("data", {})
        output = [f"✅ 成功: {result.get('message', '转存成功')}"]
        output.append(f"📁 文件名: {data.get('file_name', '未知')}")
        
        if data.get("share_url"):
            output.append(f"🔗 分享链接: {data['share_url']}")
            if data.get("pwd"):
                output.append(f"🔑 提取码: {data['pwd']}")
            
            time_options = self.get_time_options()
            time_desc = time_options.get(data.get("time", 1), "未知")
            output.append(f"⏰ 有效期: {time_desc}")
        
        return "\n".join(output)

def parse_share_url(url: str) -> Optional[str]:
    """解析并验证分享链接格式"""
    if not url.startswith("https://pan.quark.cn/s/"):
        return None
    return url

def main():
    """主函数"""
    print("夸克网盘转存工具")
    print("基于 https://api.moetp.com API")
    print("=" * 50)
    
    # 检查命令行参数
    if len(sys.argv) < 4:
        print("使用方法:")
        print("  python quark_transfer.py <token> <cookie_id> <share_url> [options]")
        print("\n参数说明:")
        print("  token      - 用户调用接口token（在用户中心获取）")
        print("  cookie_id  - cookie管理中的ID")
        print("  share_url  - 夸克网盘分享链接")
        print("\n可选参数:")
        print("  --fid <dir_id>     - 保存到指定目录ID")
        print("  --share            - 创建分享链接")
        print("  --url-type <1|2>   - 分享类型（1=公开，2=密码）")
        print("  --code <password>  - 自定义提取码")
        print("  --time <1|2|3|4>   - 有效期（1=永久，2=1天，3=7天，4=30天）")
        print("  --repeat <1-5>     - 去重方式")
        print("\n示例:")
        print("  python quark_transfer.py abc123 1 https://pan.quark.cn/s/xxxx")
        print("  python quark_transfer.py abc123 1 https://pan.quark.cn/s/xxxx --share --url-type 2")
        return
    
    token = sys.argv[1]
    cookie_id = int(sys.argv[2])
    share_url = sys.argv[3]
    
    # 验证分享链接格式
    if not parse_share_url(share_url):
        print("❌ 无效的分享链接格式")
        print("正确格式: https://pan.quark.cn/s/xxxx")
        return
    
    # 解析可选参数
    args = sys.argv[4:]
    options = {}
    
    i = 0
    while i < len(args):
        if args[i] == "--fid" and i + 1 < len(args):
            options["fid"] = args[i + 1]
            i += 2
        elif args[i] == "--share":
            options["share"] = True
            i += 1
        elif args[i] == "--url-type" and i + 1 < len(args):
            options["url_type"] = int(args[i + 1])
            i += 2
        elif args[i] == "--code" and i + 1 < len(args):
            options["code"] = args[i + 1]
            i += 2
        elif args[i] == "--time" and i + 1 < len(args):
            options["time_limit"] = int(args[i + 1])
            i += 2
        elif args[i] == "--repeat" and i + 1 < len(args):
            options["repeat"] = int(args[i + 1])
            i += 2
        else:
            i += 1
    
    # 创建转存工具实例
    transfer = QuarkTransfer(token, cookie_id)
    
    # 执行转存
    result = transfer.transfer_file(share_url, **options)
    
    # 显示结果
    print("\n" + "=" * 50)
    print("转存结果:")
    print(transfer.format_result(result))

if __name__ == "__main__":
    main()
