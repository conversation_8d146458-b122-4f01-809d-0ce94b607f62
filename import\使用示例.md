# 短剧导入系统使用示例

## 快速开始

### 1. 测试网盘连接
```bash
python main.py --mode test
```
输出示例：
```
=== 网盘测试 ===
123网盘客户端初始化成功
夸克网盘客户端初始化成功
可用网盘: ['123', 'quark']
网盘连接正常
```

### 2. 查看系统状态
```bash
python main.py --status
```

### 3. 仅爬虫模式（不启用网盘）
```bash
python main.py --mode crawler --limit 5
```

### 4. 爬虫模式（启用网盘上传任务创建）
```bash
python main.py --mode crawler --limit 5 --cloud
```

### 5. 完整系统模式（推荐）
```bash
python main.py --mode full --limit 10
```

### 6. 仅监控模式
```bash
python main.py --mode monitor
```

## 详细使用说明

### 爬虫功能
- 从AGSVPT网站爬取最新短剧信息
- 自动下载封面图片到本地
- 导入数据到Flask生产环境数据库
- 支持自动去重

### 网盘上传功能
- 支持123网盘和夸克网盘
- 自动备用网盘切换
- 永久分享链接生成
- 自动更新数据库分享信息

### QB监控功能
- 监控qBittorrent下载完成状态
- 自动匹配数据库中的短剧
- 下载完成后自动上传到网盘

## 配置说明

### 主配置文件 (config.yml)
```yaml
# qBittorrent配置
qbittorrent:
  host: "***********"
  port: 49850
  username: "admin"
  password: "your_password"

# 网盘配置
cloud_storage:
  yunpan123:
    enabled: true
    config_path: "../123/config.json"
  quark:
    enabled: true
    config_path: "../quark/config.yml"

# 导入配置
import:
  auto_upload: true
  preferred_storage: "quark"
```

### 123网盘配置 (../123/config.json)
需要配置client_id和client_secret

### 夸克网盘配置 (../quark/config.yml)
需要配置cookie文件路径

## 工作流程

### 完整自动化流程
1. **数据爬取**: 从AGSVPT爬取短剧信息
2. **数据导入**: 导入到Flask数据库
3. **种子匹配**: 匹配qBittorrent中的种子
4. **下载监控**: 监控下载完成状态
5. **网盘上传**: 自动上传到网盘
6. **分享链接**: 生成分享链接并更新数据库

### 手动流程
1. 运行爬虫获取数据
2. 手动添加种子到qBittorrent
3. 启动监控模式等待下载完成
4. 系统自动处理后续上传和分享

## 常见问题

### Q: 网盘连接失败怎么办？
A: 
1. 检查配置文件路径是否正确
2. 验证API密钥或cookie是否有效
3. 确认网络连接正常

### Q: 数据库连接失败怎么办？
A: 
1. 确认Flask项目路径正确
2. 检查数据库配置
3. 验证数据库权限

### Q: QB连接失败怎么办？
A: 
1. 确认qBittorrent Web UI已启用
2. 检查IP地址和端口
3. 验证用户名密码

### Q: 如何调试问题？
A: 
1. 查看控制台输出的详细日志
2. 检查drama_import.log日志文件
3. 使用--mode test测试各组件

## 高级用法

### 单独使用各模块
```python
# 使用网盘管理器
from cloud_storage_manager import CloudStorageManager
manager = CloudStorageManager()
result = manager.upload_with_fallback("video.mp4")

# 使用主控制器
from drama_import_manager import DramaImportManager
manager = DramaImportManager()
result = manager.run_complete_import_process(10)
```

### 自定义配置
可以修改config.yml中的各项配置来适应不同的使用场景。

## 注意事项

1. 确保所有依赖已正确安装
2. 配置文件路径必须正确
3. 网盘账号需要有足够的存储空间
4. qBittorrent需要开启Web UI
5. Flask项目环境需要正确配置

## 更新和维护

系统采用模块化设计，各功能独立，便于维护和扩展。如需添加新的网盘支持或修改功能，可以参考现有代码结构进行开发。
