"""
图片处理模块 - 下载封面图片并上传到B2存储桶
支持时间戳命名和B2 Cloud Storage集成
"""

import os
import requests
import hashlib
from datetime import datetime
from urllib.parse import urlparse
from pathlib import Path
import tempfile
import io

try:
    from b2sdk.v2 import InMemoryAccountInfo, B2Api
    B2_AVAILABLE = True
except ImportError:
    B2_AVAILABLE = False
    print("警告: b2sdk未安装，B2上传功能不可用")

try:
    from PIL import Image
    PILLOW_AVAILABLE = True
except ImportError:
    PILLOW_AVAILABLE = False
    print("警告: Pillow未安装，图片处理功能受限")


class ImageProcessor:
    """图片处理和B2上传类"""
    
    def __init__(self, b2_key_id=None, b2_app_key=None, bucket_name=None, cdn_domain=None):
        """
        初始化图片处理器
        
        Args:
            b2_key_id: B2 Key ID
            b2_app_key: B2 Application Key  
            bucket_name: B2存储桶名称
            cdn_domain: CDN域名（用于生成最终URL）
        """
        self.b2_key_id = b2_key_id
        self.b2_app_key = b2_app_key
        self.bucket_name = bucket_name
        self.cdn_domain = cdn_domain
        
        # B2 API相关
        self.b2_api = None
        self.bucket = None
        
        # 会话对象，用于复用连接
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 支持的图片格式
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.webp', '.bmp', '.gif'}
        
        print(f"图片处理器初始化 - B2可用: {B2_AVAILABLE}, Pillow可用: {PILLOW_AVAILABLE}")
    
    def _init_b2_connection(self):
        """初始化B2连接"""
        if not B2_AVAILABLE:
            raise Exception("B2 SDK未安装，无法使用B2上传功能")
            
        if not all([self.b2_key_id, self.b2_app_key, self.bucket_name]):
            raise Exception("B2配置信息不完整")
        
        try:
            # 创建B2 API实例
            info = InMemoryAccountInfo()
            self.b2_api = B2Api(info)
            
            # 授权
            self.b2_api.authorize_account("production", self.b2_key_id, self.b2_app_key)
            
            # 获取存储桶
            self.bucket = self.b2_api.get_bucket_by_name(self.bucket_name)
            
            print(f"[OK] B2连接成功 - 存储桶: {self.bucket_name}")
            return True
            
        except Exception as e:
            print(f"[ERROR] B2连接失败: {e}")
            return False
    
    def generate_timestamp_filename(self, original_url, file_extension=None):
        """
        生成基于时间戳的文件名
        
        Args:
            original_url: 原始图片URL
            file_extension: 强制指定的文件扩展名
            
        Returns:
            tuple: (相对路径, 完整时间戳文件名)
        """
        now = datetime.now()
        
        # 生成时间戳 (毫秒级)
        timestamp = int(now.timestamp() * 1000)
        
        # 生成日期路径：2025/08/02
        date_path = now.strftime("%Y/%m/%d")
        
        # 确定文件扩展名
        if file_extension:
            ext = file_extension if file_extension.startswith('.') else f'.{file_extension}'
        else:
            # 从URL解析扩展名
            parsed_url = urlparse(original_url)
            path_ext = Path(parsed_url.path).suffix.lower()
            ext = path_ext if path_ext in self.supported_formats else '.jpg'
        
        # 生成文件名：时间戳.jpg
        filename = f"{timestamp}{ext}"
        
        # 完整相对路径：2025/08/02/时间戳.jpg
        relative_path = f"{date_path}/{filename}"
        
        return relative_path, filename
    
    def download_image(self, image_url, max_size_mb=10, timeout=30):
        """
        下载图片到内存
        
        Args:
            image_url: 图片URL
            max_size_mb: 最大文件大小(MB)
            timeout: 超时时间(秒)
            
        Returns:
            bytes: 图片数据
        """
        if not image_url:
            raise ValueError("图片URL不能为空")
        
        try:
            print(f"  [DOWN] 开始下载图片: {image_url}")
            
            # 下载图片
            response = self.session.get(image_url, timeout=timeout, stream=True)
            response.raise_for_status()
            
            # 检查Content-Type
            content_type = response.headers.get('Content-Type', '').lower()
            if not any(img_type in content_type for img_type in ['image/', 'application/octet-stream']):
                print(f"  [WARN] 警告: 可能不是图片文件，Content-Type: {content_type}")
            
            # 检查文件大小
            content_length = response.headers.get('Content-Length')
            if content_length:
                size_mb = int(content_length) / (1024 * 1024)
                if size_mb > max_size_mb:
                    raise ValueError(f"图片文件过大: {size_mb:.1f}MB > {max_size_mb}MB")
            
            # 读取数据
            image_data = b''
            downloaded_size = 0
            max_size_bytes = max_size_mb * 1024 * 1024
            
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    downloaded_size += len(chunk)
                    if downloaded_size > max_size_bytes:
                        raise ValueError(f"图片文件过大: > {max_size_mb}MB")
                    image_data += chunk
            
            print(f"  [OK] 图片下载完成: {len(image_data)} bytes")
            return image_data
            
        except requests.RequestException as e:
            raise Exception(f"下载图片失败: {e}")
    
    def validate_and_process_image(self, image_data, max_width=1920, max_height=1080, quality=85):
        """
        验证并处理图片（可选的尺寸优化）
        
        Args:
            image_data: 原始图片数据
            max_width: 最大宽度
            max_height: 最大高度  
            quality: JPEG质量
            
        Returns:
            bytes: 处理后的图片数据
        """
        if not PILLOW_AVAILABLE:
            print("  [WARN] Pillow不可用，跳过图片处理")
            return image_data
        
        try:
            # 打开图片
            with Image.open(io.BytesIO(image_data)) as img:
                print(f"  [IMG] 原始图片: {img.size[0]}x{img.size[1]}, 格式: {img.format}")
                
                # 转换为RGB（处理RGBA等）
                if img.mode in ('RGBA', 'LA', 'P'):
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'P':
                        img = img.convert('RGBA')
                    background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                    img = background
                elif img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 调整尺寸（如果需要）
                if img.size[0] > max_width or img.size[1] > max_height:
                    img.thumbnail((max_width, max_height), Image.Resampling.LANCZOS)
                    print(f"  [RESIZE] 图片已调整: {img.size[0]}x{img.size[1]}")
                
                # 保存到内存
                output = io.BytesIO()
                img.save(output, format='JPEG', quality=quality, optimize=True)
                processed_data = output.getvalue()
                
                print(f"  [OK] 图片处理完成: {len(processed_data)} bytes")
                return processed_data
                
        except Exception as e:
            print(f"  [WARN] 图片处理失败，使用原始数据: {e}")
            return image_data
    
    def upload_to_b2(self, image_data, file_path):
        """
        上传图片到B2存储桶
        
        Args:
            image_data: 图片数据
            file_path: 相对路径 (如: 2025/08/02/timestamp.jpg)
            
        Returns:
            str: 完整的图片URL
        """
        if not B2_AVAILABLE:
            raise Exception("B2 SDK不可用")
        
        # 初始化B2连接（如果还没有）
        if not self.bucket:
            if not self._init_b2_connection():
                raise Exception("B2连接失败")
        
        try:
            print(f"  [B2] 开始上传到B2: {file_path}")
            
            # 上传文件
            file_info = self.bucket.upload_bytes(
                data_bytes=image_data,
                file_name=file_path,
                content_type='image/jpeg',
                file_infos={
                    'upload_timestamp': str(int(datetime.now().timestamp()))
                }
            )
            
            # 生成完整URL
            if self.cdn_domain:
                # 使用CDN域名
                full_url = f"https://{self.cdn_domain}/{file_path}"
            else:
                # 使用B2默认URL
                full_url = self.b2_api.get_download_url_for_fileid(file_info.id_)
            
            print(f"  [OK] B2上传成功: {full_url}")
            return full_url
            
        except Exception as e:
            raise Exception(f"B2上传失败: {e}")
    
    def process_image_url(self, image_url, force_extension=None):
        """
        完整的图片处理流程：下载 -> 处理 -> 上传 -> 返回URL
        
        Args:
            image_url: 原始图片URL
            force_extension: 强制使用的文件扩展名
            
        Returns:
            str: 处理后的完整图片URL
        """
        try:
            # 1. 生成时间戳文件名
            relative_path, filename = self.generate_timestamp_filename(image_url, force_extension)
            print(f"  [FILE] 生成文件路径: {relative_path}")
            
            # 2. 下载图片
            image_data = self.download_image(image_url)
            
            # 3. 处理图片（可选）
            processed_data = self.validate_and_process_image(image_data)
            
            # 4. 上传到B2
            final_url = self.upload_to_b2(processed_data, relative_path)
            
            return final_url
            
        except Exception as e:
            print(f"  [ERROR] 图片处理失败: {e}")
            raise


def create_image_processor():
    """
    创建配置好的图片处理器实例
    """
    # B2配置信息
    B2_CONFIG = {
        'b2_key_id': '004771923a64e27000000000b',
        'b2_app_key': 'K004J9LIr8zpbwa7lGFo4gzILGQTmFM',
        'bucket_name': 'duanjusp',
        'cdn_domain': 'djimage.denlu.top'
    }
    
    return ImageProcessor(**B2_CONFIG)


# 测试函数
def test_image_processing():
    """测试图片处理功能"""
    processor = create_image_processor()
    
    # 测试图片URL
    test_urls = [
        "https://example.com/test.jpg",  # 替换为实际的测试URL
        "https://via.placeholder.com/800x600.jpg"
    ]
    
    for url in test_urls:
        try:
            print(f"\n=== 测试图片处理: {url} ===")
            result_url = processor.process_image_url(url)
            print(f"✅ 处理成功: {result_url}")
            
        except Exception as e:
            print(f"❌ 处理失败: {e}")


if __name__ == '__main__':
    test_image_processing()