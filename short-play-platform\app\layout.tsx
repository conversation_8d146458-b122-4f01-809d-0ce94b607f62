import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { Toaster } from "@/components/ui/toaster"
import { SiteHeader } from "@/components/header"
import { getCategories } from "@/lib/data"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "短剧分享平台",
  description: "发现和分享最新最热的短剧资源，提供123网盘和夸克网盘链接。",
    generator: 'v0.dev'
}

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const categories = await getCategories()
  return (
    <html lang="zh-CN">
      <body className={inter.className}>
        <div className="relative flex min-h-screen flex-col">
          <SiteHeader categories={categories} />
          <main className="flex-1">{children}</main>
        </div>
        <Toaster />
      </body>
    </html>
  )
}
