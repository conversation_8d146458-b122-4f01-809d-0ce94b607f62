# qBittorrent 上传管理器

自动监控 qBittorrent 已完成的种子，上传到网盘并更新数据库分享链接。

## 功能特点

- ✅ **直连 MariaDB**: 不依赖 Flask，直接连接生产数据库
- ✅ **双网盘支持**: 支持 123 网盘和夸克网盘，可配置优先级
- ✅ **智能匹配**: 从种子名称提取中文标题，精确匹配数据库短剧
- ✅ **自动压缩**: 文件夹自动压缩为 ZIP 文件后上传
- ✅ **自动去重**: 已有分享链接的短剧自动跳过
- ✅ **配置文件**: 所有配置项独立管理
- ✅ **监控模式**: 支持定时监控和单次运行

## 配置文件

编辑 `qb_config.yml` 配置各项参数：

```yaml
# qBittorrent配置
qbittorrent:
  host: "***********"
  port: 49850
  username: "admin"
  password: "your_password"
  category: "agsvpt"
  download_path: "/downloads/agsvpt"
  host_download_path: "/vol1/1000/downloads/agsvpt"

# 数据库配置
database:
  host: "************"
  user: "sql23721_duanji"
  password: "your_password"
  database: "sql23721_duanji"

# 网盘配置
cloud_storage:
  yunpan123:
    enabled: true
    config_path: "../123/config.json"
    upload_directory: "/网站/短剧/2025"
  quark:
    enabled: true
    config_path: "../quark/config.yml"
  preferred: "quark" # 优先使用的网盘
```

## 使用方法

### 单次运行

```bash
python qb_upload_manager.py
```

### 监控模式

```bash
# 使用配置文件中的间隔时间
python qb_upload_manager.py --monitor

# 自定义监控间隔（秒）
python qb_upload_manager.py --monitor --interval 600
```

## 工作流程

1. **连接 qBittorrent** → 获取 agsvpt 分类的已完成种子
2. **提取标题** → 从种子名称提取中文标题（如：`侯门主母温黎传.Hou.Men...` → `侯门主母温黎传`）
3. **数据库匹配** → 在 MariaDB 中查找匹配的短剧记录
4. **检查去重** → 跳过已有分享链接的短剧
5. **文件查找** → 查找实际文件路径（支持模糊匹配）
6. **文件处理** → 如果是文件夹，自动压缩为 ZIP 文件
7. **网盘上传** → 按优先级上传到网盘
8. **获取链接** → 获取分享链接和提取码
9. **更新数据库** → 将分享信息写入 play_link 表
10. **清理临时文件** → 删除临时压缩文件

## 网盘返回格式

### 夸克网盘

```python
{
    "platform": "夸克网盘",
    "share_url": "https://pan.quark.cn/s/af257e75e17e",
    "extract_code": "",  # 夸克网盘不需要提取码
    "status": "success"
}
```

### 123 网盘

```python
{
    "platform": "123网盘",
    "share_url": "https://www.123pan.com/s/pK8DVv-LFMk",
    "extract_code": "xHnC",
    "status": "success"
}
```

## 数据库表结构

程序会自动操作以下表：

- **play**: 短剧基本信息表
- **play_link**: 分享链接表
  - `play_id`: 关联的短剧 ID
  - `link`: 分享链接
  - `code`: 提取码
  - `platform`: 网盘平台
  - `created_at/updated_at`: 时间戳

## 日志输出示例

```
============================================================
           qBittorrent上传管理器
============================================================
🌐 可用网盘: ['123网盘', '夸克网盘']
📊 获取到 2 个已完成的agsvpt种子

🔍 处理种子: 开到荼靡.Kai.Dao.Tu.Mi.S01.2025.720p.WEB-DL.AVC.AAC.2.0-COMBINE@AGSVWEB
🔍 提取的短剧标题: 开到荼靡
🎯 找到精确匹配: 种子[...] -> 短剧[开到荼靡]
📤 开始上传到夸克网盘: 开到荼靡
✅ 数据库更新成功: 开到荼靡
🎉 处理完成: 开到荼靡
   分享链接: https://pan.quark.cn/s/af257e75e17e

📊 处理完成，成功处理 1 个种子
```

## 故障排除

### 常见问题

1. **数据库连接失败**

   - 检查数据库配置信息
   - 确认网络连接正常

2. **网盘上传失败**

   - 检查网盘配置文件路径
   - 验证网盘账号状态

3. **文件不存在**

   - 确认 Docker 路径映射正确
   - 检查文件实际存储位置

4. **种子匹配失败**
   - 确认数据库中有对应短剧记录
   - 检查标题提取逻辑

### 调试模式

修改配置文件中的日志级别：

```yaml
runtime:
  log_level: "DEBUG"
```

## 依赖要求

```
requests
pymysql
pyyaml
```

## 注意事项

1. 确保 qBittorrent Web UI 已启用
2. 网盘配置文件必须正确配置
3. 数据库连接权限充足
4. 文件路径映射正确（Docker 环境）
