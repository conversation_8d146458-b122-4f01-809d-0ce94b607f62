# 修改分享链接

API： PUT 域名 + /api/v1/share/list/info

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#000000;">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## Body 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | --- |
| shareIdList | []uint64 | <font style="color:#000000;">必填</font> | 分享链接ID列表，数组长度最大为100 |
| trafficSwitch   | int | 选填 | 分享提取流量包   1 全部关闭<br/>2 打开游客免登录提取<br/>3 打开超流量用户提取<br/>4 全部开启 |
| trafficLimitSwitch | int | 选填 | 分享提取流量包流量限制开关<br/>1 关闭限制<br/>2 打开限制 |
| trafficLimit   | int64 | 选填 | 分享提取流量包限制流量<br/>单位：字节 |


## 示例
**请求示例**

```shell
curl --location --request PUT 'https://open-api.123pan.com/api/v1/share/list/info' \
--header 'Content-Type: application/json' \
--header 'Platform: open_platform' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)' \
--data '{
    "shareIdList": [87187530],
    "trafficSwitch": 2,
    "trafficLimitSwitch": 2,
    "trafficLimit": 1073741824
}'
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
.build();
MediaType mediaType = MediaType.parse("application/json");
RequestBody body = RequestBody.create(mediaType, "{\n    \"shareIdList\": [87187530],\n    \"trafficSwitch\": 2,\n    \"trafficLimitSwitch\": 2,\n    \"trafficLimit\": 1073741824\n}");
Request request = new Request.Builder()
.url("https://open-api.123pan.com/api/v1/share/list/info")
.method("PUT", body)
.addHeader("Content-Type", "application/json")
.addHeader("Platform", "open_platform")
.addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)")
.build();
Response response = client.newCall(request).execute();
```

```javascript
var settings = {
  "url": "https://open-api.123pan.com/api/v1/share/list/info",
  "method": "PUT",
  "timeout": 0,
  "headers": {
    "Content-Type": "application/json",
    "Platform": "open_platform",
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)"
  },
  "data": JSON.stringify({
    "shareIdList": [
      87187530
    ],
    "trafficSwitch": 2,
    "trafficLimitSwitch": 2,
    "trafficLimit": 1073741824
  }),
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
```

```javascript
const axios = require('axios');
let data = JSON.stringify({
  "shareIdList": [
    87187530
  ],
  "trafficSwitch": 2,
  "trafficLimitSwitch": 2,
  "trafficLimit": 1073741824
});

let config = {
  method: 'put',
  maxBodyLength: Infinity,
  url: 'https://open-api.123pan.com/api/v1/share/list/info',
  headers: { 
    'Content-Type': 'application/json', 
    'Platform': 'open_platform', 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
  },
  data : data
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});

```

```python
import http.client
import json

conn = http.client.HTTPSConnection("open-api.123pan.com")
payload = json.dumps({
  "shareIdList": [
    87187530
  ],
  "trafficSwitch": 2,
  "trafficLimitSwitch": 2,
  "trafficLimit": 1073741824
})
headers = {
  'Content-Type': 'application/json',
  'Platform': 'open_platform',
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
}
conn.request("PUT", "/api/v1/share/list/info", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

**响应示例**

```json
{
  "code": 0,
  "message": "ok",
  "data": null,
  "x-traceID": "89eb527f-83d3-4727-8abd-26f299e6a1e8_kong-db-5898fdd8c6-d258b"
}
```



> 更新: 2025-07-22 20:46:40  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/tanghycrgh9istqr>