from flask import Blueprint, render_template, request, redirect, url_for, abort
from sqlalchemy import desc, asc, or_, func
from models import db, Play, Category

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """主页"""
    # 获取查询参数
    search = request.args.get('search', '').strip()
    category_slug = request.args.get('category', '')
    sort_by = request.args.get('sort', 'latest')  # latest, popular
    page = request.args.get('page', 1, type=int)
    
    # 构建查询
    query = Play.query.filter(Play.status == 'active')
    
    # 搜索过滤
    if search:
        query = query.filter(
            or_(
                Play.title.contains(search),
                Play.description.contains(search)
            )
        )
    
    # 分类过滤
    if category_slug and category_slug != 'all':
        category = Category.query.filter_by(slug=category_slug).first()
        if category:
            query = query.filter(Play.categories.contains(category))
    
    # 排序
    if sort_by == 'popular':
        query = query.order_by(desc(Play.views))
    else:  # latest
        query = query.order_by(desc(Play.updated_at))
    
    # 分页
    plays = query.paginate(
        page=page, 
        per_page=20, 
        error_out=False
    )
    
    # 获取统计数据
    total_plays = Play.query.filter(Play.status == 'active').count()
    
    # 序列化播放数据
    plays_data = [play.to_dict() for play in plays.items]
    
    return render_template(
        'index.html',
        plays=plays,
        plays_data=plays_data,
        search=search,
        category_slug=category_slug,
        sort_by=sort_by,
        total_plays=total_plays
    )

@main_bp.route('/play/<int:id>')
def play_detail(id):
    """短剧详情页"""
    play = Play.query.get_or_404(id)
    
    # 增加观看次数
    play.increment_views()
    
    # 获取相关推荐（同分类的其他短剧）
    related_plays = []
    if play.primary_category:
        related_plays = Play.query.filter(
            Play.categories.contains(play.primary_category),
            Play.id != play.id,
            Play.status == 'active'
        ).order_by(desc(Play.views)).limit(6).all()
    
    # 获取热门分类用于侧边栏（取前6个）并返回字典格式
    popular_categories_result = db.session.execute(
        db.select(Category, func.count(Play.id).label('play_count'))
        .join(Category.plays)
        .filter(Category.slug != 'all')
        .group_by(Category.id)
        .order_by(func.count(Play.id).desc())
        .limit(6)
    ).all()
    
    popular_categories = []
    for category, play_count in popular_categories_result:
        category_dict = {
            'id': category.id,
            'name': category.name,
            'slug': category.slug,
            'description': category.description,
            'play_count': play_count
        }
        popular_categories.append(category_dict)
    
    return render_template(
        'play_detail.html',
        play=play,
        related_plays=related_plays,
        categories=popular_categories
    )

@main_bp.route('/category/<slug>')
def category(slug):
    """分类页面"""
    if slug == 'all':
        category_name = '全部短剧'
        query = Play.query.filter(Play.status == 'active')
    else:
        category = Category.query.filter_by(slug=slug).first_or_404()
        category_name = f'{category.name}短剧'
        query = Play.query.filter(
            Play.categories.contains(category),
            Play.status == 'active'
        )
    
    # 获取查询参数
    sort_by = request.args.get('sort', 'latest')
    page = request.args.get('page', 1, type=int)
    
    # 排序
    if sort_by == 'popular':
        query = query.order_by(desc(Play.views))
    else:  # latest
        query = query.order_by(desc(Play.updated_at))
    
    # 分页
    plays = query.paginate(
        page=page,
        per_page=20,
        error_out=False
    )
    
    # 获取热门分类用于其他分类推荐（取前8个）并返回字典格式
    popular_categories_result = db.session.execute(
        db.select(Category, func.count(Play.id).label('play_count'))
        .join(Category.plays)
        .filter(Category.slug != 'all')
        .group_by(Category.id)
        .order_by(func.count(Play.id).desc())
        .limit(8)
    ).all()
    
    popular_categories = []
    for category, play_count in popular_categories_result:
        category_dict = {
            'id': category.id,
            'name': category.name,
            'slug': category.slug,
            'description': category.description,
            'play_count': play_count
        }
        popular_categories.append(category_dict)
    
    return render_template(
        'category.html',
        plays=plays,
        category_name=category_name,
        category_slug=slug,
        sort_by=sort_by,
        categories=popular_categories
    )

@main_bp.route('/search')
def search():
    """搜索页面"""
    query_text = request.args.get('q', '').strip()
    category_slug = request.args.get('category', '')
    sort_by = request.args.get('sort', 'latest')
    page = request.args.get('page', 1, type=int)
    
    if not query_text:
        return redirect(url_for('main.index'))
    
    # 构建搜索查询
    query = Play.query.filter(
        Play.status == 'active',
        or_(
            Play.title.contains(query_text),
            Play.description.contains(query_text)
        )
    )
    
    # 分类过滤
    if category_slug and category_slug != 'all':
        category = Category.query.filter_by(slug=category_slug).first()
        if category:
            query = query.filter(Play.categories.contains(category))
    
    # 排序
    if sort_by == 'popular':
        query = query.order_by(desc(Play.views))
    else:  # latest
        query = query.order_by(desc(Play.updated_at))
    
    # 分页
    plays = query.paginate(
        page=page,
        per_page=20,
        error_out=False
    )
    
    # 获取热门分类用于筛选（取前10个）并返回字典格式
    popular_categories_result = db.session.execute(
        db.select(Category, func.count(Play.id).label('play_count'))
        .join(Category.plays)
        .filter(Category.slug != 'all')
        .group_by(Category.id)
        .order_by(func.count(Play.id).desc())
        .limit(10)
    ).all()
    
    popular_categories = []
    for category, play_count in popular_categories_result:
        category_dict = {
            'id': category.id,
            'name': category.name,
            'slug': category.slug,
            'description': category.description,
            'play_count': play_count
        }
        popular_categories.append(category_dict)
    
    return render_template(
        'search.html',
        plays=plays,
        query=query_text,
        category_slug=category_slug,
        sort_by=sort_by,
        categories=popular_categories
    )

@main_bp.route('/offline')
def offline():
    """离线页面"""
    return render_template('offline.html')

@main_bp.route('/categories')
def categories():
    """所有分类页面"""
    # 获取所有分类，按短剧数量排序并返回字典格式
    categories_with_count = db.session.execute(
        db.select(Category, func.count(Play.id).label('play_count'))
        .outerjoin(Category.plays)
        .filter(Category.slug != 'all')
        .group_by(Category.id)
        .order_by(func.count(Play.id).desc())
    ).all()
    
    # 转换为字典格式，以便在模板中使用
    all_categories = []
    for category, play_count in categories_with_count:
        category_dict = {
            'id': category.id,
            'name': category.name,
            'slug': category.slug,
            'description': category.description,
            'play_count': play_count
        }
        all_categories.append(category_dict)
    
    return render_template(
        'categories.html',
        categories=all_categories
    )