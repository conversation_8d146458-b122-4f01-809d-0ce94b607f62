{"permissions": {"allow": ["Bash(del main.py)", "Bash(rm:*)", "<PERSON><PERSON>(python:*)", "WebFetch(domain:poster.seedvault.cn)", "mcp__sequential-thinking__sequentialthinking", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(ls:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(sqlite3:*)", "Bash(cd \"C:\\Users\\<USER>\\Desktop\\code\\flask\")", "Bash(python -c \"\nfrom flask import Flask\nfrom models import db, Category, Play, PlayLink\nimport sqlite3\n\napp = Flask(__name__)\napp.config[''SQLALCHEMY_DATABASE_URI''] = ''sqlite:///plays.db''\napp.config[''SQLALCHEMY_TRACK_MODIFICATIONS''] = False\n\nwith app.app_context():\n    db.init_app(app)\n    \n    # Connect to SQLite directly to inspect table structure\n    conn = sqlite3.connect(''plays.db'')\n    cursor = conn.cursor()\n    \n    # Get all tables\n    cursor.execute(\"\"SELECT name FROM sqlite_master WHERE type=''table'';\"\")\n    tables = cursor.fetchall()\n    \n    print(''Current database tables:'')\n    for table in tables:\n        print(f''- {table[0]}'')\n        \n        # Get table info\n        cursor.execute(f''PRAGMA table_info({table[0]})'')\n        columns = cursor.fetchall()\n        for col in columns:\n            print(f''  {col[1]} ({col[2]})'')\n        print()\n    \n    conn.close()\n\")", "mcp__brave-search__brave_web_search"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["brave-search", "context7", "sequential-thinking", "memory", "playwright", "shrimp-task-manager"]}