# 获取用户信息

API： GET 域名 + /api/v1/user/info

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#000000;">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## Body 参数
无

## 返回数据
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| uid | number | 必填 | 用户账号id |
| nickname | string | 必填 | 昵称 |
| headImage | string | 必填 | 头像 |
| passport | string | 必填 | 手机号码 |
| mail | string | 必填 | 邮箱 |
| spaceUsed | number | 必填 | 已用空间 |
| spacePermanent | number | 必填 | 永久空间 |
| spaceTemp | number | 必填 | 临时空间 |
| spaceTempExpr | string | 必填 | 临时空间到期日 |
| vip | bool | 必填 | 是否会员 |
| directTraffic | number | 必填 | 剩余直链流量 |
| isHideUID | bool | 必填 | 直链链接是否隐藏UID |
| httpsCount | number | 必填 | https数量 |
| vipInfo | array | 必填 | vip信息（非VIP该字段为null） |
| vipInfo.vipLevel | number | 必填 | 1，2，3 VIP SVIP 长期VIP |
| vipInfo.vipLabel | string | 必填 | VIP级别名称 |
| vipInfo.startTime | string | 必填 | 开始时间 |
| vipInfo.endTime | string | 必填 | 结束时间 |
| developerInfo.startTime | string | 必填 | 开发者权益开始时间 |
| developerInfo.endTime | string | 必填 | 开发者权益结束时间 |


## 示例
**请求示例**

```shell
curl --location 'https://open-api.123pan.com/api/v1/user/info' \
--header 'Content-Type: application/json' \
--header 'Platform: open_platform' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)' \
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
.build();
MediaType mediaType = MediaType.parse("application/json");
RequestBody body = RequestBody.create(mediaType, "");
Request request = new Request.Builder()
.url("https://open-api.123pan.com/api/v1/user/info")
.method("GET", body)
.addHeader("Content-Type", "application/json")
.addHeader("Platform", "open_platform")
.addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)")
.build();
Response response = client.newCall(request).execute();
```

```javascript
var settings = {
  "url": "https://open-api.123pan.com/api/v1/user/info",
  "method": "GET",
  "timeout": 0,
  "headers": {
    "Content-Type": "application/json",
    "Platform": "open_platform",
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)"
  },
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
```

```javascript
const axios = require('axios');

let config = {
  method: 'get',
  maxBodyLength: Infinity,
  url: 'https://open-api.123pan.com/api/v1/user/info',
  headers: { 
    'Content-Type': 'application/json', 
    'Platform': 'open_platform', 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
  }
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});

```

```python
import http.client
import json

conn = http.client.HTTPSConnection("open-api.123pan.com")
payload = ''
headers = {
  'Content-Type': 'application/json',
  'Platform': 'open_platform',
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
}
conn.request("GET", "/api/v1/user/info", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

**响应示例**

```json
{
	"code": 0,
	"message": "ok",
	"data": {
		"uid": 1814442530,
		"nickname": "志中测试专属号003",
		"headImage": "https://download-cdn.123295.com/123-pics/head-pic/1814442530.jpg?v=1&t=4905124782&s=0a71629d4361a5e9a95316d6997c5c67&bzc=0&bzs=",
		"passport": "15184637593",
		"mail": "",
		"spaceUsed": ************,
		"spacePermanent": 6894212784062464,
		"spaceTemp": 0,
		"spaceTempExpr": 0,
		"vip": true,
		"directTraffic": 276046404229020,
		"isHideUID": false,
		"httpsCount": 9999998,
		"vipInfo": [
			{
				"vipLevel": 1,
				"vipLabel": "VIP",
				"startTime": "2025-07-06 11:12:53",
				"endTime": "2026-05-14 14:13:05"
			},
			{
				"vipLevel": 2,
				"vipLabel": "SVIP",
				"startTime": "2025-06-06 11:12:53",
				"endTime": "2025-07-06 11:12:52"
			},
			{
				"vipLevel": 3,
				"vipLabel": "长期VIP",
				"startTime": "2025-04-27 11:18:49",
				"endTime": "9999-12-31 23:59:59"
			}
		],
    "developerInfo": {
        "startTime": "2025-07-16 17:27:53",
        "endTime": "2026-07-16 17:27:52"
    }
	},
	"x-traceID": ""
}
```



> 更新: 2025-07-11 10:02:44  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/zgf9gyh7gvmdl4a3>