{"mcpServers": {"brave-search": {"command": "cmd", "args": ["/c", "npx", "-y", "brave-search-mcp@latest"], "env": {"BRAVE_API_KEY": "YOUR_KEY"}}, "context7": {"command": "cmd", "args": ["/c", "npx", "-y", "@upstash/context7-mcp@latest"]}, "sequential-thinking": {"command": "cmd", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-sequential-thinking@latest"]}, "memory": {"command": "cmd", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-memory@latest"]}, "playwright": {"command": "cmd", "args": ["/c", "npx", "-y", "@executeautomation/playwright-mcp-server@latest"]}, "shrimp-task-manager": {"command": "cmd", "args": ["/c", "npx", "-y", "mcp-shrimp-task-manager@latest"]}}}