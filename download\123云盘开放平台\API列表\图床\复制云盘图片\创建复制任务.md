# 创建复制任务

API： POST 域名 + /api/v1/oss/source/copy

说明：图床复制任务创建（可创建的任务数：3，fileIDs 长度限制：100，当前一个任务处理完后将会继续处理下个任务）

该接口将会复制云盘里的文件或目录对应的图片到对应图床目录，每次任务包含的图片总数限制 1000 张，图片格式：png, gif, jpeg, tiff, webp,jpg,tif,svg,bmp，图片大小限制：100M，文件夹层级限制：15层

如果图床目录下存在相同 etag、size 的图片将会视为同一张图片，将覆盖原图片

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#000000;">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## Body 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| fileIDs | array | 必填 | 文件id数组(string 数组) |
| toParentFileID | string | 必填 | 要移动到的图床目标文件夹id，移动到根目录时为空 |
| sourceType | string | 必填 | 复制来源(1=云盘) |
| <font style="color:#000000;">type</font> | <font style="color:#000000;">number</font> | <font style="color:#000000;">必填</font> | <font style="color:#000000;">业务类型，固定为 1</font> |


## 返回数据
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| taskID | string | 必填 | 复制任务ID,可通过该ID,调用查询复制任务状态 |


## 示例
**请求示例**

```shell
curl --location 'https://open-api.123pan.com/api/v1/oss/source/copy' \
--header 'Content-Type: application/json' \
--header 'Platform: open_platform' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)' \
--data '{
    "fileIDs": [
        "14802190",
        "14802189"
    ],
    "toParentFileID": "yk6baz03t0l000d7w33fbyq51l4izkneDIYPAIDOBIY0DcxvDwFO",
    "sourceType": 1,
    "type": 1
}'
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
.build();
MediaType mediaType = MediaType.parse("application/json");
RequestBody body = RequestBody.create(mediaType, "{\n    \"fileIDs\": [\n        \"14802190\",\n        \"14802189\"\n    ],\n    \"toParentFileID\": \"yk6baz03t0l000d7w33fbyq51l4izkneDIYPAIDOBIY0DcxvDwFO\",\n    \"sourceType\": 1,\n    \"type\": 1\n}");
Request request = new Request.Builder()
.url("https://open-api.123pan.com/api/v1/oss/source/copy")
.method("POST", body)
.addHeader("Content-Type", "application/json")
.addHeader("Platform", "open_platform")
.addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)")
.build();
Response response = client.newCall(request).execute();
```

```javascript
var settings = {
  "url": "https://open-api.123pan.com/api/v1/oss/source/copy",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "Content-Type": "application/json",
    "Platform": "open_platform",
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)"
  },
  "data": JSON.stringify({
    "fileIDs": [
      "14802190",
      "14802189"
    ],
    "toParentFileID": "yk6baz03t0l000d7w33fbyq51l4izkneDIYPAIDOBIY0DcxvDwFO",
    "sourceType": 1,
    "type": 1
  }),
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
```

```javascript
const axios = require('axios');
let data = JSON.stringify({
  "fileIDs": [
    "14802190",
    "14802189"
  ],
  "toParentFileID": "yk6baz03t0l000d7w33fbyq51l4izkneDIYPAIDOBIY0DcxvDwFO",
  "sourceType": 1,
  "type": 1
});

let config = {
  method: 'post',
  maxBodyLength: Infinity,
  url: 'https://open-api.123pan.com/api/v1/oss/source/copy',
  headers: { 
    'Content-Type': 'application/json', 
    'Platform': 'open_platform', 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
  },
  data : data
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});

```

```python
import http.client
import json

conn = http.client.HTTPSConnection("open-api.123pan.com")
payload = json.dumps({
    "fileIDs": [
        "14802190",
        "14802189"
    ],
    "toParentFileID": "yk6baz03t0l000d7w33fbyq51l4izkneDIYPAIDOBIY0DcxvDwFO",
    "sourceType": 1,
    "type": 1
})
headers = {
    'Content-Type': 'application/json',
    'Platform': 'open_platform',
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
}
conn.request("POST", "/api/v1/oss/source/copy", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

**响应示例**

```json
{
    "code": 0,
    "message": "ok",
    "data": {
        "taskID": "1ketmmu1000d86isuyq33su100w1bnp6"
    },
    "x-traceID": "37e67458-c3b3-462b-82b5-71a43d9848a7_kong-db-5898fdd8c6-t5pvc"
}
```



> 更新: 2025-03-17 19:16:55  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/trahy3lmds4o0i3r>