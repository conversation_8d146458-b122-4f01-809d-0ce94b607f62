from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import String, Text, Integer, Float, Boolean, DateTime, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship
from typing import List, Optional

db = SQLAlchemy()

# 多对多关联表 - 短剧与分类的关系
play_categories = db.Table('play_categories',
    db.Column('play_id', Integer, ForeignKey('play.id'), primary_key=True),
    db.<PERSON>umn('category_id', Integer, ForeignKey('category.id'), primary_key=True)
)

class Category(db.Model):
    """分类模型"""
    __tablename__ = 'category'
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    name: Mapped[str] = mapped_column(String(50), nullable=False)
    slug: Mapped[str] = mapped_column(String(50), unique=True, nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    order_index: Mapped[int] = mapped_column(Integer, default=0)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    plays: Mapped[List["Play"]] = relationship("Play", back_populates="categories", secondary=play_categories)
    
    def __repr__(self):
        return f'<Category {self.name}>'
    
    @property
    def play_count(self):
        """获取该分类下的短剧数量"""
        return len(self.plays)
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'slug': self.slug,
            'description': self.description,
            'order_index': self.order_index,
            'play_count': self.play_count,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class Play(db.Model):
    """短剧模型"""
    __tablename__ = 'play'
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    title: Mapped[str] = mapped_column(String(200), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    poster_url: Mapped[Optional[str]] = mapped_column(String(500))
    # 移除单一分类外键，改为多对多关系
    year: Mapped[Optional[int]] = mapped_column(Integer)  # 年份字段
    views: Mapped[int] = mapped_column(Integer, default=0)
    episodes: Mapped[Optional[int]] = mapped_column(Integer)
    status: Mapped[str] = mapped_column(String(20), default='active')  # active, inactive, pending
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    categories: Mapped[List["Category"]] = relationship("Category", back_populates="plays", secondary=play_categories)
    links: Mapped[List["PlayLink"]] = relationship("PlayLink", back_populates="play", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f'<Play {self.title}>'
    
    @property
    def primary_category(self):
        """获取主要分类（第一个分类）"""
        return self.categories[0] if self.categories else None
    
    @property
    def pan123_link(self):
        """获取123网盘链接"""
        link = next((l for l in self.links if l.platform == '123pan'), None)
        return link
    
    @property
    def quark_link(self):
        """获取夸克网盘链接"""
        link = next((l for l in self.links if l.platform == 'quark'), None)
        return link
    
    def increment_views(self):
        """增加观看次数"""
        self.views = (self.views or 0) + 1
        db.session.commit()
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'poster_url': self.poster_url,
            'categories': [{'id': cat.id, 'name': cat.name, 'slug': cat.slug} for cat in self.categories],
            'primary_category': self.primary_category.name if self.primary_category else None,
            'year': self.year,
            'views': self.views,
            'episodes': self.episodes,
            'status': self.status,
            'pan123_link': self.pan123_link.link if self.pan123_link else None,
            'quark_link': self.quark_link.link if self.quark_link else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class PlayLink(db.Model):
    """网盘链接模型"""
    __tablename__ = 'play_link'
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    play_id: Mapped[int] = mapped_column(Integer, ForeignKey('play.id'), nullable=False)
    platform: Mapped[str] = mapped_column(String(20), nullable=False)  # 123pan, quark
    link: Mapped[str] = mapped_column(String(500), nullable=False)
    code: Mapped[Optional[str]] = mapped_column(String(20))
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    play: Mapped["Play"] = relationship("Play", back_populates="links")
    
    # 唯一约束
    __table_args__ = (db.UniqueConstraint('play_id', 'platform', name='_play_platform_uc'),)
    
    def __repr__(self):
        return f'<PlayLink {self.platform}:{self.play.title}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'play_id': self.play_id,
            'platform': self.platform,
            'link': self.link,
            'code': self.code,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

# 数据库初始化函数
def init_db():
    """初始化数据库"""
    db.create_all()

def seed_data():
    """创建示例数据"""
    # 检查是否已有数据
    if Category.query.first():
        return
    
    # 创建分类
    categories = [
        Category(name='全部', slug='all', order_index=0),
        Category(name='重生', slug='rebirth', order_index=1),
        Category(name='都市', slug='urban', order_index=2),
        Category(name='古装', slug='historical', order_index=3),
        Category(name='玄幻', slug='fantasy', order_index=4),
        Category(name='言情', slug='romance', order_index=5),
        Category(name='甜宠', slug='sweet', order_index=6),
        Category(name='复仇', slug='revenge', order_index=7),
        Category(name='穿越', slug='timetravel', order_index=8),
    ]
    
    for category in categories:
        db.session.add(category)
    
    db.session.commit()
    
    # 创建示例短剧
    plays_data = [
        {
            'title': '重生之我在霸总文里当保姆',
            'description': '一场意外，她重生到一本霸总小说里，成了男主角家的保姆，且看她如何逆袭，俘获霸总的心。',
            'poster_url': '/static/images/placeholder.jpg',
            'categories': ['重生', '甜宠'],
            'year': 2024,
            'views': 12580,
            'episodes': 80,
            'links': [
                {'platform': '123pan', 'link': 'https://www.123pan.com/s/abc-123', 'code': 'a1b2'},
                {'platform': 'quark', 'link': 'https://pan.quark.cn/s/xyz-789', 'code': 'x7y8'}
            ]
        },
        {
            'title': '我的女友是机器人',
            'description': '一个普通的程序员，意外收到了一个来自未来的机器人女友，生活从此发生了翻天覆地的变化。',
            'poster_url': '/static/images/placeholder.jpg',
            'categories': ['都市'],
            'year': 2023,
            'views': 8920,
            'episodes': 60,
            'links': [
                {'platform': '123pan', 'link': 'https://www.123pan.com/s/def-456', 'code': ''},
                {'platform': 'quark', 'link': 'https://pan.quark.cn/s/uvw-456', 'code': 'u4v5'}
            ]
        },
        {
            'title': '穿越时空来爱你',
            'description': '她来自21世纪，他来自古代王朝，一根红线将他们跨越时空紧紧相连。',
            'poster_url': '/static/images/placeholder.jpg',
            'categories': ['古装', '穿越', '言情'],
            'year': 2024,
            'views': 24350,
            'episodes': 100,
            'links': [
                {'platform': '123pan', 'link': 'https://www.123pan.com/s/ghi-789', 'code': 'g7h8'},
                {'platform': 'quark', 'link': 'https://pan.quark.cn/s/rst-123', 'code': ''}
            ]
        },
        {
            'title': '都市奇缘',
            'description': '在繁华的都市中，两个陌生人因为一次偶然的相遇，展开了一段奇妙的缘分。',
            'poster_url': '/static/images/placeholder.jpg',
            'categories': ['都市', '言情'],
            'year': 2023,
            'views': 6750,
            'episodes': 45,
            'links': [
                {'platform': '123pan', 'link': 'https://www.123pan.com/s/jkl-012', 'code': 'j0k1'},
                {'platform': 'quark', 'link': 'https://pan.quark.cn/s/opq-789', 'code': 'o7p8'}
            ]
        },
        {
            'title': '千金归来',
            'description': '被调换人生的千金，在历经磨难后，强势归来，夺回属于自己的一切。',
            'poster_url': '/static/images/placeholder.jpg',
            'categories': ['重生', '复仇'],
            'year': 2024,
            'views': 15670,
            'episodes': 90,
            'links': [
                {'platform': '123pan', 'link': 'https://www.123pan.com/s/mno-345', 'code': ''},
                {'platform': 'quark', 'link': 'https://pan.quark.cn/s/lmn-456', 'code': ''}
            ]
        },
        {
            'title': '王爷，王妃又跑了',
            'description': '她是21世纪的特工，穿越成王妃，每天想着的不是宫斗，而是如何逃出王府。',
            'poster_url': '/static/images/placeholder.jpg',
            'categories': ['古装', '穿越'],
            'year': 2023,
            'views': 9820,
            'episodes': 70,
            'links': [
                {'platform': '123pan', 'link': 'https://www.123pan.com/s/pqr-678', 'code': 'p6q7'},
                {'platform': 'quark', 'link': 'https://pan.quark.cn/s/ijk-123', 'code': 'i1j2'}
            ]
        }
    ]
    
    for play_data in plays_data:
        # 创建短剧
        play_categories_list = play_data.pop('categories', [])
        play_links_list = play_data.pop('links', [])
        
        play = Play(**play_data)
        db.session.add(play)
        db.session.flush()  # 获取play.id
        
        # 添加分类关联
        for category_name in play_categories_list:
            category = Category.query.filter_by(name=category_name).first()
            if category:
                play.categories.append(category)
        
        # 添加链接
        for link_data in play_links_list:
            link = PlayLink(
                play_id=play.id,
                platform=link_data['platform'],
                link=link_data['link'],
                code=link_data['code']
            )
            db.session.add(link)
    
    db.session.commit()