#!/usr/bin/env python3
"""
qBittorrent上传管理器
监控qBittorrent已完成的种子，上传到网盘并更新数据库分享链接
直接连接MariaDB生产数据库，不依赖Flask
"""

import os
import sys
import requests
import time
import re
import pymysql
import yaml
from pathlib import Path
from typing import Dict, List, Optional
from datetime import datetime

# 添加网盘模块路径
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.append(str(project_root / "123"))
sys.path.append(str(project_root / "quark"))

# 导入网盘模块
try:
    from upload_share import YunPan123Client
except ImportError:
    YunPan123Client = None
    print("警告: 无法导入123网盘模块")

try:
    from quark import QuarkPan
except ImportError:
    QuarkPan = None
    print("警告: 无法导入夸克网盘模块")


class QBUploadManager:
    """qBittorrent上传管理器"""

    def __init__(self, config_path: str = "qb_config.yml"):
        # 加载配置文件
        self.config = self._load_config(config_path)

        # qBittorrent配置
        qb_config = self.config['qbittorrent']
        self.qb_host = qb_config['host']
        self.qb_port = qb_config['port']
        self.qb_user = qb_config['username']
        self.qb_pass = qb_config['password']
        self.qb_base_url = f"http://{self.qb_host}:{self.qb_port}"

        # 种子过滤配置
        self.qb_category = qb_config['category']
        self.qb_download_path = qb_config['download_path']
        self.host_download_path = qb_config['host_download_path']

        # 数据库配置
        self.db_config = self.config['database']
        
        # 网盘客户端
        self.yunpan123_client = None
        self.quark_client = None

        # 初始化网盘客户端
        self._init_cloud_clients()

        # 登录qBittorrent
        self.session = requests.Session()
        self._login_qb()

    def _load_config(self, config_path: str) -> dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"❌ 加载配置文件失败: {e}")
            sys.exit(1)
    
    def _init_cloud_clients(self):
        """初始化网盘客户端"""
        cloud_config = self.config['cloud_storage']

        # 初始化123网盘
        if cloud_config['yunpan123']['enabled'] and YunPan123Client:
            try:
                config_path = cloud_config['yunpan123']['config_path']
                if not os.path.isabs(config_path):
                    config_path = str(project_root / config_path.lstrip("../"))

                if os.path.exists(config_path):
                    self.yunpan123_client = YunPan123Client(config_path)
                    print("✅ 123网盘客户端初始化成功")
            except Exception as e:
                print(f"❌ 123网盘客户端初始化失败: {e}")

        # 初始化夸克网盘
        if cloud_config['quark']['enabled'] and QuarkPan:
            try:
                config_path = cloud_config['quark']['config_path']
                if not os.path.isabs(config_path):
                    config_path = str(project_root / config_path.lstrip("../"))

                if os.path.exists(config_path):
                    # 临时切换到quark目录
                    original_cwd = os.getcwd()
                    try:
                        os.chdir(str(Path(config_path).parent))
                        self.quark_client = QuarkPan(config_path)
                        print("✅ 夸克网盘客户端初始化成功")
                    finally:
                        os.chdir(original_cwd)
                else:
                    print(f"❌ 夸克网盘配置文件不存在: {config_path}")
            except Exception as e:
                print(f"❌ 夸克网盘客户端初始化失败: {e}")
    
    def _login_qb(self):
        """登录qBittorrent"""
        try:
            login_url = f"{self.qb_base_url}/api/v2/auth/login"
            login_data = {"username": self.qb_user, "password": self.qb_pass}
            
            response = self.session.post(login_url, data=login_data, timeout=10)
            response.raise_for_status()
            
            if response.text == "Ok.":
                print("✅ qBittorrent登录成功")
            else:
                raise Exception("登录失败")
                
        except Exception as e:
            print(f"❌ qBittorrent登录失败: {e}")
            sys.exit(1)
    
    def get_completed_torrents(self) -> List[Dict]:
        """获取已完成的种子列表（仅agsvpt分类）"""
        try:
            url = f"{self.qb_base_url}/api/v2/torrents/info"
            params = {
                "filter": "completed",  # 只获取已完成的种子
                "category": self.qb_category  # 只获取agsvpt分类的种子
            }

            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()

            torrents = response.json()

            # 进一步过滤：确保下载路径正确
            filtered_torrents = []
            for torrent in torrents:
                save_path = torrent.get("save_path", "")
                if save_path.startswith(self.qb_download_path):
                    filtered_torrents.append(torrent)

            print(f"📊 获取到 {len(filtered_torrents)} 个已完成的agsvpt种子")

            return filtered_torrents

        except Exception as e:
            print(f"❌ 获取种子列表失败: {e}")
            return []
    
    def extract_drama_title(self, torrent_name: str) -> str:
        """从种子名称中提取短剧标题"""
        if not torrent_name:
            return ""

        # 示例: 侯门主母温黎传.Hou.Men.Zhu.Mu.Wen.Li.Chuan.2025.S01.720p.WEB-DL.H265.AAC-GodDramas
        # 提取中文部分（第一个点之前的部分）
        parts = torrent_name.split('.')
        if parts:
            chinese_title = parts[0]
            # 只保留中文字符
            chinese_title = re.sub(r'[^\u4e00-\u9fa5]', '', chinese_title)
            return chinese_title.strip()

        return ""

    def clean_title_for_match(self, title: str) -> str:
        """清理标题用于精确匹配"""
        if not title:
            return ""

        # 只保留中文字符
        title = re.sub(r'[^\u4e00-\u9fa5]', '', title)
        return title.strip()
    
    def find_matching_play(self, torrent_name: str):
        """在数据库中查找匹配的短剧"""
        try:
            # 从种子名称中提取短剧标题
            drama_title = self.extract_drama_title(torrent_name)
            if not drama_title:
                print(f"❌ 无法从种子名称提取标题: {torrent_name}")
                return None

            print(f"🔍 提取的短剧标题: {drama_title}")

            # 直接连接MariaDB数据库
            connection = pymysql.connect(**self.db_config)
            try:
                with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                    # 查询所有短剧
                    cursor.execute("SELECT id, title FROM play WHERE status = 'active'")
                    plays = cursor.fetchall()

                    for play in plays:
                        clean_play_title = self.clean_title_for_match(play['title'])

                        # 精确匹配中文标题
                        if clean_play_title == drama_title:
                            print(f"🎯 找到精确匹配: 种子[{torrent_name}] -> 短剧[{play['title']}]")
                            return play

                    print(f"❌ 未找到匹配的短剧: {drama_title}")
                    return None
            finally:
                connection.close()

        except Exception as e:
            print(f"❌ 数据库查询失败: {e}")
            return None
    
    def upload_to_cloud(self, file_path: str, title: str) -> Optional[Dict]:
        """上传文件到网盘"""
        cloud_config = self.config['cloud_storage']
        preferred = cloud_config['preferred']

        # 根据配置确定上传顺序
        upload_order = []
        if preferred == "quark" and self.quark_client:
            upload_order.append("quark")
        if preferred == "yunpan123" and self.yunpan123_client:
            upload_order.append("yunpan123")

        # 添加备用网盘
        if self.quark_client and "quark" not in upload_order:
            upload_order.append("quark")
        if self.yunpan123_client and "yunpan123" not in upload_order:
            upload_order.append("yunpan123")

        # 尝试上传
        for storage in upload_order:
            try:
                if storage == "quark":
                    print(f"📤 开始上传到夸克网盘: {title}")

                    if os.path.isdir(file_path):
                        # 上传文件夹：获取文件夹中的所有文件
                        import glob
                        all_files = []
                        for ext in ['*.mp4', '*.mkv', '*.avi', '*.mov', '*.wmv', '*.flv', '*.webm']:
                            all_files.extend(glob.glob(os.path.join(file_path, '**', ext), recursive=True))

                        if all_files:
                            print(f"📁 找到 {len(all_files)} 个视频文件")
                            result = self.quark_client.upload_files(all_files)
                            # 取第一个文件的分享链接
                            if isinstance(result, list) and result:
                                result = result[0]
                        else:
                            # 如果没有视频文件，尝试上传整个文件夹的第一个文件
                            all_files = [os.path.join(file_path, f) for f in os.listdir(file_path)
                                        if os.path.isfile(os.path.join(file_path, f))]
                            if all_files:
                                result = self.quark_client.upload_file(all_files[0])
                            else:
                                raise Exception("文件夹中没有可上传的文件")
                    else:
                        # 上传单个文件
                        result = self.quark_client.upload_file(file_path)

                    return {
                        "platform": "夸克网盘",
                        "share_url": result["share_url"],
                        "extract_code": "",  # 夸克网盘不需要提取码
                        "status": "success"
                    }

                elif storage == "yunpan123":
                    print(f"📤 开始上传到123网盘: {title}")
                    upload_dir = cloud_config['yunpan123']['upload_directory']

                    if os.path.isdir(file_path):
                        # 上传文件夹：找到最大的视频文件
                        import glob
                        video_files = []
                        for ext in ['*.mp4', '*.mkv', '*.avi', '*.mov', '*.wmv', '*.flv', '*.webm']:
                            video_files.extend(glob.glob(os.path.join(file_path, '**', ext), recursive=True))

                        if video_files:
                            # 选择最大的文件
                            largest_file = max(video_files, key=os.path.getsize)
                            print(f"📁 选择最大的视频文件: {os.path.basename(largest_file)}")
                            file_to_upload = largest_file
                        else:
                            # 如果没有视频文件，选择文件夹中最大的文件
                            all_files = [os.path.join(file_path, f) for f in os.listdir(file_path)
                                        if os.path.isfile(os.path.join(file_path, f))]
                            if all_files:
                                file_to_upload = max(all_files, key=os.path.getsize)
                                print(f"📁 选择最大的文件: {os.path.basename(file_to_upload)}")
                            else:
                                raise Exception("文件夹中没有可上传的文件")

                        result = self.yunpan123_client.upload_and_share(
                            file_path=file_to_upload,
                            directory_path=upload_dir,
                            share_name=f"短剧: {title}"
                        )
                    else:
                        # 上传单个文件
                        result = self.yunpan123_client.upload_and_share(
                            file_path=file_path,
                            directory_path=upload_dir,
                            share_name=f"短剧: {title}"
                        )

                    return {
                        "platform": "123网盘",
                        "share_url": result["shareUrl"],
                        "extract_code": result.get("extractCode", ""),
                        "status": "success"
                    }

            except Exception as e:
                print(f"❌ {storage}网盘上传失败: {e}")
                continue

        print("❌ 所有网盘上传都失败了")
        return None
    
    def update_play_link(self, play, share_info: Dict):
        """更新短剧的分享链接"""
        try:
            # 直接连接MariaDB数据库
            connection = pymysql.connect(**self.db_config)
            try:
                with connection.cursor() as cursor:
                    # 检查是否已存在PlayLink记录
                    cursor.execute("SELECT id FROM play_link WHERE play_id = %s", (play['id'],))
                    existing_link = cursor.fetchone()

                    current_time = datetime.now()

                    if existing_link:
                        # 更新现有记录
                        cursor.execute("""
                            UPDATE play_link
                            SET link_url = %s, link_password = %s, platform = %s, updated_at = %s
                            WHERE play_id = %s
                        """, (
                            share_info["share_url"],
                            share_info["extract_code"],
                            share_info["platform"],
                            current_time,
                            play['id']
                        ))
                    else:
                        # 创建新记录
                        cursor.execute("""
                            INSERT INTO play_link (play_id, link_url, link_password, platform, created_at, updated_at)
                            VALUES (%s, %s, %s, %s, %s, %s)
                        """, (
                            play['id'],
                            share_info["share_url"],
                            share_info["extract_code"],
                            share_info["platform"],
                            current_time,
                            current_time
                        ))

                    connection.commit()
                    print(f"✅ 数据库更新成功: {play['title']}")
            finally:
                connection.close()

        except Exception as e:
            print(f"❌ 数据库更新失败: {e}")
    
    def process_completed_torrents(self):
        """处理已完成的种子"""
        torrents = self.get_completed_torrents()
        
        if not torrents:
            print("📭 没有已完成的种子")
            return
        
        processed_count = 0
        
        for torrent in torrents:
            torrent_name = torrent["name"]
            torrent_path = torrent["save_path"]
            
            print(f"\n🔍 处理种子: {torrent_name}")
            
            # 查找匹配的短剧
            play = self.find_matching_play(torrent_name)
            if not play:
                continue
            
            # 检查是否已有分享链接
            try:
                connection = pymysql.connect(**self.db_config)
                try:
                    with connection.cursor() as cursor:
                        cursor.execute("SELECT link_url FROM play_link WHERE play_id = %s", (play['id'],))
                        existing_link = cursor.fetchone()
                        if existing_link and existing_link[0]:
                            print(f"⏭️  短剧已有分享链接，跳过: {play['title']}")
                            continue
                finally:
                    connection.close()
            except:
                pass
            
            # 构建文件路径（映射Docker路径到宿主机路径）
            if torrent_path == self.qb_download_path:
                full_path = self.host_download_path + "/" + torrent_name
            else:
                full_path = torrent_path + "/" + torrent_name
            
            # 检查路径是否存在
            if not os.path.exists(full_path):
                print(f"❌ 路径不存在: {full_path}")
                print(f"🔍 尝试查找匹配文件...")

                # 尝试查找实际文件
                import glob
                search_pattern = f"{self.host_download_path}/*{play['title']}*"
                matching_files = glob.glob(search_pattern)

                if matching_files:
                    full_path = matching_files[0]
                    print(f"✅ 找到匹配路径: {full_path}")
                else:
                    print(f"❌ 未找到任何匹配文件，跳过")
                    continue

            # 检查是文件还是目录
            if os.path.isdir(full_path):
                print(f"📁 检测到目录: {full_path}")
                print(f"📤 将上传整个文件夹")
            elif os.path.isfile(full_path):
                print(f"📄 检测到文件: {full_path}")
            else:
                print(f"❌ 路径类型未知，跳过: {full_path}")
                continue
            
            # 上传到网盘
            share_info = self.upload_to_cloud(full_path, play['title'])
            if not share_info:
                continue

            # 更新数据库
            self.update_play_link(play, share_info)
            processed_count += 1

            print(f"🎉 处理完成: {play['title']}")
            print(f"   分享链接: {share_info['share_url']}")
            if share_info['extract_code']:
                print(f"   提取码: {share_info['extract_code']}")
        
        print(f"\n📊 处理完成，成功处理 {processed_count} 个种子")
    
    def run_once(self):
        """运行一次处理"""
        print("=" * 60)
        print("           qBittorrent上传管理器")
        print("=" * 60)
        
        # 显示网盘状态
        available_clouds = []
        if self.yunpan123_client:
            available_clouds.append("123网盘")
        if self.quark_client:
            available_clouds.append("夸克网盘")
        
        print(f"🌐 可用网盘: {available_clouds}")
        
        if not available_clouds:
            print("❌ 没有可用的网盘，程序退出")
            return
        
        # 处理种子
        self.process_completed_torrents()
    
    def run_monitor(self, interval: Optional[int] = None):
        """监控模式运行"""
        if interval is None:
            interval = self.config['runtime']['monitor_interval']
        print(f"🔄 启动监控模式，检查间隔: {interval}秒")
        
        try:
            while True:
                self.run_once()
                print(f"\n⏰ 等待 {interval} 秒后进行下次检查...")
                time.sleep(interval)
        except KeyboardInterrupt:
            print("\n👋 监控已停止")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='qBittorrent上传管理器')
    parser.add_argument('--monitor', action='store_true', help='监控模式')
    parser.add_argument('--interval', type=int, default=300, help='监控间隔（秒）')
    
    args = parser.parse_args()
    
    manager = QBUploadManager()
    
    if args.monitor:
        manager.run_monitor(args.interval)
    else:
        manager.run_once()


if __name__ == "__main__":
    main()
