# 获取上传域名

API： GET 域名 + /upload/v2/file/domain

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#DF2A3F;">必填</font> | 鉴权access_token |
| Platform | string | <font style="color:#DF2A3F;">必填</font> | 固定为:open_platform |


## Body 参数
无

## 返回数据 
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | --- |
| data | array | 必填 | 上传域名，存在多个可以任选其一 |


## 示例
**请求示例**

```shell
curl --request GET \
  --url https://open-api.123pan.com/upload/v2/file/domain \
  --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5c...(过长省略)' \
  --header 'Platform: open_platform' \
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
  .build();
MediaType mediaType = MediaType.parse("text/plain");
RequestBody body = RequestBody.create(mediaType, "");
Request request = new Request.Builder()
  .url("https://open-api.123pan.com/upload/v2/file/domain")
  .method("GET", body)
  .addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5c...(过长省略)")
  .addHeader("Platform", "open_platform")
  .build();
Response response = client.newCall(request).execute();
```

```javascript
var settings = {
  "url": "https://open-api.123pan.com/upload/v2/file/domain",
  "method": "GET",
  "timeout": 0,
  "headers": {
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5c...(过长省略)",
    "Platform": "open_platform"
  },
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
```

```javascript
const axios = require('axios');

let config = {
  method: 'get',
  maxBodyLength: Infinity,
  url: 'https://open-api.123pan.com/upload/v2/file/domain',
  headers: { 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5c...(过长省略)', 
    'Platform': 'open_platform'
  }
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});

```

```python
import http.client

conn = http.client.HTTPSConnection("open-api.123pan.com")
payload = ''
headers = {
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5c...(过长省略)',
  'Platform': 'open_platform'
}
conn.request("GET", "/upload/v2/file/domain", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

**响应示例**

```json
{
	"code": 0,
	"message": "ok",
	"data": [
		"https://openapi-upload.123pan.com"
	],
	"x-traceID": ""
}
```



> 更新: 2025-06-23 09:13:56  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/agn8lolktbqie7p9>