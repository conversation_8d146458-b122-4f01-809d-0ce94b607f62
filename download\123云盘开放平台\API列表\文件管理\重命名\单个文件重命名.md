# 单个文件重命名

API：PUT 域名 + /api/v1/file/name

## <font style="color:rgb(38, 38, 38);">Header 参数</font>
| **<font style="color:rgb(38, 38, 38);">名称</font>** | **<font style="color:rgb(38, 38, 38);">类型</font>** | **<font style="color:rgb(38, 38, 38);">是否必填</font>** | **<font style="color:rgb(38, 38, 38);">说明</font>** |
| :---: | :---: | :---: | :---: |
| <font style="color:rgb(38, 38, 38);">Authorization</font> | <font style="color:rgb(38, 38, 38);">string</font> | <font style="color:rgb(0, 0, 0);">必填</font><font style="color:rgb(38, 38, 38);"></font> | <font style="color:rgb(38, 38, 38);">鉴权access_token</font> |
| <font style="color:rgb(38, 38, 38);">Platform</font> | <font style="color:rgb(38, 38, 38);">string</font> | <font style="color:rgb(38, 38, 38);">必填</font> | <font style="color:rgb(38, 38, 38);">固定为:open_platform</font> |


## Body参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| fileId | number | 是 | 文件id |
| fileName | string | 是 | 文件名 |


## 示例
请求示例

```shell
curl --location --request PUT 'https://open-api.123pan.com/api/v1/file/name' \
--header 'Content-Type: application/json' \
--header 'Platform: open_platform' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)' \
--data '{
    "fileId": 14749954,
    "fileName": "测试修改文件名称.exe"
}'
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
.build();
MediaType mediaType = MediaType.parse("application/json");
RequestBody body = RequestBody.create(mediaType, "{\n    \"fileId\": 14749954,\n    \"fileName\": \"测试修改文件名称.exe\"\n}");
Request request = new Request.Builder()
.url("https://open-api.123pan.com/api/v1/file/name")
.method("PUT", body)
.addHeader("Content-Type", "application/json")
.addHeader("Platform", "open_platform")
.addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)")
.build();
Response response = client.newCall(request).execute();
```

```javascript
var settings = {
  "url": "https://open-api.123pan.com/api/v1/file/name",
  "method": "PUT",
  "timeout": 0,
  "headers": {
    "Content-Type": "application/json",
    "Platform": "open_platform",
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)"
  },
  "data": JSON.stringify({
    "fileId": 14749954,
    "fileName": "测试修改文件名称.exe"
  }),
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
```

```javascript
const axios = require('axios');
let data = JSON.stringify({
  "fileId": 14749954,
  "fileName": "测试修改文件名称.exe"
});

let config = {
  method: 'put',
  maxBodyLength: Infinity,
  url: 'https://open-api.123pan.com/api/v1/file/name',
  headers: { 
    'Content-Type': 'application/json', 
    'Platform': 'open_platform', 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
  },
  data : data
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});

```

```python
import http.client
import json

conn = http.client.HTTPSConnection("open-api.123pan.com")
payload = json.dumps({
    "fileId": 14749954,
    "fileName": "测试修改文件名称.exe"
})
headers = {
    'Content-Type': 'application/json',
    'Platform': 'open_platform',
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
}
conn.request("PUT", "/api/v1/file/name", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

响应示例

```json
{
  "code": 0,
  "message": "ok",
  "data": null,
  "x-traceID": "331020ae-a650-4a50-b133-7dcfa35b26a1_kong-db-5898fdd8c6-wnv6h"
}
```



> 更新: 2025-03-17 19:17:19  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/ha6mfe9tteht5skc>