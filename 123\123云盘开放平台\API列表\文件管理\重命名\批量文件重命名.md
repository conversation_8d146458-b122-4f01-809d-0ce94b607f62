# 批量文件重命名

<font style="color:rgb(38, 38, 38);">API： POST 域名 + /api/v1/file/rename  
说明：批量重命名文件，最多支持同时30个文件重命名</font>

## <font style="color:rgb(38, 38, 38);">Header 参数</font>
| **<font style="color:rgb(38, 38, 38);">名称</font>** | **<font style="color:rgb(38, 38, 38);">类型</font>** | **<font style="color:rgb(38, 38, 38);">是否必填</font>** | **<font style="color:rgb(38, 38, 38);">说明</font>** |
| :---: | :---: | :---: | :---: |
| <font style="color:rgb(38, 38, 38);">Authorization</font> | <font style="color:rgb(38, 38, 38);">string</font> | <font style="color:rgb(0, 0, 0);">必填</font><font style="color:rgb(38, 38, 38);"></font> | <font style="color:rgb(38, 38, 38);">鉴权access_token</font> |
| <font style="color:rgb(38, 38, 38);">Platform</font> | <font style="color:rgb(38, 38, 38);">string</font> | <font style="color:rgb(38, 38, 38);">必填</font> | <font style="color:rgb(38, 38, 38);">固定为:open_platform</font> |


## <font style="color:rgb(38, 38, 38);">Body 参数</font>
| **<font style="color:rgb(38, 38, 38);">名称</font>**<font style="color:rgb(38, 38, 38);"></font> | **<font style="color:rgb(38, 38, 38);">类型</font>**<font style="color:rgb(38, 38, 38);"></font> | **<font style="color:rgb(38, 38, 38);">是否必填</font>**<font style="color:rgb(38, 38, 38);"></font> | **<font style="color:rgb(38, 38, 38);">说明</font>**<font style="color:rgb(38, 38, 38);"></font> |
| :---: | :---: | :---: | :---: |
| <font style="color:rgb(38, 38, 38);">renameList</font> | <font style="color:rgb(38, 38, 38);">array</font> | <font style="color:rgb(38, 38, 38);">必填</font> | <font style="color:rgb(38, 38, 38);">数组,每个成员的格式为 文件ID|新的文件名</font> |


## <font style="color:rgb(38, 38, 38);">示例</font>
**<font style="color:rgb(38, 38, 38);">请求示例</font>**

```shell
curl --location 'https://open-api.123pan.com/api/v1/file/rename' \
--header 'Content-Type: application/json' \
--header 'Platform: open_platform' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ...(过长省略)' \
--data '{
    "renameList": [
        "14705301|测试文件重命名",
        "14705306|测试文件重命名.mp4"
    ]
}'
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
.build();
MediaType mediaType = MediaType.parse("application/json");
RequestBody body = RequestBody.create(mediaType, "{\n    \"renameList\": [\n        \"14705301|测试文件重命名\",\n        \"14705306|测试文件重命名.mp4\"\n    ]\n}");
Request request = new Request.Builder()
.url("https://open-api.123pan.com/api/v1/file/rename")
.method("POST", body)
.addHeader("Content-Type", "application/json")
.addHeader("Platform", "open_platform")
.addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ...(过长省略)")
.build();
Response response = client.newCall(request).execute();
```

```javascript
var settings = {
  "url": "https://open-api.123pan.com/api/v1/file/rename",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "Content-Type": "application/json",
    "Platform": "open_platform",
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ...(过长省略)"
  },
  "data": JSON.stringify({
    "renameList": [
      "14705301|测试文件重命名",
      "14705306|测试文件重命名.mp4"
    ]
  }),
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
```

```javascript
const axios = require('axios');
let data = JSON.stringify({
  "renameList": [
    "14705301|测试文件重命名",
    "14705306|测试文件重命名.mp4"
  ]
});

let config = {
  method: 'post',
  maxBodyLength: Infinity,
  url: 'https://open-api.123pan.com/api/v1/file/rename',
  headers: { 
    'Content-Type': 'application/json', 
    'Platform': 'open_platform', 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ...(过长省略)'
  },
  data : data
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});

```

```python
import http.client
import json

conn = http.client.HTTPSConnection("open-api.123pan.com")
payload = json.dumps({
    "renameList": [
        "14705301|测试文件重命名",
        "14705306|测试文件重命名.mp4"
    ]
})
headers = {
    'Content-Type': 'application/json',
    'Platform': 'open_platform',
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ...(过长省略)'
}
conn.request("POST", "/api/v1/file/rename", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

**<font style="color:rgb(38, 38, 38);">响应示例</font>**

```json
{
  "code": 0,
  "message": "ok",
  "data": null,
  "x-traceID": "b2304316-6f18-4478-9e05-1f1654592beb_kong-db-5898fdd8c6-d258b"
}
```



> 更新: 2025-03-17 19:17:07  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/imhguepnr727aquk>