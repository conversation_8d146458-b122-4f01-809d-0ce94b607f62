from flask import Blueprint, jsonify, request
from sqlalchemy import desc, or_
from models import db, Play, Category, PlayLink

api_bp = Blueprint('api', __name__)

def create_response(success=True, data=None, error=None, pagination=None):
    """创建统一的API响应格式"""
    response = {'success': success}
    if data is not None:
        response['data'] = data
    if error is not None:
        response['error'] = error
    if pagination is not None:
        response['pagination'] = pagination
    return jsonify(response)

def serialize_play(play):
    """序列化短剧对象"""
    pan123_link = play.pan123_link
    quark_link = play.quark_link
    
    return {
        'id': play.id,
        'title': play.title,
        'description': play.description,
        'poster_url': play.poster_url,
        'primary_category': {
            'id': play.primary_category.id,
            'name': play.primary_category.name,
            'slug': play.primary_category.slug
        } if play.primary_category else None,
        'categories': [{
            'id': category.id,
            'name': category.name,
            'slug': category.slug
        } for category in play.categories],
        'pan123': {
            'link': pan123_link.link if pan123_link else '',
            'code': pan123_link.code if pan123_link else ''
        },
        'quark': {
            'link': quark_link.link if quark_link else '',
            'code': quark_link.code if quark_link else ''
        },
        'views': play.views,
        'episodes': play.episodes,
        'year': play.year,
        'created_at': play.created_at.isoformat(),
        'updated_at': play.updated_at.isoformat()
    }

@api_bp.route('/plays')
def get_plays():
    """获取短剧列表API"""
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        search = request.args.get('search', '').strip()
        category_slug = request.args.get('category', '')
        sort_by = request.args.get('sort', 'latest')
        
        # 构建查询
        query = Play.query.filter(Play.status == 'active')
        
        # 搜索过滤
        if search:
            query = query.filter(
                or_(
                    Play.title.contains(search),
                    Play.description.contains(search)
                )
            )
        
        # 分类过滤
        if category_slug and category_slug != 'all':
            category = Category.query.filter_by(slug=category_slug).first()
            if category:
                query = query.filter(Play.categories.contains(category))
        
        # 排序
        if sort_by == 'popular':
            query = query.order_by(desc(Play.views))
        else:  # latest
            query = query.order_by(desc(Play.updated_at))
        
        # 分页
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        # 序列化数据
        plays_data = [serialize_play(play) for play in pagination.items]
        
        pagination_data = {
            'page': pagination.page,
            'per_page': pagination.per_page,
            'total': pagination.total,
            'pages': pagination.pages,
            'has_prev': pagination.has_prev,
            'has_next': pagination.has_next
        }
        
        return create_response(
            data=plays_data,
            pagination=pagination_data
        )
        
    except Exception as e:
        return create_response(
            success=False,
            error={'code': 'INTERNAL_ERROR', 'message': str(e)}
        ), 500

@api_bp.route('/plays/<int:play_id>')
def get_play(play_id):
    """获取单个短剧详情API"""
    try:
        play = Play.query.get_or_404(play_id)
        return create_response(data=serialize_play(play))
    except Exception as e:
        return create_response(
            success=False,
            error={'code': 'PLAY_NOT_FOUND', 'message': 'Play not found'}
        ), 404

@api_bp.route('/categories')
def get_categories():
    """获取分类列表API"""
    try:
        categories = Category.query.order_by(Category.order_index).all()
        categories_data = []
        
        for category in categories:
            categories_data.append({
                'id': category.id,
                'name': category.name,
                'slug': category.slug,
                'description': category.description,
                'play_count': category.play_count,
                'order_index': category.order_index
            })
        
        return create_response(data=categories_data)
        
    except Exception as e:
        return create_response(
            success=False,
            error={'code': 'INTERNAL_ERROR', 'message': str(e)}
        ), 500

@api_bp.route('/search/suggestions')
def get_search_suggestions():
    """获取搜索建议API - 轻量级版本用于实时搜索"""
    try:
        query_text = request.args.get('q', '').strip()
        if not query_text or len(query_text) < 2:
            return create_response(data=[])
        
        limit = min(request.args.get('limit', 5, type=int), 10)
        
        # 构建搜索查询 - 只搜索标题以提高性能
        plays = Play.query.filter(
            Play.status == 'active',
            Play.title.contains(query_text)
        ).order_by(desc(Play.views)).limit(limit).all()
        
        # 轻量级序列化 - 只返回必需字段
        suggestions_data = []
        for play in plays:
            suggestions_data.append({
                'id': play.id,
                'title': play.title,
                'poster_url': play.poster_url,
                'category': {
                    'name': play.primary_category.name if play.primary_category else '未分类',
                    'slug': play.primary_category.slug if play.primary_category else 'uncategorized'
                },
                'views': play.views
            })
        
        return create_response(data=suggestions_data)
        
    except Exception as e:
        return create_response(
            success=False,
            error={'code': 'INTERNAL_ERROR', 'message': str(e)}
        ), 500

@api_bp.route('/search')
def search_plays():
    """搜索短剧API"""
    try:
        query_text = request.args.get('q', '').strip()
        if not query_text:
            return create_response(
                success=False,
                error={'code': 'INVALID_QUERY', 'message': 'Query parameter is required'}
            ), 400
        
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        limit = request.args.get('limit', type=int)  # 用于搜索建议
        category_slug = request.args.get('category', '')
        sort_by = request.args.get('sort', 'latest')
        
        # 构建搜索查询
        query = Play.query.filter(
            Play.status == 'active',
            or_(
                Play.title.contains(query_text),
                Play.description.contains(query_text)
            )
        )
        
        # 分类过滤
        if category_slug and category_slug != 'all':
            category = Category.query.filter_by(slug=category_slug).first()
            if category:
                query = query.filter(Play.categories.contains(category))
        
        # 排序
        if sort_by == 'popular':
            query = query.order_by(desc(Play.views))
        else:  # latest
            query = query.order_by(desc(Play.updated_at))
        
        # 如果有limit参数，直接返回指定数量的结果（用于搜索建议）
        if limit:
            plays = query.limit(limit).all()
            plays_data = [serialize_play(play) for play in plays]
            return create_response(data=plays_data)
        
        # 分页
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        # 序列化数据
        plays_data = [serialize_play(play) for play in pagination.items]
        
        pagination_data = {
            'page': pagination.page,
            'per_page': pagination.per_page,
            'total': pagination.total,
            'pages': pagination.pages,
            'has_prev': pagination.has_prev,
            'has_next': pagination.has_next
        }
        
        return create_response(
            data=plays_data,
            pagination=pagination_data
        )
        
    except Exception as e:
        return create_response(
            success=False,
            error={'code': 'INTERNAL_ERROR', 'message': str(e)}
        ), 500

@api_bp.route('/plays/popular')
def get_popular_plays():
    """获取热门短剧API"""
    try:
        limit = min(request.args.get('limit', 10, type=int), 50)
        category_slug = request.args.get('category', '')
        
        query = Play.query.filter(Play.status == 'active')
        
        # 分类过滤
        if category_slug and category_slug != 'all':
            category = Category.query.filter_by(slug=category_slug).first()
            if category:
                query = query.filter(Play.categories.contains(category))
        
        plays = query.order_by(desc(Play.views)).limit(limit).all()
        plays_data = [serialize_play(play) for play in plays]
        
        return create_response(data=plays_data)
        
    except Exception as e:
        return create_response(
            success=False,
            error={'code': 'INTERNAL_ERROR', 'message': str(e)}
        ), 500

@api_bp.route('/plays/latest')
def get_latest_plays():
    """获取最新短剧API"""
    try:
        limit = min(request.args.get('limit', 10, type=int), 50)
        category_slug = request.args.get('category', '')
        
        query = Play.query.filter(Play.status == 'active')
        
        # 分类过滤
        if category_slug and category_slug != 'all':
            category = Category.query.filter_by(slug=category_slug).first()
            if category:
                query = query.filter(Play.categories.contains(category))
        
        plays = query.order_by(desc(Play.updated_at)).limit(limit).all()
        plays_data = [serialize_play(play) for play in plays]
        
        return create_response(data=plays_data)
        
    except Exception as e:
        return create_response(
            success=False,
            error={'code': 'INTERNAL_ERROR', 'message': str(e)}
        ), 500

@api_bp.route('/statistics')
def get_statistics():
    """获取统计信息API"""
    try:
        # 基础统计
        total_plays = Play.query.filter(Play.status == 'active').count()
        total_views = db.session.query(db.func.sum(Play.views)).scalar() or 0
        
        # 分类统计
        category_counts = {}
        for category in Category.query.all():
            if category.slug == 'all':
                continue
            count = Play.query.filter(
                Play.categories.contains(category),
                Play.status == 'active'
            ).count()
            category_counts[category.slug] = count
        
        statistics = {
            'total_plays': total_plays,
            'total_views': total_views,
            'category_counts': category_counts,
            'update_frequency': {
                'today': 0,  # 可以实现具体的统计逻辑
                'this_week': 0,
                'this_month': 0
            }
        }
        
        return create_response(data=statistics)
        
    except Exception as e:
        return create_response(
            success=False,
            error={'code': 'INTERNAL_ERROR', 'message': str(e)}
        ), 500

@api_bp.route('/health')
def health_check():
    """健康检查API"""
    return create_response(data={
        'status': 'healthy',
        'timestamp': db.func.now()
    })