"""
短剧数据导入和网盘分享主控制器
整合爬虫、数据库导入、QB下载监控、网盘上传的完整流程
"""

import os
import sys
import yaml
import time
import logging
import threading
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

# 添加Flask项目路径
flask_path = Path(__file__).parent.parent / "flask"
sys.path.insert(0, str(flask_path))

from cloud_storage_manager import CloudStorageManager


@dataclass
class DramaImportTask:
    """短剧导入任务"""
    id: str
    title: str
    status: str  # pending, downloading, downloaded, uploading, completed, failed
    torrent_hash: Optional[str] = None
    file_path: Optional[str] = None
    share_info: Optional[Dict[str, Any]] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    error_message: Optional[str] = None


class DramaImportManager:
    """短剧导入管理器"""
    
    def __init__(self, config_path: str = "config.yml"):
        self.config_path = config_path
        self.config = self._load_config()
        self.logger = self._setup_logging()
        
        # 初始化组件
        self.cloud_manager = CloudStorageManager(config_path)
        self.tasks = {}  # 任务字典
        self.running = False
        self.monitor_thread = None
        
        # QB配置
        qb_config = self.config.get("qbittorrent", {})
        self.qb_host = qb_config.get("host", "***********")
        self.qb_port = qb_config.get("port", 49850)
        self.qb_user = qb_config.get("username", "admin")
        self.qb_pass = qb_config.get("password", "507877550@lihao")
        self.qb_download_path = qb_config.get("download_path", "/downloads")
        
        # 导入配置
        import_config = self.config.get("import", {})
        self.auto_upload = import_config.get("auto_upload", True)
        self.preferred_storage = import_config.get("preferred_storage", "quark")
        self.monitor_interval = import_config.get("monitor_interval", 30)  # 秒
        
        self.logger.info("短剧导入管理器初始化完成")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return {}
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger(__name__)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def crawl_and_import_data(self, limit: int = 10) -> List[Dict[str, Any]]:
        """爬取数据并导入数据库"""
        self.logger.info(f"开始爬取和导入数据，限制: {limit}")
        
        try:
            # 导入爬虫模块
            from agsvpt_crawler import crawl_and_import
            
            # 执行爬取和导入
            imported_count, skipped_count = crawl_and_import(limit)
            
            result = {
                "total_crawled": limit,
                "imported": imported_count,
                "skipped": skipped_count,
                "status": "success"
            }
            
            self.logger.info(f"数据导入完成: {result}")
            return result
            
        except Exception as e:
            self.logger.error(f"数据爬取和导入失败: {e}")
            return {
                "status": "failed",
                "error": str(e)
            }
    
    def create_import_task(self, title: str, torrent_hash: Optional[str] = None) -> str:
        """创建导入任务"""
        task_id = f"task_{int(time.time())}_{len(self.tasks)}"
        
        task = DramaImportTask(
            id=task_id,
            title=title,
            status="pending",
            torrent_hash=torrent_hash,
            created_at=datetime.now()
        )
        
        self.tasks[task_id] = task
        self.logger.info(f"创建导入任务: {task_id} - {title}")
        
        return task_id
    
    def update_task_status(self, task_id: str, status: str, **kwargs):
        """更新任务状态"""
        if task_id not in self.tasks:
            return
        
        task = self.tasks[task_id]
        task.status = status
        task.updated_at = datetime.now()
        
        # 更新其他字段
        for key, value in kwargs.items():
            if hasattr(task, key):
                setattr(task, key, value)
        
        self.logger.info(f"任务状态更新: {task_id} -> {status}")
    
    def get_qb_torrents(self) -> List[Dict[str, Any]]:
        """获取QB种子列表"""
        try:
            import requests
            
            # 登录QB
            login_url = f"http://{self.qb_host}:{self.qb_port}/api/v2/auth/login"
            login_data = {"username": self.qb_user, "password": self.qb_pass}
            
            session = requests.Session()
            response = session.post(login_url, data=login_data)
            response.raise_for_status()
            
            # 获取种子列表
            torrents_url = f"http://{self.qb_host}:{self.qb_port}/api/v2/torrents/info"
            response = session.get(torrents_url)
            response.raise_for_status()
            
            torrents = response.json()
            self.logger.info(f"获取到 {len(torrents)} 个种子")
            
            return torrents
            
        except Exception as e:
            self.logger.error(f"获取QB种子列表失败: {e}")
            return []
    
    def monitor_downloads(self):
        """监控下载完成的文件"""
        self.logger.info("开始监控下载")
        
        while self.running:
            try:
                torrents = self.get_qb_torrents()
                
                for torrent in torrents:
                    if torrent["state"] == "uploading" or torrent["state"] == "stalledUP":
                        # 下载完成的种子
                        torrent_hash = torrent["hash"]
                        torrent_name = torrent["name"]
                        
                        # 查找对应的任务
                        task = self._find_task_by_torrent(torrent_hash)
                        if task and task.status == "downloading":
                            # 更新任务状态
                            file_path = os.path.join(self.qb_download_path, torrent_name)
                            self.update_task_status(
                                task.id, 
                                "downloaded", 
                                file_path=file_path
                            )
                            
                            # 如果启用自动上传，则开始上传
                            if self.auto_upload:
                                self._start_upload_task(task.id)
                
                time.sleep(self.monitor_interval)
                
            except Exception as e:
                self.logger.error(f"监控下载时出错: {e}")
                time.sleep(self.monitor_interval)
    
    def _find_task_by_torrent(self, torrent_hash: str) -> Optional[DramaImportTask]:
        """根据种子hash查找任务"""
        for task in self.tasks.values():
            if task.torrent_hash == torrent_hash:
                return task
        return None
    
    def _start_upload_task(self, task_id: str):
        """开始上传任务"""
        if task_id not in self.tasks:
            return
        
        task = self.tasks[task_id]
        if not task.file_path or not os.path.exists(task.file_path):
            self.update_task_status(task_id, "failed", error_message="文件不存在")
            return
        
        self.update_task_status(task_id, "uploading")
        
        try:
            # 上传到网盘
            result = self.cloud_manager.upload_with_fallback(
                task.file_path,
                preferred_storage=self.preferred_storage,
                share_name=f"短剧: {task.title}"
            )
            
            # 更新任务状态
            self.update_task_status(
                task_id, 
                "completed", 
                share_info=result
            )
            
            # 更新数据库中的分享链接
            self._update_database_share_link(task.title, result)
            
        except Exception as e:
            self.logger.error(f"上传任务失败 {task_id}: {e}")
            self.update_task_status(
                task_id, 
                "failed", 
                error_message=str(e)
            )
    
    def _update_database_share_link(self, title: str, share_info: Dict[str, Any]):
        """更新数据库中的分享链接"""
        try:
            from models import db, Play, PlayLink
            from app import create_app
            
            app = create_app()
            with app.app_context():
                # 查找短剧
                play = Play.query.filter_by(title=title).first()
                if not play:
                    self.logger.warning(f"未找到短剧: {title}")
                    return
                
                # 创建或更新分享链接
                play_link = PlayLink.query.filter_by(play_id=play.id).first()
                if not play_link:
                    play_link = PlayLink(play_id=play.id)
                    db.session.add(play_link)
                
                play_link.link_url = share_info["share_url"]
                play_link.link_password = share_info.get("extract_code", "")
                play_link.platform = share_info["platform"]
                play_link.updated_at = datetime.utcnow()
                
                db.session.commit()
                self.logger.info(f"数据库分享链接更新成功: {title}")
                
        except Exception as e:
            self.logger.error(f"更新数据库分享链接失败: {e}")
    
    def start_monitoring(self):
        """启动监控"""
        if self.running:
            return
        
        self.running = True
        self.monitor_thread = threading.Thread(target=self.monitor_downloads)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
        self.logger.info("监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.running = False
        if self.monitor_thread:
            self.monitor_thread.join()
        
        self.logger.info("监控已停止")
    
    def get_task_status(self, task_id: Optional[str] = None) -> Dict[str, Any]:
        """获取任务状态"""
        if task_id:
            if task_id in self.tasks:
                task = self.tasks[task_id]
                return {
                    "id": task.id,
                    "title": task.title,
                    "status": task.status,
                    "created_at": task.created_at.isoformat() if task.created_at else None,
                    "updated_at": task.updated_at.isoformat() if task.updated_at else None,
                    "share_info": task.share_info,
                    "error_message": task.error_message
                }
            else:
                return {"error": "任务不存在"}
        else:
            # 返回所有任务状态
            return {
                "total_tasks": len(self.tasks),
                "tasks": [
                    {
                        "id": task.id,
                        "title": task.title,
                        "status": task.status,
                        "created_at": task.created_at.isoformat() if task.created_at else None
                    }
                    for task in self.tasks.values()
                ]
            }
    
    def run_complete_import_process(self, crawl_limit: int = 10) -> Dict[str, Any]:
        """运行完整的导入流程"""
        self.logger.info("开始完整的导入流程")
        
        # 1. 爬取和导入数据
        crawl_result = self.crawl_and_import_data(crawl_limit)
        if crawl_result["status"] != "success":
            return crawl_result
        
        # 2. 启动监控
        self.start_monitoring()
        
        return {
            "status": "success",
            "message": "导入流程已启动，监控已开始",
            "crawl_result": crawl_result,
            "available_storages": self.cloud_manager.get_available_storages()
        }


def main():
    """主函数"""
    manager = DramaImportManager()
    
    # 运行完整导入流程
    result = manager.run_complete_import_process(10)
    print(f"导入结果: {result}")
    
    # 保持运行以监控下载
    try:
        while True:
            time.sleep(60)
            status = manager.get_task_status()
            print(f"任务状态: {status}")
    except KeyboardInterrupt:
        print("停止监控")
        manager.stop_monitoring()


if __name__ == "__main__":
    main()
