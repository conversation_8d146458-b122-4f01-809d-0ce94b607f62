import os
from datetime import timedelta

class Config:
    """生产环境配置"""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'prod-secret-key-change-in-production'
    
    # 生产环境MariaDB数据库
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'mysql+pymysql://sql23721_duanji:507877550%40lihao@160.30.208.2/sql23721_duanji?charset=utf8mb4'
    
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # 分页配置
    PLAYS_PER_PAGE = 20
    
    # 上传配置
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    UPLOAD_FOLDER = os.path.join(os.path.dirname(__file__), 'static', 'uploads')
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
    
    # 生产环境设置
    DEBUG = False
    TESTING = False

# 简化配置字典，只保留生产环境
config = {
    'default': Config,
    'production': Config
}