"use client"

import { Copy } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"

export function CopyButton({ textToCopy, platformName }: { textToCopy: string; platformName: string }) {
  const { toast } = useToast()

  const handleCopy = () => {
    navigator.clipboard
      .writeText(textToCopy)
      .then(() => {
        toast({
          title: "复制成功",
          description: `${platformName} 分享链接已复制到剪贴板。`,
        })
      })
      .catch((err) => {
        console.error("Failed to copy: ", err)
        toast({
          title: "复制失败",
          description: "无法访问剪贴板。",
          variant: "destructive",
        })
      })
  }

  return (
    <Button variant="ghost" size="icon" onClick={handleCopy}>
      <Copy className="h-4 w-4" />
      <span className="sr-only">复制 {platformName} 链接</span>
    </Button>
  )
}
