# 视频转码操作

API： POST 域名 + /api/v1/transcode/video

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#000000;">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## Body 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| fileId | number | 必填 | 文件Id |
| codecName | string | 必填 | 编码方式 |
| videoTime | number | 必填 | 视频时长，单位：秒 |
| <font style="color:#000000;">resolutions</font> | <font style="color:#000000;">string</font> | <font style="color:#000000;">必填</font> | <font style="color:#000000;">要转码的分辨率，多个之间以逗号分割，如："2160P,1080P,720P",注意：p是大写，如果之前已经转码过别的分辨率，那就无需再传</font> |


### body参数示例
```json
{
  "fileId": 2875008,
  "codecName": "H.264",
  "videoTime": 51,
  "resolutions": "2160P,1080P,720P"
}
```

## 返回数据
文字提示：720P&1080P已成功开始转码，请在转码结果中查询

### **返回示例**
```json
{
    "code": 0,
    "message": "ok",
    "data": "2160P&1080P&720P已成功开始转码，请在转码结果中查询",
    "x-traceID": ""
}
```



> 更新: 2025-03-17 19:16:39  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/xy42nv2x8wav9n5l>