# 创建离线迁移任务

API： POST 域名 + /api/v1/oss/offline/download

说明：

1. 离线下载任务仅支持http/https任务创建，提交url资源后将在后台自动下载图片资源并上传到对应图床目录
2. url 支持的图片格式：png, gif, jpeg, tiff, webp,jpg,tif,svg,bmp，图片大小限制：100M
3. 如果图床目录下存在相同 etag、size 的图片将会覆盖原图片

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#000000;">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## Body 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | --- |
| url | string | 必填 | 下载资源地址(http/https) |
| fileName | string | 非必填 |  自定义文件名称 （需携带图片格式，支持格式：png, gif, jpeg, tiff, webp,jpg,tif,svg,bmp）   |
| businessDirID | string | 非必填 | 选择下载到指定目录ID。 示例:10023<br/>注:不支持下载到根目录,默认下载到名为"来自:离线下载"的目录中 |
| callBackUrl | string | 非必填 | 回调地址,当文件下载成功或者失败,均会通过回调地址通知。回调内容如下      url: 下载资源地址<br/>status: 0 成功，1 失败<br/>fileReason：失败原因<br/>fileID:成功后,该文件在云盘上的ID      请求类型：POST   {<br/>	"url": "[http://dc.com/resource.jpg",](http://dc.com/resource.jpg",)<br/>	"status": 0, <br/>	"failReason": "",<br/>        "fileID":100<br/>} |
| <font style="color:#000000;">type</font> | <font style="color:#000000;">number</font> | <font style="color:#000000;">必填</font> | <font style="color:#000000;">业务类型，固定为 1</font> |


## 返回数据
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | --- |
| taskID | number | 必填 | 离线下载任务ID,可通过该ID,调用查询任务进度接口获取下载进度 |


## 示例
请求示例

```shell
curl --location 'https://open-api.123pan.com/api/v1/oss/offline/download' \
--header 'Content-Type: application/json' \
--header 'Platform: open_platform' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)' \
--data '{
    "url": "https://vip.123pan.cn/1815309870/%E6%B5%8B%E8%AF%95%E7%9B%B4%E9%93%BE%E6%96%87%E4%BB%B6%E5%A4%B9/6ac54ccb31a09a5c1223677ba07c283f.jpeg",
    "type": 1
}'
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
.build();
MediaType mediaType = MediaType.parse("application/json");
RequestBody body = RequestBody.create(mediaType, "{\n    \"url\": \"https://vip.123pan.cn/1815309870/%E6%B5%8B%E8%AF%95%E7%9B%B4%E9%93%BE%E6%96%87%E4%BB%B6%E5%A4%B9/6ac54ccb31a09a5c1223677ba07c283f.jpeg\",\n    \"type\": 1\n}");
Request request = new Request.Builder()
.url("https://open-api.123pan.com/api/v1/oss/offline/download")
.method("POST", body)
.addHeader("Content-Type", "application/json")
.addHeader("Platform", "open_platform")
.addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)")
.build();
Response response = client.newCall(request).execute();
```

```javascript
var settings = {
  "url": "https://open-api.123pan.com/api/v1/oss/offline/download",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "Content-Type": "application/json",
    "Platform": "open_platform",
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)"
  },
  "data": JSON.stringify({
    "url": "https://vip.123pan.cn/1815309870/%E6%B5%8B%E8%AF%95%E7%9B%B4%E9%93%BE%E6%96%87%E4%BB%B6%E5%A4%B9/6ac54ccb31a09a5c1223677ba07c283f.jpeg",
    "type": 1
  }),
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
```

```javascript
const axios = require('axios');
let data = JSON.stringify({
  "url": "https://vip.123pan.cn/1815309870/%E6%B5%8B%E8%AF%95%E7%9B%B4%E9%93%BE%E6%96%87%E4%BB%B6%E5%A4%B9/6ac54ccb31a09a5c1223677ba07c283f.jpeg",
  "type": 1
});

let config = {
  method: 'post',
  maxBodyLength: Infinity,
  url: 'https://open-api.123pan.com/api/v1/oss/offline/download',
  headers: { 
    'Content-Type': 'application/json', 
    'Platform': 'open_platform', 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
  },
  data : data
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});

```

```python
import http.client
import json

conn = http.client.HTTPSConnection("open-api.123pan.com")
payload = json.dumps({
  "url": "https://vip.123pan.cn/1815309870/%E6%B5%8B%E8%AF%95%E7%9B%B4%E9%93%BE%E6%96%87%E4%BB%B6%E5%A4%B9/6ac54ccb31a09a5c1223677ba07c283f.jpeg",
  "type": 1
})
headers = {
  'Content-Type': 'application/json',
  'Platform': 'open_platform',
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
}
conn.request("POST", "/api/v1/oss/offline/download", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

响应示例

```json
{
    "code": 0,
    "message": "ok",
    "data": {
        "taskID": 403316
    },
    "x-traceID": "f65dfd51-ad28-47e6-ad21-10da7f46d98a_kong-db-5898fdd8c6-t5pvc"
}
```



> 更新: 2025-03-17 19:17:00  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/ctigc3a08lqzsfnq>