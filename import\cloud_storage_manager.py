"""
统一网盘管理器
整合123网盘和夸克网盘的上传和分享功能
"""

import os
import sys
import yaml
import logging
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
from abc import ABC, abstractmethod

# 添加123和quark模块路径
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.append(str(project_root / "123"))
sys.path.append(str(project_root / "quark"))

try:
    from upload_share import YunPan123Client
except ImportError:
    YunPan123Client = None
    print("警告: 无法导入123网盘模块")

try:
    from quark import QuarkPan
except ImportError:
    QuarkPan = None
    print("警告: 无法导入夸克网盘模块")


class CloudStorageInterface(ABC):
    """网盘存储接口"""
    
    @abstractmethod
    def upload_and_share(self, file_path: str, **kwargs) -> Dict[str, Any]:
        """上传文件并创建分享链接"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """检查网盘是否可用"""
        pass


class YunPan123Storage(CloudStorageInterface):
    """123网盘存储实现"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.client = None
        self._init_client()
    
    def _init_client(self):
        """初始化123网盘客户端"""
        if not YunPan123Client:
            return

        try:
            config_path = self.config.get("config_path", "123/config.json")

            # 处理相对路径
            if not os.path.isabs(config_path):
                # 从import目录的角度解析路径
                current_dir = Path(__file__).parent
                config_path = str(current_dir.parent / config_path.lstrip("../"))

            if not os.path.exists(config_path):
                print(f"123网盘配置文件不存在: {config_path}")
                return

            self.client = YunPan123Client(config_path)
            print("123网盘客户端初始化成功")
        except Exception as e:
            print(f"123网盘客户端初始化失败: {e}")
            self.client = None
    
    def upload_and_share(self, file_path: str, **kwargs) -> Dict[str, Any]:
        """上传文件并创建分享链接"""
        if not self.client:
            raise Exception("123网盘客户端未初始化")
        
        directory_path = kwargs.get("directory_path", "/短剧资源")
        share_name = kwargs.get("share_name") or f"分享: {os.path.basename(file_path)}"
        expire_days = kwargs.get("expire_days", 0)  # 0表示永久
        
        result = self.client.upload_and_share(
            file_path=file_path,
            directory_path=directory_path,
            share_name=share_name,
            expire_days=expire_days
        )
        
        return {
            "platform": "123网盘",
            "share_url": result["shareUrl"],
            "extract_code": result.get("extractCode", ""),
            "file_name": result["fileName"],
            "expire_days": expire_days,
            "status": "success"
        }
    
    def is_available(self) -> bool:
        """检查123网盘是否可用"""
        return self.client is not None


class QuarkStorage(CloudStorageInterface):
    """夸克网盘存储实现"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.client = None
        self._init_client()
    
    def _init_client(self):
        """初始化夸克网盘客户端"""
        if not QuarkPan:
            return

        try:
            config_path = self.config.get("config_path", "quark/config.yml")

            # 处理相对路径
            if not os.path.isabs(config_path):
                # 从import目录的角度解析路径
                current_dir = Path(__file__).parent
                config_path = str(current_dir.parent / config_path.lstrip("../"))

            if not os.path.exists(config_path):
                print(f"夸克网盘配置文件不存在: {config_path}")
                return

            # 临时切换工作目录到quark目录，因为QuarkPan类使用相对路径读取cookie
            original_cwd = os.getcwd()
            quark_dir = Path(config_path).parent

            try:
                os.chdir(str(quark_dir))
                self.client = QuarkPan(config_path)
                print("夸克网盘客户端初始化成功")
            finally:
                os.chdir(original_cwd)

        except Exception as e:
            print(f"夸克网盘客户端初始化失败: {e}")
            self.client = None
    
    def upload_and_share(self, file_path: str, **kwargs) -> Dict[str, Any]:
        """上传文件并创建分享链接"""
        if not self.client:
            raise Exception("夸克网盘客户端未初始化")
        
        share_expire = kwargs.get("share_expire", 1)  # 1表示永久
        
        result = self.client.upload_file(file_path, share_expire)
        
        return {
            "platform": "夸克网盘",
            "share_url": result["share_url"],
            "extract_code": result.get("passwd", ""),
            "file_name": result["title"],
            "expire_type": share_expire,
            "status": result.get("status", "success")
        }
    
    def is_available(self) -> bool:
        """检查夸克网盘是否可用"""
        return self.client is not None


class CloudStorageManager:
    """统一网盘管理器"""
    
    def __init__(self, config_path: str = "config.yml"):
        self.config_path = config_path
        self.config = self._load_config()
        self.storages = {}
        self.logger = self._setup_logging()
        
        # 初始化网盘存储
        self._init_storages()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return {}
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger(__name__)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _init_storages(self):
        """初始化网盘存储"""
        cloud_config = self.config.get("cloud_storage", {})
        
        # 初始化123网盘
        if cloud_config.get("yunpan123", {}).get("enabled", False):
            yunpan123_config = cloud_config["yunpan123"]
            self.storages["123"] = YunPan123Storage(yunpan123_config)
        
        # 初始化夸克网盘
        if cloud_config.get("quark", {}).get("enabled", False):
            quark_config = cloud_config["quark"]
            self.storages["quark"] = QuarkStorage(quark_config)
        
        available_storages = [name for name, storage in self.storages.items() if storage.is_available()]
        self.logger.info(f"可用网盘: {available_storages}")
    
    def get_available_storages(self) -> List[str]:
        """获取可用的网盘列表"""
        return [name for name, storage in self.storages.items() if storage.is_available()]
    
    def upload_to_storage(self, file_path: str, storage_name: str, **kwargs) -> Dict[str, Any]:
        """上传文件到指定网盘"""
        if storage_name not in self.storages:
            raise ValueError(f"不支持的网盘: {storage_name}")
        
        storage = self.storages[storage_name]
        if not storage.is_available():
            raise Exception(f"{storage_name}网盘不可用")
        
        self.logger.info(f"开始上传文件到{storage_name}网盘: {file_path}")
        
        try:
            result = storage.upload_and_share(file_path, **kwargs)
            self.logger.info(f"文件上传成功: {result['share_url']}")
            return result
        except Exception as e:
            self.logger.error(f"文件上传失败: {e}")
            raise
    
    def upload_with_fallback(self, file_path: str, preferred_storage: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """上传文件，支持备用网盘"""
        available_storages = self.get_available_storages()
        
        if not available_storages:
            raise Exception("没有可用的网盘")
        
        # 确定上传顺序
        upload_order = []
        if preferred_storage and preferred_storage in available_storages:
            upload_order.append(preferred_storage)
            available_storages.remove(preferred_storage)
        upload_order.extend(available_storages)
        
        last_error = None
        for storage_name in upload_order:
            try:
                self.logger.info(f"尝试使用{storage_name}网盘上传")
                return self.upload_to_storage(file_path, storage_name, **kwargs)
            except Exception as e:
                last_error = e
                self.logger.warning(f"{storage_name}网盘上传失败: {e}")
                continue
        
        raise Exception(f"所有网盘上传都失败了，最后错误: {last_error}")
    
    def batch_upload(self, file_paths: List[str], **kwargs) -> List[Dict[str, Any]]:
        """批量上传文件"""
        results = []
        total_files = len(file_paths)
        
        self.logger.info(f"开始批量上传 {total_files} 个文件")
        
        for i, file_path in enumerate(file_paths, 1):
            try:
                self.logger.info(f"上传进度: {i}/{total_files} - {file_path}")
                result = self.upload_with_fallback(file_path, **kwargs)
                result["file_path"] = file_path
                result["upload_index"] = i
                results.append(result)
            except Exception as e:
                self.logger.error(f"文件上传失败 {file_path}: {e}")
                results.append({
                    "file_path": file_path,
                    "upload_index": i,
                    "status": "failed",
                    "error": str(e)
                })
        
        success_count = sum(1 for r in results if r.get("status") != "failed")
        self.logger.info(f"批量上传完成: {success_count}/{total_files} 个文件成功")
        
        return results


def main():
    """测试函数"""
    manager = CloudStorageManager()
    
    # 显示可用网盘
    available = manager.get_available_storages()
    print(f"可用网盘: {available}")
    
    # 测试上传（需要实际文件）
    # test_file = "test.txt"
    # if os.path.exists(test_file):
    #     result = manager.upload_with_fallback(test_file)
    #     print(f"上传结果: {result}")


if __name__ == "__main__":
    main()
