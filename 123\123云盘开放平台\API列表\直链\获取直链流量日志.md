# 获取直链流量日志

API：GET 域名 + /api/v1/direct-link/log

注：此接口需要开通开发者权益，并且仅限查询近三天的日志数据

## Header 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| Authorization | string | <font style="color:#000000;">必填</font> | 鉴权access_token |
| Platform | string | 必填 | 固定为:open_platform |


## QueryString 参数
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| pageNum | number | 必填 | 页数 |
| pageSize | number | 必填 | 分页大小 |
| startTime | string | 必填 | 开始时间，格式：2025-01-01 00:00:00 |
| endTime | string | 必填 | 结束时间，格式：2025-01-01 23:59:59 |


## 返回数据
| **名称** | **类型** | **是否必填** | **说明** |
| :---: | :---: | :---: | :---: |
| total | number | 必填 | 总数 |
| list | array | 必填 |  |
| uniqueID | string | 必填 | 唯一id |
| fileName | string | 必填 | 文件名 |
| fileSize | number | 必填 | 文件大小（字节） |
| filePath | string | 必填 | 文件路径 |
| directLinkURL | string | 必填 | 直链URL |
| fileSource | number | 必填 | 文件来源 1全部文件 2图床 |
| totalTraffic | number | 必填 | 消耗流量（字节） |


## 示例
**请求示例**

```shell
curl --location 'https://open-api.123pan.com/api/v1/direct-link/log?pageNum=1&pageSize=100&startTime=2025-03-04%2000%3A00%3A00&endTime=2025-03-05%2023%3A59%3A59' \
--header 'Content-Type: application/json' \
--header 'Platform: open_platform' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
```

```java
OkHttpClient client = new OkHttpClient().newBuilder()
.build();
MediaType mediaType = MediaType.parse("application/json");
RequestBody body = RequestBody.create(mediaType, "");
Request request = new Request.Builder()
.url("https://open-api.123pan.com/api/v1/direct-link/log?pageNum=1&pageSize=100&startTime=2025-03-04%2000%3A00%3A00&endTime=2025-03-05%2023%3A59%3A59")
.method("GET", body)
.addHeader("Content-Type", "application/json")
.addHeader("Platform", "open_platform")
.addHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)")
.build();
Response response = client.newCall(request).execute();
```

```javascript
var settings = {
  "url": "https://open-api.123pan.com/api/v1/direct-link/log?pageNum=1&pageSize=100&startTime=2025-03-04%2000%3A00%3A00&endTime=2025-03-05%2023%3A59%3A59",
  "method": "GET",
  "timeout": 0,
  "headers": {
    "Content-Type": "application/json",
    "Platform": "open_platform",
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)"
  },
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
```

```javascript
const axios = require('axios');

let config = {
  method: 'get',
  maxBodyLength: Infinity,
  url: 'https://open-api.123pan.com/api/v1/direct-link/log?pageNum=1&pageSize=100&startTime=2025-03-04%2000%3A00%3A00&endTime=2025-03-05%2023%3A59%3A59',
  headers: { 
    'Content-Type': 'application/json', 
    'Platform': 'open_platform', 
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
  }
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});

```

```python
import http.client
import json

conn = http.client.HTTPSConnection("open-api.123pan.com")
payload = ''
headers = {
    'Content-Type': 'application/json',
    'Platform': 'open_platform',
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJl...(过长省略)'
}
conn.request("GET", "/api/v1/direct-link/log?pageNum=1&pageSize=100&startTime=2025-03-04%252000%253A00%253A00&endTime=2025-03-05%252023%253A59%253A59", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))
```

**响应示例**

```json
{
  "code": 0,
  "message": "ok",
  "data": {
    "list": [
      {
        "uniqueID": "b23ce3be-2e06-4421-8c74-5b69bac99d69",
        "fileName": "Planet.Earth.III.S01E06.Extremes.2160p.iP.WEB-DL.AAC2.0.HLG.H.265-Q66.mkv",
        "fileSize": 2595421554,
        "filePath": "/测试图片/Planet.Earth.III.S01E06.Extremes.2160p.iP.WEB-DL.AAC2.0.HLG.H.265-Q66.mkv",
        "directLinkURL": "http://vipdev.123pan.com/1814435789/测试图片/Planet.Earth.III.S01E06.Extremes.2160p.iP.WEB-DL.AAC2.0.HLG.H.265-Q66.mkv",
        "fileSource": 1,
        "totalTraffic": 2859077463
      },
      {
        "uniqueID": "94bf5252-7d66-4d97-98e9-5b358eef7560",
        "fileName": "video.mp4",
        "fileSize": 729699,
        "filePath": "/测试图片/video.mp4",
        "directLinkURL": "http://vipdev.123pan.com/1814435789/测试图片/video.mp4",
        "fileSource": 1,
        "totalTraffic": 2971802
      }
    ],
    "total": 2
  },
  "x-traceID": ""
}
```



> 更新: 2025-03-17 19:17:38  
> 原文: <https://123yunpan.yuque.com/org-wiki-123yunpan-muaork/cr6ced/agmqpmu0dm0iogc9>